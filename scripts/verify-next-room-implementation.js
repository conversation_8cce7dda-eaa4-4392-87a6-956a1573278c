#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify that the Next Room field implementation is working correctly
 * This script checks the API endpoints and form structure
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Next Room Implementation');
console.log('=' .repeat(50));

// Check 1: Verify form component has next_room field
console.log('\n1. Checking form component...');
const formPath = path.join(__dirname, '../src/admin/components/simple-individual-room-form.tsx');
const formContent = fs.readFileSync(formPath, 'utf8');

const hasNextRoomField = formContent.includes('next_room:');
const hasNextRoomLabel = formContent.includes('Next Room');
const hasCommentedLeftRight = formContent.includes('/* Commented out Left Room and Right Room fields');

console.log(`   ✓ Has next_room field in formData: ${hasNextRoomField ? '✅' : '❌'}`);
console.log(`   ✓ Has "Next Room" label: ${hasNextRoomLabel ? '✅' : '❌'}`);
console.log(`   ✓ Has commented out Left/Right Room fields: ${hasCommentedLeftRight ? '✅' : '❌'}`);

// Check 2: Verify API validation schemas
console.log('\n2. Checking API validation schemas...');
const apiPath = path.join(__dirname, '../src/api/admin/hotel-management/rooms/route.ts');
const apiContent = fs.readFileSync(apiPath, 'utf8');

const hasNextRoomValidation = apiContent.includes('next_room: z.string().optional()');
console.log(`   ✓ Has next_room validation in API: ${hasNextRoomValidation ? '✅' : '❌'}`);

// Check 3: Verify workflow includes next_room
console.log('\n3. Checking workflow...');
const workflowPath = path.join(__dirname, '../src/workflows/hotel-management/room/create-room.ts');
const workflowContent = fs.readFileSync(workflowPath, 'utf8');

const hasNextRoomInWorkflow = workflowContent.includes('next_room?: string;');
const hasNextRoomInMetadata = workflowContent.includes('next_room: input.next_room');
console.log(`   ✓ Has next_room in workflow input: ${hasNextRoomInWorkflow ? '✅' : '❌'}`);
console.log(`   ✓ Has next_room in metadata: ${hasNextRoomInMetadata ? '✅' : '❌'}`);

// Check 4: Verify TypeScript interfaces
console.log('\n4. Checking TypeScript interfaces...');
const typesPath = path.join(__dirname, '../src/admin/components/hotel/types/booking.ts');
const typesContent = fs.readFileSync(typesPath, 'utf8');

const hasNextRoomInTypes = typesContent.includes('next_room?: string;');
console.log(`   ✓ Has next_room in Room interface: ${hasNextRoomInTypes ? '✅' : '❌'}`);

// Summary
console.log('\n📊 Implementation Summary:');
const allChecks = [
  hasNextRoomField,
  hasNextRoomLabel,
  hasCommentedLeftRight,
  hasNextRoomValidation,
  hasNextRoomInWorkflow,
  hasNextRoomInMetadata,
  hasNextRoomInTypes
];

const passedChecks = allChecks.filter(Boolean).length;
const totalChecks = allChecks.length;

console.log(`   Passed: ${passedChecks}/${totalChecks} checks`);

if (passedChecks === totalChecks) {
  console.log('   🎉 All checks passed! Implementation looks good.');
} else {
  console.log('   ⚠️  Some checks failed. Please review the implementation.');
}

// Check 5: Test API endpoint structure
console.log('\n5. Testing API endpoint availability...');
const BASE_URL = 'http://localhost:9000';

async function testAPI() {
  try {
    const response = await fetch(`${BASE_URL}/health`);
    if (response.ok) {
      console.log('   ✅ Server is running and accessible');
      
      // Test the room creation endpoint structure
      console.log('   📝 Room creation endpoint: POST /admin/direct-rooms');
      console.log('   📝 Expected next_room field in request body');
      
    } else {
      console.log('   ❌ Server health check failed');
    }
  } catch (error) {
    console.log('   ❌ Server is not accessible:', error.message);
  }
}

// Run async test if fetch is available
if (typeof fetch !== 'undefined') {
  testAPI();
} else {
  console.log('   ℹ️  Fetch not available in this environment, skipping API test');
}

console.log('\n✨ Verification complete!');
console.log('\n📋 Next steps to test manually:');
console.log('   1. Open http://localhost:9000/app/hotel-management/hotels/01K1SVK8C7GYSFTNKQQ5N1GHKP/room-configs/prod_01K1SVQWGXT8ZTVA2QAVMZXK4S/rooms');
console.log('   2. Click "Add New Room" button');
console.log('   3. Verify that "Next Room" field is visible');
console.log('   4. Verify that "Left Room" and "Right Room" fields are NOT visible');
console.log('   5. Fill in the form and submit to test API integration');
