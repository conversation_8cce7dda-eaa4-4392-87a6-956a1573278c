{"name": "@flinkk/hotel-management", "version": "0.0.4", "author": "Flinkk ", "license": "MIT", "files": [".medusa/server"], "exports": {"./package.json": "./package.json", "./workflows": "./.medusa/server/src/workflows/index.js", "./.medusa/server/src/modules/*": "./.medusa/server/src/modules/*/index.js", "./modules/*": "./.medusa/server/src/modules/*/index.js", "./providers/*": "./.medusa/server/src/providers/*/index.js", "./*": "./.medusa/server/src/*.js"}, "keywords": ["flink", "plugin"], "scripts": {"build": "medusa plugin:build", "dev": "medusa plugin:develop", "prepublishOnly": "medusa plugin:build", "publish": "medusa plugin:publish"}, "peerDependencies": {"@camped-ai/admin-sdk": "*", "@camped-ai/cli": "*", "@camped-ai/framework": "*", "@camped-ai/test-utils": "*", "@camped-ai/medusa": "*", "@camped-ai/ui": "*", "@camped-ai/icons": "*", "@mikro-orm/cli": "*", "@mikro-orm/core": "*", "@mikro-orm/knex": "*", "@mikro-orm/migrations": "*", "@mikro-orm/postgresql": "*", "awilix": "*", "pg": "*"}, "engines": {"node": ">=20"}, "dependencies": {"@flinkk/hotel-management": "file:.yalc/@flinkk/hotel-management"}, "yalcSig": "a4fe583d68446ad554d19727ca0bee8a"}