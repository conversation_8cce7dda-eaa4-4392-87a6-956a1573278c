import { createStep, createWorkflow, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { PACKAGE_LOOKUP_MODULE } from "src/modules/package-lookup";
import PackageLookupModuleService from "src/modules/package-lookup/service";

export type UpdatePackageLookupStepInput = {
  id: string;
  data: {
    package_name?: string;
    description?: string;
    is_active?: boolean;
  };
};

type UpdatePackageLookupWorkflowInput = UpdatePackageLookupStepInput;

const updatePackageLookupStep = createStep(
  "update-package-lookup-step",
  async (input: UpdatePackageLookupStepInput, { container }) => {
    const packageLookupService: PackageLookupModuleService = container.resolve(PACKAGE_LOOKUP_MODULE);
    
    const packageLookups = await packageLookupService.updatePackageLookups([{
      id: input.id,
      ...input.data,
    }]);
    const packageLookup = packageLookups[0];
    return new StepResponse(packageLookup);
  },
  async (prevUpdatedPackageLookup, { container }) => {
    if (!prevUpdatedPackageLookup) {
      return;
    }
    const packageLookupService: PackageLookupModuleService = container.resolve(PACKAGE_LOOKUP_MODULE);
    await packageLookupService.updatePackageLookups([{
      id: prevUpdatedPackageLookup.id,
      ...prevUpdatedPackageLookup,
    }]);
  }
);

export const UpdatePackageLookupWorkflow = createWorkflow(
  "update-package-lookup",
  function (input: UpdatePackageLookupWorkflowInput) {
    const packageLookup = updatePackageLookupStep(input);
    return packageLookup;
  }
);
