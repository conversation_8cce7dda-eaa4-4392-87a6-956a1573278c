import { createStep, createWorkflow, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { PACKAGE_LOOKUP_MODULE } from "src/modules/package-lookup";
import PackageLookupModuleService from "src/modules/package-lookup/service";

export type CreatePackageLookupStepInput = {
  package_name: string;
  description?: string;
  is_active?: boolean;
};

type CreatePackageLookupWorkflowInput = CreatePackageLookupStepInput;

const createPackageLookupStep = createStep(
  "create-package-lookup-step",
  async (input: CreatePackageLookupStepInput, { container }) => {
    const packageLookupService: PackageLookupModuleService = container.resolve(PACKAGE_LOOKUP_MODULE);
    
    const packageLookups = await packageLookupService.createPackageLookups([{
      package_name: input.package_name,
      description: input.description || null,
      is_active: input.is_active ?? true,
    }]);
    const packageLookup = packageLookups[0];
    
    return new StepResponse(packageLookup);
  },
  async (packageLookup, { container }) => {
    if (!packageLookup) {
      return;
    }
    const packageLookupService: PackageLookupModuleService = container.resolve(PACKAGE_LOOKUP_MODULE);
    await packageLookupService.deletePackageLookups([packageLookup.id]);
  }
);

export const CreatePackageLookupWorkflow = createWorkflow(
  "create-package-lookup",
  function (input: CreatePackageLookupWorkflowInput) {
    const packageLookup = createPackageLookupStep(input);
    return packageLookup;
  }
);
