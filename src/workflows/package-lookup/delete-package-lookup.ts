import { createStep, createWorkflow, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { PACKAGE_LOOKUP_MODULE } from "src/modules/package-lookup";
import PackageLookupModuleService from "src/modules/package-lookup/service";

export type DeletePackageLookupStepInput = {
  ids: string[];
};

type DeletePackageLookupWorkflowInput = DeletePackageLookupStepInput;

const deletePackageLookupStep = createStep(
  "delete-package-lookup-step",
  async (input: DeletePackageLookupStepInput, { container }) => {
    const packageLookupService: PackageLookupModuleService = container.resolve(PACKAGE_LOOKUP_MODULE);
    await packageLookupService.deletePackageLookups(input.ids);
    return new StepResponse(void 0);
  }
);

export const DeletePackageLookupWorkflow = createWorkflow(
  "delete-package-lookup",
  function (input: DeletePackageLookupWorkflowInput) {
    const result = deletePackageLookupStep(input);
    return result;
  }
);
