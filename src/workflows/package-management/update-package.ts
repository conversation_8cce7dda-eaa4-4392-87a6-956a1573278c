import { createStep, createWorkflow, StepResponse, WorkflowResponse } from "@camped-ai/framework/workflows-sdk";
import { PACKAGE_MANAGEMENT_MODULE } from "src/modules/package-management";
import PackageManagementModuleService from "src/modules/package-management/service";

export type UpdatePackageStepInput = {
  id: string;
  package_lookup_id?: string;
  description?: string;
  valid_from?: string;
  valid_to?: string;
  destination_ids?: string[];
};

type UpdatePackageWorkflowInput = UpdatePackageStepInput;

const validateUpdatePackageInputStep = createStep(
  "validate-update-package-input",
  async (input: UpdatePackageStepInput) => {
    if (!input.id) {
      throw new Error("Package ID is required");
    }
    return new StepResponse(input);
  }
);

const getExistingPackageStep = createStep(
  "get-existing-package",
  async (input: UpdatePackageStepInput, { container }) => {
    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);
    
    // Get the existing package with its destinations
    const packages = await packageManagementService.listPackages({
      id: [input.id],
    }, {
      relations: ["package_destinations"],
    });

    if (!packages || packages.length === 0) {
      throw new Error(`Package with ID ${input.id} not found`);
    }

    return new StepResponse(packages[0]);
  }
);

const updatePackageStep = createStep(
  "update-package",
  async (input: UpdatePackageStepInput, { container }) => {
    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);
    
    console.log(`📝 Updating package: ${input.id}`);

    // Prepare update data (only include fields that are provided)
    const updateData: any = {};
    if (input.package_lookup_id !== undefined) updateData.package_lookup_id = input.package_lookup_id;
    if (input.description !== undefined) updateData.description = input.description;
    if (input.valid_from !== undefined) updateData.valid_from = input.valid_from;
    if (input.valid_to !== undefined) updateData.valid_to = input.valid_to;

    // Update the package
    const updatedPackages = await packageManagementService.updatePackages([{
      id: input.id,
      ...updateData,
    }]);

    console.log(`✅ Package updated:`, updatedPackages[0]);

    return new StepResponse(updatedPackages[0]);
  }
);

const updatePackageDestinationsStep = createStep(
  "update-package-destinations",
  async (input: UpdatePackageStepInput, { container }) => {
    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);
    
    // Only update destinations if destination_ids is provided
    if (!input.destination_ids) {
      console.log("🔄 No destination updates requested");
      return new StepResponse({ message: "No destination updates" });
    }

    console.log(`🔄 Updating package destinations for package: ${input.id}`);

    // Get existing destinations
    const existingDestinations = await packageManagementService.listPackageDestinations({
      package_id: input.id,
    });

    // Delete existing destinations
    if (existingDestinations && existingDestinations.length > 0) {
      console.log(`🗑️ Removing ${existingDestinations.length} existing destinations`);
      await packageManagementService.deletePackageDestinations(
        existingDestinations.map(dest => dest.id)
      );
    }

    // Create new destinations
    if (input.destination_ids.length > 0) {
      console.log(`➕ Adding ${input.destination_ids.length} new destinations`);
      const newDestinations = input.destination_ids.map(destinationId => ({
        package_id: input.id,
        destination_id: destinationId,
      }));

      const createdDestinations = await packageManagementService.createPackageDestinations(newDestinations);
      console.log(`✅ Created ${createdDestinations.length} package destinations`);
      
      return new StepResponse(createdDestinations);
    }

    return new StepResponse({ message: "No destinations to add" });
  }
);

export const UpdatePackageWorkflow = createWorkflow(
  "update-package",
  (input: UpdatePackageWorkflowInput) => {
    // Step 1: Validate input
    const validatedInput = validateUpdatePackageInputStep(input);
    
    // Step 2: Get existing package for verification
    const existingPackage = getExistingPackageStep(validatedInput);
    
    // Step 3: Update the package
    const updatedPackage = updatePackageStep(validatedInput);
    
    // Step 4: Update package destinations
    const updatedDestinations = updatePackageDestinationsStep(validatedInput);
    
    return new WorkflowResponse({
      package: updatedPackage,
      destinations: updatedDestinations,
    });
  }
);
