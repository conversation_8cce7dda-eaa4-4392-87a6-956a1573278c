import { createStep, createWorkflow, StepResponse, WorkflowResponse } from "@camped-ai/framework/workflows-sdk";
import { PACKAGE_MANAGEMENT_MODULE } from "src/modules/package-management";
import PackageManagementModuleService from "src/modules/package-management/service";

export type DeletePackageStepInput = {
  id: string;
};

type DeletePackageWorkflowInput = DeletePackageStepInput;

const validateDeletePackageInputStep = createStep(
  "validate-delete-package-input",
  async (input: DeletePackageStepInput) => {
    if (!input.id) {
      throw new Error("Package ID is required");
    }
    return new StepResponse(input);
  }
);

const getPackageForDeletionStep = createStep(
  "get-package-for-deletion",
  async (input: DeletePackageStepInput, { container }) => {
    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);
    
    // Get the package with its destinations for verification
    const packages = await packageManagementService.listPackages({
      id: [input.id],
    }, {
      relations: ["package_destinations"],
    });

    if (!packages || packages.length === 0) {
      throw new Error(`Package with ID ${input.id} not found`);
    }

    return new StepResponse(packages[0]);
  }
);

const deletePackageStep = createStep(
  "delete-package",
  async (input: DeletePackageStepInput, { container }) => {
    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);
    
    console.log(`🗑️ Deleting package: ${input.id}`);

    // First, delete all package destinations (due to foreign key constraints)
    const packageDestinations = await packageManagementService.listPackageDestinations({
      package_id: input.id,
    });

    if (packageDestinations && packageDestinations.length > 0) {
      console.log(`🗑️ Deleting ${packageDestinations.length} package destinations`);
      await packageManagementService.deletePackageDestinations(
        packageDestinations.map(dest => dest.id)
      );
    }

    // Then delete the package itself
    await packageManagementService.deletePackages([input.id]);

    console.log(`✅ Package ${input.id} deleted successfully`);

    return new StepResponse({ 
      id: input.id, 
      deleted: true,
      message: "Package deleted successfully" 
    });
  },
  async (originalPackage: any, { container }) => {
    // Compensation function - restore the package if needed
    if (!originalPackage) {
      return;
    }

    console.log("🔄 Rolling back package deletion:", originalPackage.id);
    
    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);
    
    try {
      // Restore the package (this would need to be implemented if using soft deletes)
      // For hard deletes, compensation is more complex and might not be feasible
      console.log("⚠️ Package deletion compensation not implemented for hard deletes");
    } catch (error) {
      console.error("Failed to compensate package deletion:", error);
    }
  }
);

export const DeletePackageWorkflow = createWorkflow(
  "delete-package",
  (input: DeletePackageWorkflowInput) => {
    // Step 1: Validate input
    const validatedInput = validateDeletePackageInputStep(input);
    
    // Step 2: Get package for verification
    const originalPackage = getPackageForDeletionStep(validatedInput);
    
    // Step 3: Delete the package and its destinations
    const result = deletePackageStep(validatedInput);
    
    return new WorkflowResponse(result);
  }
);
