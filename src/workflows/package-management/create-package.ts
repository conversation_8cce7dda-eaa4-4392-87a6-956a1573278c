import { createStep, createWorkflow, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { PACKAGE_MANAGEMENT_MODULE } from "src/modules/package-management";
import PackageManagementModuleService from "src/modules/package-management/service";

export type CreatePackageStepInput = {
  package_lookup_id: string;
  description: string;
  valid_from_date: string;
  valid_to_date: string;
  destination_ids: string[];
};

type CreatePackageWorkflowInput = CreatePackageStepInput;

const createPackageStep = createStep(
  "create-package-step",
  async (input: CreatePackageStepInput, { container }) => {
    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);

    console.log("🚀 Creating package with input:", input);

    // Create the package record
    const packageData = {
      package_lookup_id: input.package_lookup_id,
      description: input.description,
      valid_from_date: new Date(input.valid_from_date),
      valid_to_date: new Date(input.valid_to_date),
    };

    console.log("📦 Package data to create:", packageData);

    const packages = await packageManagementService.createPackages([packageData]);
    const packageRecord = packages[0];

    console.log("✅ Package created:", packageRecord);

    // Create package destinations - one record for each selected destination
    const packageDestinationData = input.destination_ids.map(destinationId => ({
      package_id: packageRecord.id,
      destination_id: destinationId,
    }));

    console.log("🎯 Package destinations to create:", packageDestinationData);

    const packageDestinations = await packageManagementService.createPackageDestinations(packageDestinationData);

    console.log("✅ Package destinations created:", packageDestinations);

    return new StepResponse({
      package: packageRecord,
      destinations: packageDestinations,
    });
  },
  async (result, { container }) => {
    if (!result) {
      return;
    }

    console.log("🔄 Rolling back package creation:", result);

    const packageManagementService: PackageManagementModuleService = container.resolve(PACKAGE_MANAGEMENT_MODULE);

    // Delete package destinations first (due to foreign key constraints)
    if (result.destinations && result.destinations.length > 0) {
      console.log("🗑️ Deleting package destinations:", result.destinations.map(dest => dest.id));
      await packageManagementService.deletePackageDestinations(
        result.destinations.map(dest => dest.id)
      );
    }

    // Delete the package
    if (result.package) {
      console.log("🗑️ Deleting package:", result.package.id);
      await packageManagementService.deletePackages([result.package.id]);
    }
  }
);

export const createPackageWorkflow = createWorkflow(
  "create-package-workflow",
  (input: CreatePackageWorkflowInput) => {
    return createPackageStep(input);
  }
);
