import {
  createStep,
  createWorkflow,
  StepResponse,
  WorkflowResponse,
} from "@camped-ai/framework/workflows-sdk";
import { MedusaError, Modu<PERSON> } from "@camped-ai/framework/utils";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "src/modules/supplier-products-services";
import SupplierProductsServicesModuleService from "src/modules/supplier-products-services/service";
import { CreateProductServiceInput, ProductServiceOutput } from "src/modules/supplier-products-services/types";
import { createProductServiceSyncService } from "../../utils/service-registration";

export type CreateProductServiceStepInput = CreateProductServiceInput;

type CreateProductServiceWorkflowInput = CreateProductServiceStepInput;

export const validateProductServiceInputStep = createStep(
  "validate-product-service-input",
  async (input: CreateProductServiceStepInput) => {
    // Note: Name is optional - if provided, it will be used; if not, it will be auto-generated
    // Validate required fields
    // Note: Type field has been removed from the interface

    if (!input.category_id || input.category_id.trim().length === 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Category ID is required"
      );
    }

    if (!input.unit_type_id || input.unit_type_id.trim().length === 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Unit Type ID is required"
      );
    }

    // Validate base_cost if provided
    if (input.base_cost !== undefined && input.base_cost < 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Base cost must be non-negative"
      );
    }

    // Validate status if provided
    if (input.status && !["active", "inactive"].includes(input.status)) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Status must be either 'active' or 'inactive'"
      );
    }

    // Validate tag_ids if provided
    if (input.tag_ids && !Array.isArray(input.tag_ids)) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Tag IDs must be an array"
      );
    }

    // Validate custom_fields if provided (should be an object/record)
    if (input.custom_fields && typeof input.custom_fields !== 'object') {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Custom fields must be an object"
      );
    }

    // Validate service_level if provided
    if (input.service_level && !["hotel", "destination"].includes(input.service_level)) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Service level must be either 'hotel' or 'destination'"
      );
    }

    // Validate hotel_id and destination_id based on service_level
    // Note: hotel_id and destination_id are optional - services can be created without specific locations
    const serviceLevel = input.service_level || "hotel"; // Default to hotel if not provided

    if (serviceLevel === "hotel" && input.hotel_id) {
      // If hotel_id is provided, validate it's not empty
      if (input.hotel_id.trim().length === 0) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Hotel ID cannot be empty when provided"
        );
      }
    }

    if (serviceLevel === "destination" && input.destination_id) {
      // If destination_id is provided, validate it's not empty
      if (input.destination_id.trim().length === 0) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Destination ID cannot be empty when provided"
        );
      }
    }

    return new StepResponse(input);
  }
);

export const createProductServiceStep = createStep(
  "create-product-service",
  async (input: CreateProductServiceStepInput, { container }) => {
    console.log("🔄 Workflow received input:", JSON.stringify(input, null, 2));

    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    // Get the query service from the workflow container
    const queryService = container.resolve("query");

    const productService = await supplierProductsServicesService.createProductService(input, queryService);

    return new StepResponse(productService, productService.id);
  },
  async (productServiceId: string, { container }) => {
    // Compensation: Delete the created product/service if workflow fails
    const supplierProductsServicesService: SupplierProductsServicesModuleService =
      container.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);

    try {
      await supplierProductsServicesService.deleteProductServices([productServiceId]);
    } catch (error) {
      // Log error but don't throw to avoid masking the original error
      console.error(`Failed to compensate product/service creation: ${error.message}`);
    }
  }
);

export const syncProductServiceToAddOnStep = createStep(
  "sync-product-service-to-addon",
  async (productService: ProductServiceOutput, { container }) => {
    try {
      console.log(`🔄 Starting reverse mapping sync for product service ${productService.id}`);

      // Get the sync service with proper service registration
      const syncService = createProductServiceSyncService(container);

      // Perform the sync which creates product/variant and updates product_service with IDs
      const syncResult = await syncService.syncProductServiceToAddOn(productService.id);

      console.log(`✅ Reverse mapping completed for product service ${productService.id}:`, {
        product_id: syncResult.product.id,
        product_variant_id: syncResult.variant.id
      });

      return new StepResponse(productService);
    } catch (error) {
      console.error(`❌ Failed to perform reverse mapping for product service ${productService.id}:`, error);
      // Don't throw error to prevent breaking the workflow - the product service was created successfully
      // The reverse mapping can be done later via the manual sync or event system
      return new StepResponse(productService);
    }
  }
);

export const emitProductServiceCreatedEventStep = createStep(
  "emit-product-service-created-event",
  async (productService: ProductServiceOutput, { container }) => {
    try {
      const eventModuleService = container.resolve(Modules.EVENT_BUS);

      await eventModuleService.emit({
        name: "product-service.created",
        data: {
          id: productService.id,
          product_service: productService,
        },
      });

      console.log(`✅ Emitted product-service.created event for ${productService.id}`);
      return new StepResponse(productService);
    } catch (error) {
      console.error(`❌ Failed to emit product-service.created event:`, error);
      // Don't throw error to prevent breaking the workflow
      return new StepResponse(productService);
    }
  }
);

export const CreateProductServiceWorkflow = createWorkflow(
  "create-product-service",
  (input: CreateProductServiceWorkflowInput) => {
    // Step 1: Validate input
    const validatedInput = validateProductServiceInputStep(input);
   

    // Step 2: Create the product/service
    const productService = createProductServiceStep(validatedInput);

    // Step 3: Perform reverse mapping sync (create product/variant and update product_service)
    const productServiceWithSync = syncProductServiceToAddOnStep(productService);

    // Step 4: Emit created event for additional processing
    const productServiceWithEvent = emitProductServiceCreatedEventStep(productService);

    return new WorkflowResponse(productServiceWithEvent);
  }
);
