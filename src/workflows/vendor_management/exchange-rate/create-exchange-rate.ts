import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import SupplierModuleService from "src/modules/vendor_management/supplier-service";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type CreateExchangeRateStepInput = {
  date?: string; // Legacy field - optional for backward compatibility
  valid_from: string;
  valid_to: string;
  base_currency: string;
  selling_currency: string;
  exchange_rate: number;
  metadata?: Record<string, any>;
};

type CreateExchangeRateWorkflowInput = CreateExchangeRateStepInput;

const createExchangeRateStep = createStep(
  "create-exchange-rate-step",
  async (input: CreateExchangeRateStepInput, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );

    console.log("Creating exchange rate with input:", input);

    // Validate input
    if (!input.valid_from || !input.valid_to || !input.base_currency || !input.selling_currency || !input.exchange_rate) {
      throw new Error("Missing required fields: valid_from, valid_to, base_currency, selling_currency, exchange_rate");
    }

    // Validate exchange rate is positive
    if (input.exchange_rate <= 0) {
      throw new Error("Exchange rate must be a positive number");
    }

    // Create the exchange rate
    const exchangeRate = await supplierModuleService.createExchangeRate({
      date: input.date, // Keep for backward compatibility
      valid_from: input.valid_from,
      valid_to: input.valid_to,
      base_currency: input.base_currency.toUpperCase(),
      selling_currency: input.selling_currency.toUpperCase(),
      exchange_rate: input.exchange_rate,
      metadata: {
        ...input.metadata,
        created_at: new Date().toISOString(),
      },
    });

    console.log("Successfully created exchange rate:", exchangeRate);

    return new StepResponse(exchangeRate, exchangeRate.id);
  },
  async (exchangeRateId: string, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );
    // Rollback: delete the created exchange rate
    await supplierModuleService.deleteExchangeRate(exchangeRateId);
  }
);

export const CreateExchangeRateWorkflow = createWorkflow(
  "create-exchange-rate",
  (input: CreateExchangeRateWorkflowInput) => {
    // Create the exchange rate
    const exchangeRate = createExchangeRateStep(input);

    // Emit event for exchange rate creation
    emitEventStep({
      eventName: "exchange_rate.created",
      data: {
        exchange_rate: exchangeRate,
        date: input.date,
        valid_from: input.valid_from,
        valid_to: input.valid_to,
        base_currency: input.base_currency,
        selling_currency: input.selling_currency,
        exchange_rate: input.exchange_rate,
      },
    });

    return new WorkflowResponse(exchangeRate);
  }
);
