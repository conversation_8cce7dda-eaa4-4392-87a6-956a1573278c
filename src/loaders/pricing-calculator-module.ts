import { asFunction } from "awilix";
import { withClient } from "../utils/db";

/**
 * Pricing Calculator Module
 * Registers a Medusa-compatible "module" that applies the required DB changes on medusa db:migrate
 * by providing a migrations runner hook. This integrates with the existing loader pattern.
 *
 * This module:
 * - Renames product_service.base_cost -> product_service.gross_cost
 * - Adds pricing fields required by the calculator UI:
 *   currency, commission, net_cost, margin_rate, selling_price_cost_currency, selling_price_gbp
 * - Adds constraints and index on gross_cost
 *
 * The SQL is idempotent and safe to re-run.
 */
async function applyPricingCalculatorMigrations() {
  const sql = `
    -- 1) Rename base_cost -> gross_cost if present
    DO $$
    BEGIN
      IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
          AND table_name = 'product_service'
          AND column_name = 'base_cost'
      ) THEN
        ALTER TABLE "product_service" RENAME COLUMN "base_cost" TO "gross_cost";
      END IF;
    END$$;

    -- 2) Add required pricing columns
    ALTER TABLE "product_service"
      ADD COLUMN IF NOT EXISTS "currency" TEXT NOT NULL DEFAULT 'GBP',
      ADD COLUMN IF NOT EXISTS "commission" NUMERIC(5,4) NULL,
      ADD COLUMN IF NOT EXISTS "net_cost" NUMERIC(12,2) NULL,
      ADD COLUMN IF NOT EXISTS "margin_rate" NUMERIC(5,4) NULL,
      ADD COLUMN IF NOT EXISTS "selling_price_cost_currency" NUMERIC(12,2) NULL,
      ADD COLUMN IF NOT EXISTS "selling_price_gbp" NUMERIC(12,2) NULL;

    -- 3) Constraints: ranges and non-negativity
    DO $$
    BEGIN
      -- Commission between 0 and 1 (inclusive)
      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_product_service_commission_range'
      ) THEN
        ALTER TABLE "product_service"
          ADD CONSTRAINT "chk_product_service_commission_range"
          CHECK ("commission" IS NULL OR ("commission" >= 0 AND "commission" <= 1));
      END IF;

      -- Margin rate between 0 and 1 (exclusive upper bound)
      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_product_service_margin_rate_range'
      ) THEN
        ALTER TABLE "product_service"
          ADD CONSTRAINT "chk_product_service_margin_rate_range"
          CHECK ("margin_rate" IS NULL OR ("margin_rate" >= 0 AND "margin_rate" < 1));
      END IF;

      -- Non-negative monetary checks
      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_product_service_gross_cost_nonneg'
      ) THEN
        ALTER TABLE "product_service"
          ADD CONSTRAINT "chk_product_service_gross_cost_nonneg"
          CHECK ("gross_cost" IS NULL OR "gross_cost" >= 0);
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_product_service_net_cost_nonneg'
      ) THEN
        ALTER TABLE "product_service"
          ADD CONSTRAINT "chk_product_service_net_cost_nonneg"
          CHECK ("net_cost" IS NULL OR "net_cost" >= 0);
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_product_service_selling_price_cost_curr_nonneg'
      ) THEN
        ALTER TABLE "product_service"
          ADD CONSTRAINT "chk_product_service_selling_price_cost_curr_nonneg"
          CHECK ("selling_price_cost_currency" IS NULL OR "selling_price_cost_currency" >= 0);
      END IF;

      IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'chk_product_service_selling_price_gbp_nonneg'
      ) THEN
        ALTER TABLE "product_service"
          ADD CONSTRAINT "chk_product_service_selling_price_gbp_nonneg"
          CHECK ("selling_price_gbp" IS NULL OR "selling_price_gbp" >= 0);
      END IF;
    END$$;

    -- 4) Replace index on base_cost with index on gross_cost
    DO $$
    BEGIN
      IF EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'public'
          AND indexname = 'IDX_product_service_base_cost'
      ) THEN
        DROP INDEX IF EXISTS "IDX_product_service_base_cost";
      END IF;
    END$$;

    CREATE INDEX IF NOT EXISTS "IDX_product_service_gross_cost"
    ON "product_service" ("gross_cost")
    WHERE "deleted_at" IS NULL AND "gross_cost" IS NOT NULL;
  `;

  await withClient(async (client) => {
    await client.query(sql);
  });
}

export default function pricingCalculatorModuleLoader({ container }: { container: any }) {
  // Register a hook that Medusa executes during db:migrate lifecycle if present in loaders.
  // Many Medusa setups execute loader files which can register tasks; we bind this function
  // so it can be invoked by a migration orchestrator in loaders/index.ts.
  container.register({
    pricingCalculatorMigrations: asFunction(() => ({
      run: async () => {
        try {
          console.log("🛠️ Applying Pricing Calculator DB changes...");
          await applyPricingCalculatorMigrations();
          console.log("✅ Pricing Calculator DB changes applied (idempotent).");
        } catch (e: any) {
          console.error("❌ Failed to apply Pricing Calculator DB changes:", e?.message || e);
          throw e;
        }
      },
    }))
      .singleton(),
  });
}
