#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create the order_invoice table for storing invoice data
 * related to orders, with integration to external providers like Xero.
 */

import { withClient } from "../utils/db";

async function createOrderInvoiceTable() {
  console.log('🚀 Creating Order Invoice Table');
  console.log('=' .repeat(50));

  try {
    await withClient(async (client) => {
      // Check if table already exists
      console.log('🔍 Checking if order_invoice table exists...');
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'order_invoice'
        );
      `);

      if (tableExists.rows[0].exists) {
        console.log('⚠️ order_invoice table already exists. Skipping creation.');
        return;
      }

      // Create order_invoice table
      console.log('📊 Creating order_invoice table...');
      await client.query(`
        CREATE TABLE IF NOT EXISTS "order_invoice" (
          "id" text NOT NULL,
          "order_id" text NOT NULL,
          "metadata" jsonb NULL,
          "provider" varchar(255) NOT NULL DEFAULT 'xero',
          "reference_id" varchar(255) NULL,
          "created_at" timestamptz NOT NULL DEFAULT now(),
          "updated_at" timestamptz NOT NULL DEFAULT now(),
          "deleted_at" timestamptz NULL,
          CONSTRAINT "order_invoice_pkey" PRIMARY KEY ("id")
        );
      `);
      console.log('✅ order_invoice table created');

      // Create indexes for performance
      console.log('📊 Creating indexes for order_invoice...');
      
      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_order_invoice_order_id" 
        ON "order_invoice" ("order_id") 
        WHERE "deleted_at" IS NULL;
      `);
      console.log('✅ Index IDX_order_invoice_order_id created');

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_order_invoice_reference_id" 
        ON "order_invoice" ("reference_id") 
        WHERE "deleted_at" IS NULL AND "reference_id" IS NOT NULL;
      `);
      console.log('✅ Index IDX_order_invoice_reference_id created');

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_order_invoice_provider" 
        ON "order_invoice" ("provider") 
        WHERE "deleted_at" IS NULL;
      `);
      console.log('✅ Index IDX_order_invoice_provider created');

      await client.query(`
        CREATE INDEX IF NOT EXISTS "IDX_order_invoice_deleted_at" 
        ON "order_invoice" ("deleted_at");
      `);
      console.log('✅ Index IDX_order_invoice_deleted_at created');

      // Add foreign key constraint linking order_id to the orders table
      console.log('🔗 Adding foreign key constraint...');
      try {
        await client.query(`
          ALTER TABLE "order_invoice" 
          ADD CONSTRAINT "FK_order_invoice_order_id" 
          FOREIGN KEY ("order_id") REFERENCES "order" ("id") 
          ON DELETE CASCADE ON UPDATE CASCADE;
        `);
        console.log('✅ Foreign key constraint FK_order_invoice_order_id created');
      } catch (fkError) {
        console.log('⚠️ Foreign key constraint may already exist or order table not found:', fkError.message);
      }

      console.log('');
      console.log('🎉 Order Invoice Table Creation Complete!');
      console.log('');
      console.log('📋 Summary:');
      console.log('  ✅ Table: order_invoice');
      console.log('  ✅ Columns: id, order_id, metadata, provider, reference_id, created_at, updated_at, deleted_at');
      console.log('  ✅ Indexes: order_id, reference_id, provider, deleted_at');
      console.log('  ✅ Foreign Key: order_id -> order.id');
      console.log('');
    });
  } catch (error) {
    console.error('❌ Error creating order_invoice table:', error);
    process.exit(1);
  }
}

// Export as default function for medusa exec
export default createOrderInvoiceTable;
