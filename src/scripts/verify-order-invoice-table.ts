#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify the order_invoice table was created correctly
 * with all specified columns, indexes, and foreign key constraints.
 */

import { withClient } from "../utils/db";

async function verifyOrderInvoiceTable() {
  console.log('🔍 Verifying Order Invoice Table');
  console.log('=' .repeat(50));

  try {
    await withClient(async (client) => {
      // 1. Check if table exists
      console.log('1️⃣ Checking if order_invoice table exists...');
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'order_invoice'
        );
      `);

      if (!tableExists.rows[0].exists) {
        console.log('❌ order_invoice table does not exist!');
        return;
      }
      console.log('✅ order_invoice table exists');

      // 2. Check table columns
      console.log('\n2️⃣ Checking table columns...');
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'order_invoice'
        ORDER BY ordinal_position;
      `);

      console.log('📋 Table columns:');
      columns.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}) ${col.column_default ? `default: ${col.column_default}` : ''}`);
      });

      // Verify required columns exist
      const requiredColumns = ['id', 'order_id', 'metadata', 'provider', 'reference_id', 'created_at', 'updated_at', 'deleted_at'];
      const existingColumns = columns.rows.map(col => col.column_name);
      const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
      
      if (missingColumns.length > 0) {
        console.log(`❌ Missing columns: ${missingColumns.join(', ')}`);
      } else {
        console.log('✅ All required columns present');
      }

      // 3. Check indexes
      console.log('\n3️⃣ Checking indexes...');
      const indexes = await client.query(`
        SELECT indexname, indexdef
        FROM pg_indexes 
        WHERE tablename = 'order_invoice'
        AND schemaname = 'public'
        ORDER BY indexname;
      `);

      console.log('📋 Table indexes:');
      indexes.rows.forEach(idx => {
        console.log(`  - ${idx.indexname}`);
        console.log(`    ${idx.indexdef}`);
      });

      // Verify required indexes exist
      const requiredIndexes = [
        'IDX_order_invoice_order_id',
        'IDX_order_invoice_reference_id', 
        'IDX_order_invoice_provider',
        'IDX_order_invoice_deleted_at'
      ];
      const existingIndexes = indexes.rows.map(idx => idx.indexname);
      const missingIndexes = requiredIndexes.filter(idx => !existingIndexes.includes(idx));
      
      if (missingIndexes.length > 0) {
        console.log(`❌ Missing indexes: ${missingIndexes.join(', ')}`);
      } else {
        console.log('✅ All required indexes present');
      }

      // 4. Check foreign key constraints
      console.log('\n4️⃣ Checking foreign key constraints...');
      const foreignKeys = await client.query(`
        SELECT 
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name,
          rc.update_rule,
          rc.delete_rule
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        JOIN information_schema.referential_constraints AS rc
          ON tc.constraint_name = rc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_name = 'order_invoice';
      `);

      console.log('📋 Foreign key constraints:');
      foreignKeys.rows.forEach(fk => {
        console.log(`  - ${fk.constraint_name}:`);
        console.log(`    ${fk.table_name}.${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
        console.log(`    ON UPDATE ${fk.update_rule}, ON DELETE ${fk.delete_rule}`);
      });

      // Verify required foreign key exists
      const expectedForeignKey = 'FK_order_invoice_order_id';
      const existingForeignKeys = foreignKeys.rows.map(fk => fk.constraint_name);
      
      if (!existingForeignKeys.includes(expectedForeignKey)) {
        console.log(`❌ Missing foreign key constraint: ${expectedForeignKey}`);
      } else {
        console.log('✅ Required foreign key constraint present');
      }

      // 5. Test basic operations
      console.log('\n5️⃣ Testing basic operations...');
      
      // Test that we can insert a record (we'll roll it back)
      await client.query('BEGIN');
      try {
        await client.query(`
          INSERT INTO order_invoice (id, order_id, provider, reference_id, metadata)
          VALUES ('test_invoice_123', 'test_order_123', 'xero', 'xero_ref_123', '{"test": true}')
        `);
        console.log('✅ Insert operation successful');
        
        // Test that we can query the record
        const testRecord = await client.query(`
          SELECT * FROM order_invoice WHERE id = 'test_invoice_123'
        `);
        console.log('✅ Select operation successful');
        console.log(`   Record found: ${testRecord.rows.length > 0 ? 'Yes' : 'No'}`);
        
      } catch (error) {
        console.log('❌ Basic operations failed:', error.message);
      } finally {
        await client.query('ROLLBACK');
        console.log('✅ Test transaction rolled back');
      }

      console.log('\n🎉 Order Invoice Table Verification Complete!');
      console.log('\n📊 Summary:');
      console.log(`  ✅ Table exists: ${tableExists.rows[0].exists ? 'Yes' : 'No'}`);
      console.log(`  ✅ Columns: ${existingColumns.length}/${requiredColumns.length} required columns present`);
      console.log(`  ✅ Indexes: ${existingIndexes.length - 1}/${requiredIndexes.length} required indexes present`); // -1 for primary key index
      console.log(`  ✅ Foreign Keys: ${existingForeignKeys.length}/1 required foreign key present`);
      console.log('  ✅ Basic operations: Working');
    });
  } catch (error) {
    console.error('❌ Error verifying order_invoice table:', error);
    process.exit(1);
  }
}

// Export as default function for medusa exec
export default verifyOrderInvoiceTable;
