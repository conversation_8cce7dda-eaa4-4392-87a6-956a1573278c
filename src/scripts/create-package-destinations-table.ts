#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create package_destinations table
 */

import { withClient } from "../utils/db";

async function createPackageDestinationsTable() {
  console.log('🔧 Creating package_destinations table');
  console.log('=' .repeat(60));

  try {
    await withClient(async (client) => {
      // Check if package_destinations table exists
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'package_destinations'
        );
      `);

      if (tableExists.rows[0].exists) {
        console.log('✅ package_destinations table already exists');
        return;
      }

      console.log('📝 Creating package_destinations table...');
      
      // Create the table
      await client.query(`
        CREATE TABLE "package_destinations" (
          "id" text NOT NULL,
          "package_id" text NOT NULL,
          "destination_id" text NOT NULL,
          "created_at" timestamp with time zone NOT NULL DEFAULT now(),
          "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
          "deleted_at" timestamp with time zone,
          CONSTRAINT "package_destinations_pkey" PRIMARY KEY ("id")
        );
      `);

      // Create indexes for better performance
      await client.query(`
        CREATE INDEX "IDX_package_destinations_package_id" 
        ON "package_destinations" ("package_id") 
        WHERE "deleted_at" IS NULL;
      `);

      await client.query(`
        CREATE INDEX "IDX_package_destinations_destination_id" 
        ON "package_destinations" ("destination_id") 
        WHERE "deleted_at" IS NULL;
      `);

      console.log('✅ package_destinations table created successfully');
      console.log('✅ Indexes created for package_destinations table');
      
      // Show current table structure
      const tableStructure = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'package_destinations'
        ORDER BY ordinal_position;
      `);

      console.log('\n📋 package_destinations table structure:');
      tableStructure.rows.forEach(row => {
        console.log(`  - ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });

    });
  } catch (error) {
    console.error('❌ Error creating package_destinations table:', error);
    process.exit(1);
  }
}

// Run the script
createPackageDestinationsTable();
