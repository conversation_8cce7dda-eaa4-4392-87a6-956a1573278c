import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { createInventoryWithDateLevelsWorkflow } from "../workflows/create-inventory-with-date-levels";
import { addDateLevelsToInventoryWorkflow } from "../workflows/add-date-levels-to-inventory";
import { IProductModuleService } from "@camped-ai/framework/types";


// Dynamic Field Schema Types
export interface DynamicFieldSchema {
  label: string;
  key: string;
  type:
    | "text"
    | "number"
    | "dropdown"
    | "multi-select"
    | "date"
    | "time"
    | "time-range"
    | "date-range"
    | "boolean"
    | "number-range"
    | "hotels"
    | "destinations"
    | "addons";
  options?: string[];
  required: boolean;
  used_in_filtering?: boolean;
  used_in_supplier_offering?: boolean;
  used_in_product?: boolean;
  used_in_product_services?: boolean; // New flag for product and services usage
  used_in_uniqueness?: boolean; // New flag specifically for uniqueness validation
  locked_in_offerings?: boolean;
  order?: number; // Optional field ordering for product name generation
  field_context?: "supplier" | "customer"; // Context filtering for fields
}

export interface CategoryOutput {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  dynamic_field_schema?: DynamicFieldSchema[];
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface UnitTypeOutput {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface TagOutput {
  id: string;
  name: string;
  color?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface ProductServiceSupplierOutput {
  id: string;
  product_service_id: string;
  supplier_id: string;
  cost: number;
  currency_code: string;
  availability: string;
  max_capacity?: number;
  season?: string;
  valid_from?: Date;
  valid_until?: Date;
  notes?: string;
  lead_time_days?: number;
  minimum_order?: number;
  is_active: boolean;
  is_preferred: boolean;
  created_by?: string;
  updated_by?: string;
  created_at: Date;
  updated_at: Date;
  // Populated supplier information
  supplier?: {
    id: string;
    name: string;
    type: string;
    status: string;
    primary_contact_name?: string;
    primary_contact_email?: string;
  };
}
// Output Types
export interface ProductServiceOutput {
  id: string;
  name: string;
  description?: string;
  base_cost?: number; // Legacy field for backward compatibility
  custom_fields?: Record<string, any>; // JSON object for dynamic field values
  status: "active" | "inactive";
  category_id: string;
  unit_type_id: string;
  service_level: "hotel" | "destination";
  hotel_id?: string; // JSON array of hotel IDs for hotel-level services
  destination_id?: string; // JSON array of destination IDs for destination-level services
  category?: CategoryOutput;
  unit_type?: UnitTypeOutput;
  tags?: TagOutput[];
  suppliers?: ProductServiceSupplierOutput[];
  created_at: Date;
  updated_at: Date;
  product_id?: string | null; // Foreign key to product table (populated during sync)
  product_variant_id?: string | null; // Foreign key to product_variant table (populated during sync)

  // Pricing Calculator Fields (7 required fields)
  gross_cost?: number;
  cost_currency?: string;
  commission?: number; // Stored as fraction 0-1
  net_cost?: number;
  margin_rate?: number; // Stored as fraction 0-1
  selling_price_cost_currency?: number;
  selling_price_gbp?: number;
}
/**
 * Extract customer custom fields from product service
 */
async function extractCustomerFields(productServiceId: string, _container: any): Promise<any[]> {
  try {
    // This would typically query your custom fields or metafield definitions
    // For now, return empty array - you can implement this based on your needs
    return [];
  } catch (error) {
    console.warn(`Could not extract customer fields for product service ${productServiceId}:`, error);
    return [];
  }
}

/**
 * Create product and variant from product service data
 */
async function createProductAndVariantFromProductService(
  productService: ProductServiceOutput,
  container: any
): Promise<{ product: any; variant: any }> {
  
  
  try {
    const productModuleService: IProductModuleService =container.resolve(
          Modules.PRODUCT
        );
    // Generate the required IDs with prefixes
    const productId = `prod_addon_${productService.id}`;
    const variantId = `variant_addon_${productService.id}`;

    // Extract customer custom fields from the product service
    const customerFields = await extractCustomerFields(productService.id, container);

    // Create metadata JSON with all required fields and custom fields
    const metadata = {
      type: (productService as any).type,
      description: productService.description || "",
      category: (productService as any).category_id,
      unit_type_id: (productService as any).unit_type_id,
      add_on_service: true,
      // Store custom fields as JSON object directly
      custom_fields: (productService as any).custom_fields || {},
      // Store customer custom fields separately for booking forms
      customer_fields: customerFields,
      // Add pricing fields
      cost: (productService as any).base_cost || 0,
      cost_currency: "CHF",
      selling_margin: 0,
      selling_price_cost_currency: 0,
      selling_price: 0,
      selling_currency: "CHF",
      // Add service level and location fields
      service_level: (productService as any).service_level || "hotel",
      hotel_id: (productService as any).hotel_id || null,
      destination_id: (productService as any).destination_id || null,
    };

    // Check if product already exists
    let product: any;
    try {
      const existingProducts = await productModuleService.listProducts({
        id: [productId],
      });
      if (existingProducts.length > 0) {
        // Product exists, update it
        const productData = {
          title: (productService as any).name,
          subtitle: "",
          description: productService.description || "",
          status: (productService as any).status === "active" ? "published" as const : "draft" as const,
        };
        product = await productModuleService.updateProducts(productId, productData);
      } else {
        // Product doesn't exist, create it
        const productData = {
          id: productId,
          title: (productService as any).name,
          subtitle: "",
          description: productService.description || "",
          status: (productService as any).status === "active" ? "published" as const : "draft" as const,
        };
        product = await productModuleService.createProducts(productData);
      }
    } catch (error) {
      console.error(`Error handling product ${productId}:`, error);
      throw error;
    }

    // Check if variant already exists
    let variant: any;
    try {
      const existingVariants = await productModuleService.listProductVariants({
        id: [variantId],
      });
      if (existingVariants.length > 0) {
        // Variant exists, update it
        const variantData = {
          title: (productService as any).name,
          sku: productService.id, // Use raw product_service.id as SKU
          metadata: {
            ...metadata,
            status: (productService as any).status === "active" ? "Active" : "Inactive", // Add-ons page status
          },
        };
        variant = await productModuleService.updateProductVariants(variantId, variantData);
      } else {
        // Variant doesn't exist, create it
        const variantData = {
          id: variantId,
          title: (productService as any).name,
          sku: productService.id, // Use raw product_service.id as SKU
          product_id: productId,
          metadata: {
            ...metadata,
            status: (productService as any).status === "active" ? "Active" : "Inactive", // Add-ons page status
          },
          inventory_quantity: 1,
          manage_inventory: false,
        };
        const createdVariants = await productModuleService.createProductVariants([variantData]);
        variant = createdVariants[0];
      }
    } catch (error) {
      console.error(`Error handling variant ${variantId}:`, error);
      throw error;
    }

    return { product, variant };
  } catch (error) {
    console.error(
      `Failed to create/update product and variant from product service ${productService.id}:`,
      error
    );
    throw error;
  }
}

/**
 * Product Service to Product/Variant/Inventory Creator
 *
 * This subscriber handles the complete flow when a product service is created:
 * 1. Creates a Product from the product service data
 * 2. Creates a Product Variant linked to the product
 * 3. Updates the original product service with product_id and product_variant_id
 * 4. Creates an Inventory Item linked to the variant
 * 5. Generates Date Levels for date-based availability
 *
 * Event: product-service.created
 *
 * The subscriber is non-blocking - if any step fails, it logs the error but doesn't
 * throw to avoid breaking the original product service creation process.
 */
export default async function variantInventoryCreatorHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; product_service?: any }>) {
  const logger = container.resolve("logger");
  logger.info('new subscriber')

  try {
    const productServiceId = data.id;
    const productService = data.product_service;
    logger.info(productService)

    if (!productService) {
      logger.warn(`No product service data provided for ID: ${productServiceId}`);
      return;
    }

    logger.info(`Creating product, variant, and inventory for product service: ${productServiceId}`);

    // First, create the product and variant from the product service
    const { product, variant } = await createProductAndVariantFromProductService(
      productService,
      container
    );
    
    logger.info(`Created product ${product.id} and variant ${variant.id} for product service: ${productServiceId}`);

    // Update the product_service record with the synchronized product_id and product_variant_id
   

    // Extract configuration from metadata with sensible defaults
    const roomType = variant.metadata?.room_type || "standard";
    const defaultCapacity = variant.metadata?.default_capacity || 1;
    const checkInTime = variant.metadata?.check_in_time || "15:00";
    const checkOutTime = variant.metadata?.check_out_time || "11:00";
    logger.info(productService?.valid_from )
     logger.info(productService?.valid_to )
    
    // Get date range from metadata or use defaults
    const startDate = new Date(productService?.valid_from).toISOString().split('T')[0] || 
                     new Date().toISOString().split('T')[0];
    const endDate = new Date(productService.valid_to).toISOString().split('T')[0] || new Date(productService?.valid_from).toISOString().split('T')[0]
logger.info(startDate)
     logger.info(endDate )
    // Check if inventory item already exists for this variant
    const inventoryModuleService = container.resolve(Modules.INVENTORY) as any;
    let inventoryItemId: string | undefined;

    try {
      const existingInventory = await inventoryModuleService.listInventoryItems({
        sku: variant.sku,
      });

      if (existingInventory.length > 0) {
        inventoryItemId = existingInventory[0].id;
        logger.info(`Using existing inventory item: ${inventoryItemId}`);
      }
    } catch (error) {
      logger.warn(`Could not check existing inventory: ${error.message}`);
    }

    
    let dateLevels:any = [];

   
      // Generate daily availability for the specified period
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      const currentDate = new Date(start);
      while (currentDate < end) {
        const nextDate = new Date(currentDate);
        nextDate.setDate(nextDate.getDate() + 1);

        dateLevels.push({
          from_date: currentDate.toISOString().split('T')[0],
          to_date: nextDate.toISOString().split('T')[0],
          check_in_time: checkInTime,
          check_out_time: checkOutTime,
          available_quantity: defaultCapacity,
          status: "capacity",
          notes: `Auto-generated for ${roomType}`,
          metadata: {
            room_type: roomType,
            auto_generated: true,
            variant_id: variant.id,
          },
        });

        currentDate.setDate(currentDate.getDate() + 1);
        
        // Limit to prevent too many records (max 90 days if no end date specified)
        if (dateLevels.length >= 90 && !variant.metadata?.availability_end_date) {
          break;
        }
      }

      console.log('here',dateLevels)
    

    if (inventoryItemId && dateLevels.length > 0) {
      // Add date levels to existing inventory item
      await addDateLevelsToInventoryWorkflow(container).run({
        input: {
          inventory_item_id: inventoryItemId,
          product_variant_id: variant.id,
          date_levels: dateLevels,
        },
      });
      
      logger.info(`Added ${dateLevels.length} date levels to existing inventory item: ${inventoryItemId}`);
    } else if (dateLevels.length > 0) {
      // Create new inventory item with date levels
      const inventoryItemData = {
        sku: variant.sku,
        requires_shipping: false,
        metadata: {
          room_type: roomType,
          is_inventory: true,
          variant_id: variant.id,
          auto_created: true,
        },
      };

      const result = await createInventoryWithDateLevelsWorkflow(container).run({
        input: {
          inventory_item: inventoryItemData,
          product_variant_id: variant.id,
          date_levels: dateLevels,
        },
      });

      logger.info(`Created inventory item ${result.result.inventory_item.id} with ${dateLevels.length} date levels for variant: ${variant.id}`);
    } else {
      logger.info(`No date levels to create for variant: ${variant.id}`);
    }

  } catch (error) {
    logger.error(`Error creating inventory for product service ${data.id}:`, error);
    // Don't throw - we don't want to break the product service creation process
  }
}

export const config: SubscriberConfig = {
  event: "product-service.created",
  context: {
    subscriberId: "variant-inventory-creator",
  },
};


