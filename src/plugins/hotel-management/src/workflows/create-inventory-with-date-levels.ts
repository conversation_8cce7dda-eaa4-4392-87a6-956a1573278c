import {
  createWorkflow,
  WorkflowResponse,
  transform,
} from "@camped-ai/workflows-sdk";
import { createInventoryItemsStep } from "@camped-ai/medusa/core-flows";
import { createDateInventoryLevelStep } from "./steps/create-date-inventory-level";
import { date } from "zod";

export interface DateLevelInput {
  from_date: string;
  to_date: string;
  check_in_time?: string;
  check_out_time?: string;
  available_quantity: number;
  status?: "capacity" | "reserved" | "allocated" | "canceled" | "on_hold" | "maintenance";
  notes?: string;
  metadata?: Record<string, any>;
}

export interface CreateInventoryWithDateLevelsInput {
  inventory_item: {
    sku?: string;
    origin_country?: string;
    hs_code?: string;
    metadata?: Record<string, any>;
  };
  product_variant_id?: string;
  date_levels: DateLevelInput[];
}

export interface CreateInventoryWithDateLevelsOutput {
  inventory_item: {
    id: string;
    sku?: string;
    metadata?: Record<string, any>;
  };
  date_levels: Array<{
    id: string;
    inventory_item_id: string;
    from_date: string;
    to_date: string;
    available_quantity: number;
    status: string;
  }>;
}

export const createInventoryWithDateLevelsWorkflow = createWorkflow(
 { name:"create-inventory-with-date-levels",
    retentionTime: 86400, // 24 hours in seconds
    store: true,
 },
  (input: CreateInventoryWithDateLevelsInput) => {
    // Step 1: Create the inventory item
    const inventoryItem = createInventoryItemsStep([input.inventory_item]);

    // Step 2: Transform the inventory item result to get the ID
    const inventoryItemData = transform(
      { inventoryItem, productVariantId: input.product_variant_id },
      ({ inventoryItem, productVariantId }) => ({
        inventory_item_id: inventoryItem[0].id,
        product_variant_id: productVariantId,
        inventory_item: inventoryItem[0],
      })
    );

    // Step 3: Create date levels in parallel for better performance
    const dateLevelsInput = transform(
      { inventoryItemData, dateLevels: input.date_levels },
      ({ inventoryItemData, dateLevels }) => {
         dateLevels.map((dateLevel) => {
          console.log({dateLevel})
          return  createDateInventoryLevelStep({
          inventory_item_id: inventoryItemData.inventory_item_id,
          product_variant_id: inventoryItemData.product_variant_id,
          from_date: dateLevel.from_date,
          to_date: dateLevel.to_date,
          check_in_time: dateLevel.check_in_time,
          check_out_time: dateLevel.check_out_time,
          available_quantity: dateLevel.available_quantity,
          status: dateLevel.status || "capacity",
          notes: dateLevel.notes,
          metadata: dateLevel.metadata || {},
          })
        })});
     



    // No date levels to create
    return new WorkflowResponse({
      inventory_item: inventoryItemData.inventory_item,
      date_levels: [],
    });
  }
);
