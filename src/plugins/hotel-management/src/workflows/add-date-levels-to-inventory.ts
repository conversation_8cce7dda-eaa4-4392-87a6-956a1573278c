import {
  createWorkflow,
  WorkflowResponse,
} from "@camped-ai/workflows-sdk";
import { createDateInventoryLevelStep } from "./steps/create-date-inventory-level";

export interface DateLevelInput {
  from_date: string;
  to_date: string;
  check_in_time?: string;
  check_out_time?: string;
  available_quantity: number;
  status?: "capacity" | "reserved" | "allocated" | "canceled" | "on_hold" | "maintenance";
  notes?: string;
  metadata?: Record<string, any>;
}

export interface AddDateLevelsToInventoryInput {
  inventory_item_id: string;
  product_variant_id?: string;
  date_levels: DateLevelInput[];
}

export interface AddDateLevelsToInventoryOutput {
  inventory_item_id: string;
  date_levels: Array<{
    id: string;
    inventory_item_id: string;
    from_date: string;
    to_date: string;
    available_quantity: number;
    status: string;
  }>;
}

export const addDateLevelsToInventoryWorkflow = createWorkflow(
  "add-date-levels-to-inventory",
  (input: AddDateLevelsToInventoryInput) => {
    // For now, just create the first date level as a simple test
    // TODO: Implement proper parallel processing later
    const firstDateLevel = input.date_levels[0];

    if (!firstDateLevel) {
      return new WorkflowResponse({
        inventory_item_id: input.inventory_item_id,
        date_levels: [],
      });
    }

    const dateLevelInput = {
      inventory_item_id: input.inventory_item_id,
      product_variant_id: input.product_variant_id,
      from_date: firstDateLevel.from_date,
      to_date: firstDateLevel.to_date,
      check_in_time: firstDateLevel.check_in_time,
      check_out_time: firstDateLevel.check_out_time,
      available_quantity: firstDateLevel.available_quantity,
      status: firstDateLevel.status || "capacity",
      notes: firstDateLevel.notes,
      metadata: firstDateLevel.metadata || {},
    };

    const createdLevel = createDateInventoryLevelStep(dateLevelInput);

    // Return the result
    return new WorkflowResponse({
      inventory_item_id: input.inventory_item_id,
      date_levels: [createdLevel],
    });
  }
);
