import { createStep, StepResponse } from "@camped-ai/workflows-sdk";
import { DATE_INVENTORY_MODULE } from "../../modules/date-inventory";
import DateInventoryService from "../../modules/date-inventory/service";

export interface CreateDateInventoryLevelInput {
  inventory_item_id: string;
  product_variant_id?: string;
  from_date: string;
  to_date: string;
  check_in_time?: string;
  check_out_time?: string;
  available_quantity: number;
  status?: "capacity" | "reserved" | "allocated" | "canceled" | "on_hold" | "maintenance";
  cart_id?: string;
  order_id?: string;
  expires_at?: Date;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface CreateDateInventoryLevelOutput {
  id: string;
  inventory_item_id: string;
  from_date: string;
  to_date: string;
  available_quantity: number;
  status: string;
}

export const createDateInventoryLevelStep = createStep(
  "create-date-inventory-level",
  async (input: CreateDateInventoryLevelInput, { container }) => {
    const dateInventoryService: DateInventoryService = container.resolve(
      DATE_INVENTORY_MODULE
    );

    const dateInventoryLevel = await dateInventoryService.createDateInventoryLevel({
      inventory_item_id: input.inventory_item_id,
      product_variant_id: input.product_variant_id,
      from_date: input.from_date,
      to_date: input.to_date,
      check_in_time: input.check_in_time,
      check_out_time: input.check_out_time,
      available_quantity: input.available_quantity,
      status: input.status || "capacity",
      cart_id: input.cart_id,
      order_id: input.order_id,
      expires_at: input.expires_at,
      notes: input.notes,
      metadata: input.metadata || {},
    });

    return new StepResponse(
      {
        id: dateInventoryLevel.id,
        inventory_item_id: dateInventoryLevel.inventory_item_id,
        from_date: dateInventoryLevel.from_date,
        to_date: dateInventoryLevel.to_date,
        available_quantity: dateInventoryLevel.available_quantity,
        status: dateInventoryLevel.status,
      },
      {
        id: dateInventoryLevel.id,
        inventory_item_id: dateInventoryLevel.inventory_item_id,
      }
    );
  },
  async (compensationInput, { container }) => {
    if (!compensationInput?.id) return;

    const dateInventoryService: DateInventoryService = container.resolve(
      DATE_INVENTORY_MODULE
    );

    // Soft delete the created date inventory level
    await dateInventoryService.softDeleteInventoryDateLevels([compensationInput.id]);
  }
);
