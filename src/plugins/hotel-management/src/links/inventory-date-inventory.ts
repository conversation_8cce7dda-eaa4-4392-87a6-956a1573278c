import { defineLink } from "@camped-ai/framework/utils";
import InventoryModule from "@camped-ai/medusa/inventory";
import DateInventoryModule from "../modules/date-inventory";

/**
 * Link between Medusa's core inventory_item and our date-based inventory levels
 * This creates a one-to-many relationship:
 * - One inventory_item can have many date-based availability records
 */
export default defineLink(
  InventoryModule.linkable.inventoryItem,
  {
    linkable: DateInventoryModule.linkable.inventoryDateLevel,
    isList: true
  }
);
