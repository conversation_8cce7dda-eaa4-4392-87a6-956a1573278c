export interface DateRangeOptions {
  start_date: string; // YYYY-MM-DD
  end_date: string;   // YYYY-MM-DD
  check_in_time?: string; // HH:mm
  check_out_time?: string; // HH:mm
  default_quantity: number;
  status?: "capacity" | "on_hold" | "maintenance";
  notes?: string;
  metadata?: Record<string, any>;
}

export interface GeneratedDateLevel {
  from_date: string;
  to_date: string;
  check_in_time?: string;
  check_out_time?: string;
  available_quantity: number;
  status: "capacity" | "on_hold" | "maintenance";
  notes?: string;
  metadata?: Record<string, any>;
}

/**
 * Generate date levels for a date range
 * Creates daily capacity records between start and end dates
 */
export function generateDateLevelsForRange(options: DateRangeOptions): GeneratedDateLevel[] {
  const {
    start_date,
    end_date,
    check_in_time = "15:00", // Default hotel check-in
    check_out_time = "11:00", // Default hotel check-out
    default_quantity,
    status = "capacity",
    notes,
    metadata = {},
  } = options;

  const startDate = new Date(start_date);
  const endDate = new Date(end_date);
  const dateLevels: GeneratedDateLevel[] = [];

  // Validate dates
  if (startDate >= endDate) {
    throw new Error("start_date must be before end_date");
  }

  // Generate daily records
  const currentDate = new Date(startDate);
  while (currentDate < endDate) {
    const nextDate = new Date(currentDate);
    nextDate.setDate(nextDate.getDate() + 1);

    dateLevels.push({
      from_date: currentDate.toISOString().split('T')[0],
      to_date: nextDate.toISOString().split('T')[0],
      check_in_time,
      check_out_time,
      available_quantity: default_quantity,
      status,
      notes,
      metadata: {
        ...metadata,
        generated_at: new Date().toISOString(),
        day_of_week: currentDate.getDay(),
        is_weekend: currentDate.getDay() === 0 || currentDate.getDay() === 6,
      },
    });

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dateLevels;
}

/**
 * Generate weekly date levels
 * Creates weekly capacity records (Monday to Monday)
 */
export function generateWeeklyDateLevels(options: DateRangeOptions): GeneratedDateLevel[] {
  const {
    start_date,
    end_date,
    check_in_time = "15:00",
    check_out_time = "11:00",
    default_quantity,
    status = "capacity",
    notes,
    metadata = {},
  } = options;

  const startDate = new Date(start_date);
  const endDate = new Date(end_date);
  const dateLevels: GeneratedDateLevel[] = [];

  // Find the first Monday
  const currentDate = new Date(startDate);
  while (currentDate.getDay() !== 1) { // 1 = Monday
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Generate weekly records
  while (currentDate < endDate) {
    const nextWeek = new Date(currentDate);
    nextWeek.setDate(nextWeek.getDate() + 7);

    // Don't go beyond end_date
    const weekEnd = nextWeek > endDate ? endDate : nextWeek;

    dateLevels.push({
      from_date: currentDate.toISOString().split('T')[0],
      to_date: weekEnd.toISOString().split('T')[0],
      check_in_time,
      check_out_time,
      available_quantity: default_quantity,
      status,
      notes,
      metadata: {
        ...metadata,
        generated_at: new Date().toISOString(),
        period_type: 'weekly',
        week_start: currentDate.toISOString().split('T')[0],
      },
    });

    currentDate.setDate(currentDate.getDate() + 7);
  }

  return dateLevels;
}

/**
 * Generate monthly date levels
 * Creates monthly capacity records (1st to 1st of next month)
 */
export function generateMonthlyDateLevels(options: DateRangeOptions): GeneratedDateLevel[] {
  const {
    start_date,
    end_date,
    check_in_time = "15:00",
    check_out_time = "11:00",
    default_quantity,
    status = "capacity",
    notes,
    metadata = {},
  } = options;

  const startDate = new Date(start_date);
  const endDate = new Date(end_date);
  const dateLevels: GeneratedDateLevel[] = [];

  // Start from the first day of the month
  const currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1);

  // Generate monthly records
  while (currentDate < endDate) {
    const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
    
    // Don't go beyond end_date
    const monthEnd = nextMonth > endDate ? endDate : nextMonth;

    dateLevels.push({
      from_date: currentDate.toISOString().split('T')[0],
      to_date: monthEnd.toISOString().split('T')[0],
      check_in_time,
      check_out_time,
      available_quantity: default_quantity,
      status,
      notes,
      metadata: {
        ...metadata,
        generated_at: new Date().toISOString(),
        period_type: 'monthly',
        month: currentDate.getMonth() + 1,
        year: currentDate.getFullYear(),
      },
    });

    currentDate.setMonth(currentDate.getMonth() + 1);
  }

  return dateLevels;
}
