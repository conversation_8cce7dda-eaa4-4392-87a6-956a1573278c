import { model } from "@camped-ai/framework/utils";

/**
 * Date-based inventory level model
 * This extends Medusa's inventory system with date-based availability
 */
export const InventoryDateLevel = model.define("inventory_date_level", {
  id: model.id({ prefix: "date_inv" }).primaryKey(),
  
  // Core inventory reference
  inventory_item_id: model.text().index(),
  product_variant_id: model.text().nullable(),
  
  // Date range fields
  from_date: model.dateTime(),
  to_date: model.dateTime(),
  check_in_time: model.text().nullable(),
  check_out_time: model.text().nullable(),
  
  // Availability fields
  available_quantity: model.number().default(0),
  status: model.enum([
    "capacity",
    "reserved", 
    "allocated",
    "canceled",
    "on_hold",
    "maintenance"
  ]).default("capacity"),
  
  // Reservation fields
  cart_id: model.text().nullable(),
  order_id: model.text().nullable(),
  expires_at: model.dateTime().nullable(),
  
  // Metadata
  notes: model.text().nullable(),
  metadata: model.json().default({}),
})
.indexes([
  {
    name: "IDX_date_inventory_item_date_range",
    on: ["inventory_item_id", "from_date", "to_date"],
    unique: false,
  },
  {
    name: "IDX_date_inventory_status_expires",
    on: ["status", "expires_at"],
    unique: false,
  },
  {
    name: "IDX_date_inventory_product_variant",
    on: ["product_variant_id"],
    unique: false,
    where: "product_variant_id IS NOT NULL",
  },
  {
    name: "IDX_date_inventory_cart",
    on: ["cart_id"],
    unique: false,
    where: "cart_id IS NOT NULL",
  },
  {
    name: "IDX_date_inventory_order",
    on: ["order_id"],
    unique: false,
    where: "order_id IS NOT NULL",
  }
]);
