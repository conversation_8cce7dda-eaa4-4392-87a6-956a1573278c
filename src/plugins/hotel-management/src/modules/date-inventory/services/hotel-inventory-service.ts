import { MedusaService, Modules } from "@camped-ai/framework/utils";
import { IInventoryService } from "@camped-ai/framework/types";

export interface DateBasedInventoryInput {
  inventory_item_id: string;
  location_id: string;
  from_date: string;
  to_date: string;
  check_in_time?: string;
  check_out_time?: string;
  stocked_quantity: number;
  level_status?: 'available' | 'reserved' | 'allocated' | 'blocked' | 'maintenance';
  cart_id?: string;
  order_id?: string;
  expires_at?: Date;
  notes?: string;
  hotel_metadata?: Record<string, any>;
}

export interface DateAvailabilityQuery {
  inventory_item_id: string;
  location_id?: string;
  from_date: string;
  to_date: string;
  check_in_time?: string;
  check_out_time?: string;
  quantity?: number;
}

export interface ReservationInput extends DateAvailabilityQuery {
  cart_id: string;
  quantity: number;
  ttl_minutes?: number;
}

export interface AvailabilityResult {
  available: number;
  capacity: number;
  reserved: number;
  allocated: number;
  blocked: number;
}

/**
 * Hotel Inventory Service
 * Extends Medusa's inventory system with date-based functionality
 */
export default class HotelInventoryService extends MedusaService({}) {
  protected readonly inventoryService: IInventoryService;
  protected readonly container: any;
  protected readonly pluginOptions: any;

  constructor(container: any) {
    super(container);
    this.container = container;
    this.inventoryService = container.resolve(Modules.INVENTORY);
    this.pluginOptions = container.resolve("hotelManagementPluginOptions");
  }

  /**
   * Mark an inventory item as date-based
   */
  async enableDateBasedInventory(inventoryItemId: string, metadata: Record<string, any> = {}) {
    // Use raw SQL to update the extended inventory_item table
    const sql = `
      UPDATE inventory_item
      SET
        inventory_type = $1,
        hotel_metadata = $2,
        updated_at = now()
      WHERE id = $3
    `;

    await this.container.resolve("manager").execute(sql, [
      "date_based",
      JSON.stringify(metadata),
      inventoryItemId,
    ]);

    return { success: true };
  }

  /**
   * Create date-based inventory level
   */
  async createDateInventoryLevel(data: DateBasedInventoryInput) {
    // First, ensure the inventory item is marked as date_based
    await this.enableDateBasedInventory(data.inventory_item_id, data.hotel_metadata || {});

    // Create basic inventory level first
    const inventoryLevel = await this.inventoryService.createInventoryLevels([{
      inventory_item_id: data.inventory_item_id,
      location_id: data.location_id,
      stocked_quantity: data.stocked_quantity,
    }]);

    // Then update with extended fields using raw SQL
    const sql = `
      UPDATE inventory_level
      SET
        from_date = $1,
        to_date = $2,
        check_in_time = $3,
        check_out_time = $4,
        level_status = $5,
        notes = $6,
        hotel_metadata = $7,
        updated_at = now()
      WHERE id = $8
    `;

    await this.container.resolve("manager").execute(sql, [
      data.from_date,
      data.to_date,
      data.check_in_time,
      data.check_out_time,
      data.level_status || 'available',
      data.notes,
      JSON.stringify(data.hotel_metadata || {}),
      inventoryLevel[0].id,
    ]);

    return inventoryLevel[0];
  }

  /**
   * Check date-based availability
   */
  async checkDateAvailability(query: DateAvailabilityQuery): Promise<AvailabilityResult> {
    const {
      inventory_item_id,
      location_id,
      from_date,
      to_date,
      check_in_time = '00:00',
      check_out_time = '00:00',
      quantity,
    } = query;

    // Use raw SQL for complex date range queries
    const sql = `
      WITH req AS (
        SELECT
          $1::text AS inventory_item_id,
          $2::text AS location_id,
          $3::date AS from_date,
          $4::date AS to_date,
          $5::time AS check_in_time,
          $6::time AS check_out_time
      ),
      overlapping_levels AS (
        SELECT 
          il.*,
          ii.inventory_type
        FROM inventory_level il
        JOIN inventory_item ii ON il.inventory_item_id = ii.id
        JOIN req r ON il.inventory_item_id = r.inventory_item_id
        WHERE ii.inventory_type IN ('date_based', 'hybrid')
          AND il.from_date IS NOT NULL 
          AND il.to_date IS NOT NULL
          AND il.deleted_at IS NULL
          AND ($2::text IS NULL OR il.location_id = r.location_id)
          AND tsrange(
            il.from_date::timestamp + COALESCE(il.check_in_time, '00:00'::time),
            il.to_date::timestamp + COALESCE(il.check_out_time, '00:00'::time),
            '[)'
          ) && tsrange(
            r.from_date::timestamp + r.check_in_time,
            r.to_date::timestamp + r.check_out_time,
            '[)'
          )
      )
      SELECT
        COALESCE(SUM(CASE WHEN level_status = 'available' THEN stocked_quantity ELSE 0 END), 0) AS capacity,
        COALESCE(SUM(CASE WHEN level_status = 'reserved' AND (expires_at IS NULL OR expires_at > now()) THEN stocked_quantity ELSE 0 END), 0) AS reserved,
        COALESCE(SUM(CASE WHEN level_status = 'allocated' THEN stocked_quantity ELSE 0 END), 0) AS allocated,
        COALESCE(SUM(CASE WHEN level_status IN ('blocked', 'maintenance') THEN stocked_quantity ELSE 0 END), 0) AS blocked
      FROM overlapping_levels;
    `;

    const params = [
      inventory_item_id,
      location_id || null,
      from_date,
      to_date,
      check_in_time,
      check_out_time,
    ];

    // Execute raw query
    const result = await this.container.resolve("manager").execute(sql, params);
    const row = result[0] || { capacity: 0, reserved: 0, allocated: 0, blocked: 0 };
    
    const available = Math.max(0, Number(row.capacity) - Number(row.reserved) - Number(row.allocated) - Number(row.blocked));

    return {
      capacity: Number(row.capacity),
      reserved: Number(row.reserved),
      allocated: Number(row.allocated),
      blocked: Number(row.blocked),
      available,
    };
  }

  /**
   * Reserve date-based inventory
   */
  async reserveDateInventory(input: ReservationInput): Promise<{ success: boolean; reason?: string }> {
    const {
      inventory_item_id,
      location_id,
      from_date,
      to_date,
      check_in_time,
      check_out_time,
      cart_id,
      quantity,
      ttl_minutes = this.pluginOptions.defaultReservationTTL,
    } = input;

    // Check availability first
    const availability = await this.checkDateAvailability({
      inventory_item_id,
      location_id,
      from_date,
      to_date,
      check_in_time,
      check_out_time,
      quantity,
    });

    if (availability.available < quantity) {
      return {
        success: false,
        reason: `Insufficient availability: ${availability.available} < ${quantity}`,
      };
    }

    // Create reservation
    await this.createDateInventoryLevel({
      inventory_item_id,
      location_id: location_id!,
      from_date,
      to_date,
      check_in_time,
      check_out_time,
      stocked_quantity: quantity,
      level_status: 'reserved',
      cart_id,
      expires_at: new Date(Date.now() + ttl_minutes * 60 * 1000),
      notes: `Reserved for cart ${cart_id}`,
      hotel_metadata: { reservation_type: 'cart', created_at: new Date().toISOString() },
    });

    return { success: true };
  }

  /**
   * Commit reservations to allocations
   */
  async commitReservations(cartId: string, orderId: string): Promise<{ success: boolean; updated: number }> {
    // Use raw SQL to update reservations to allocations
    const sql = `
      UPDATE inventory_level
      SET
        level_status = 'allocated',
        order_id = $1,
        cart_id = NULL,
        expires_at = NULL,
        notes = $2,
        hotel_metadata = $3,
        updated_at = now()
      WHERE level_status = 'reserved'
        AND cart_id = $4
        AND (expires_at IS NULL OR expires_at > now())
        AND from_date IS NOT NULL
    `;

    const result = await this.container.resolve("manager").execute(sql, [
      orderId,
      `Allocated for order ${orderId}`,
      JSON.stringify({
        allocation_type: 'order',
        allocated_at: new Date().toISOString(),
        original_cart_id: cartId,
      }),
      cartId,
    ]);

    const updated = result.affectedRows || result.rowCount || 0;
    return { success: true, updated };
  }

  /**
   * Get inventory summary for an item
   */
  async getInventorySummary(inventoryItemId: string) {
    const query = this.container.resolve("query");

    // Get inventory item details
    const { data: items } = await query.graph({
      entity: "inventory_item",
      filters: { id: inventoryItemId },
      fields: ["id", "inventory_type", "hotel_metadata"],
    });

    if (!items.length) {
      throw new Error(`Inventory item ${inventoryItemId} not found`);
    }

    const item = items[0];

    if (item.inventory_type === 'date_based' || item.inventory_type === 'hybrid') {
      // Get date-based inventory levels
      const { data: levels } = await query.graph({
        entity: "inventory_level",
        filters: { 
          inventory_item_id: inventoryItemId,
          from_date: { $ne: null },
          to_date: { $ne: null },
        },
        fields: ["id", "level_status", "stocked_quantity", "from_date", "to_date", "expires_at"],
      });

      const summary = levels.reduce((acc, level) => {
        const isExpired = level.expires_at && new Date(level.expires_at) <= new Date();
        const effectiveStatus = (level.level_status === 'reserved' && isExpired) ? 'available' : level.level_status;
        
        acc[effectiveStatus] = (acc[effectiveStatus] || 0) + level.stocked_quantity;
        return acc;
      }, {} as Record<string, number>);

      return {
        inventory_type: item.inventory_type,
        total_levels: levels.length,
        summary,
        metadata: item.hotel_metadata,
      };
    } else {
      // Standard location-based inventory
      const levels = await this.inventoryService.listInventoryLevels({
        inventory_item_id: inventoryItemId,
      });

      const totalStock = levels.reduce((sum, level) => sum + (level.stocked_quantity || 0), 0);
      const totalReserved = levels.reduce((sum, level) => sum + (level.reserved_quantity || 0), 0);

      return {
        inventory_type: 'location_based',
        total_stock: totalStock,
        available_stock: totalStock - totalReserved,
        reserved_stock: totalReserved,
        locations: levels.length,
      };
    }
  }

  /**
   * Cleanup expired reservations
   */
  async cleanupExpiredReservations(): Promise<{ cleaned: number }> {
    // Use raw SQL to cleanup expired reservations
    const sql = `
      UPDATE inventory_level
      SET
        level_status = 'available',
        cart_id = NULL,
        expires_at = NULL,
        notes = 'Reservation expired and released',
        hotel_metadata = $1,
        updated_at = now()
      WHERE level_status = 'reserved'
        AND expires_at IS NOT NULL
        AND expires_at <= now()
        AND from_date IS NOT NULL
    `;

    const result = await this.container.resolve("manager").execute(sql, [
      JSON.stringify({
        released_at: new Date().toISOString(),
        release_reason: 'expired',
      }),
    ]);

    const cleaned = result.affectedRows || result.rowCount || 0;
    return { cleaned };
  }
}
