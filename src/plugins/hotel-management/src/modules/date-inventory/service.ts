import { MedusaService } from "@camped-ai/framework/utils";
import { InventoryDateLevel } from "./models/date-inventory-level";

export type InventoryWindowStatus =
  | "capacity"
  | "reserved"
  | "allocated"
  | "canceled"
  | "on_hold"
  | "on_request"
  | "maintenance";

export interface DateWindowInput {
  inventory_item_id: string;
  product_variant_id?: string | null;
  from_date: string; // YYYY-MM-DD
  to_date: string;   // YYYY-MM-DD (exclusive upper bound)
  check_in_time?: string | null;  // HH:mm or HH:mm:ss
  check_out_time?: string | null; // HH:mm or HH:mm:ss
}

export interface CapacityUpsertInput extends DateWindowInput {
  quantity: number;
  status?: Extract<InventoryWindowStatus, "capacity" | "on_hold" | "maintenance">;
  notes?: string | null;
  metadata?: Record<string, any> | null;
}

export interface ReserveInput extends DateWindowInput {
  quantity: number;
  cart_id: string;
  ttl_minutes?: number; // default 15
  notes?: string | null;
  metadata?: Record<string, any> | null;
}

export interface CommitInput {
  cart_id: string;
  order_id: string;
}

export interface CancelReservationInput {
  cart_id: string;
}

export interface ReleaseAllocationInput {
  order_id: string;
  windows?: Array<DateWindowInput & { quantity: number }>;
  reason?: string;
}

export interface AvailabilityQuery extends DateWindowInput {
  quantity?: number; // if omitted, returns numeric availability
}

export interface AvailabilityBreakdown {
  capacity: number;
  blocks: number;    // on_hold + maintenance
  reserved: number;  // live (expires_at > now)
  allocated: number;
  available: number;
}

/**
 * DateInventoryService for managing date-based inventory
 * This service handles date-based availability for hotel rooms and services
 */
export default class DateInventoryService extends MedusaService({
  InventoryDateLevel,
}) {
  protected readonly container: any;
  protected readonly withClient: (fn: (client: any) => Promise<any>) => Promise<any>;

  private static DEFAULT_TTL_MINUTES = 15;

  constructor(container: any) {
    super(container);
    this.container = container;

    // Resolve withClient helper for raw SQL queries
    try {
      this.withClient = require("../../../../utils/db").withClient;
    } catch {
      this.withClient = async (_fn: any) => {
        throw new Error("withClient utility not found. Ensure src/utils/db exports withClient.");
      };
    }
  }

  /**
   * Create date inventory level
   */
  async createDateInventoryLevel(data: any) {
    const dateInventoryLevel = await this.createInventoryDateLevels([data]);
    return dateInventoryLevel[0];
  }

  /**
   * Get date inventory levels for an inventory item
   */
  async getDateInventoryLevelsForItem(inventoryItemId: string) {
    return await this.listInventoryDateLevels({
      inventory_item_id: inventoryItemId,
    });
  }

  /**
   * Reserve inventory with cart linking
   */
  async reserve(input: ReserveInput): Promise<{ success: boolean; reason?: string }> {
    const {
      inventory_item_id,
      product_variant_id = null,
      from_date,
      to_date,
      check_in_time = null,
      check_out_time = null,
      cart_id,
      quantity,
      ttl_minutes = DateInventoryService.DEFAULT_TTL_MINUTES,
      notes = null,
      metadata = {},
    } = input;

    // Check availability first
    const breakdown = await this.checkAvailability({
      inventory_item_id,
      product_variant_id: product_variant_id || undefined,
      from_date,
      to_date,
      check_in_time: check_in_time || undefined,
      check_out_time: check_out_time || undefined,
      quantity,
    });

    if (breakdown.available < quantity) {
      return { success: false, reason: `Insufficient availability: ${breakdown.available} < ${quantity}` };
    }

    // Create reservation
    await this.createDateInventoryLevel({
      inventory_item_id,
      product_variant_id,
      from_date,
      to_date,
      check_in_time,
      check_out_time,
      available_quantity: quantity,
      status: "reserved",
      cart_id,
      expires_at: new Date(Date.now() + ttl_minutes * 60 * 1000),
      notes,
      metadata: metadata || {},
    });

    return { success: true };
  }

  /**
   * Commit reservations to allocations
   */
  async commit(input: CommitInput): Promise<{ success: boolean; updated: number }> {
    const { cart_id, order_id } = input;

    // Find all reservations for this cart
    const reservations = await this.listInventoryDateLevels({
      status: "reserved",
      cart_id: cart_id,
      expires_at: { $gt: new Date() }, // Only non-expired reservations
    });

    let updated = 0;
    for (const reservation of reservations) {
      // Update reservation to allocation
      await this.updateInventoryDateLevels([{
        id: reservation.id,
        status: "allocated",
        order_id: order_id,
        cart_id: null,
        expires_at: null,
      }]);

      updated++;
    }

    return { success: true, updated };
  }

  /**
   * Check availability using the existing complex SQL logic
   * This maintains your sophisticated availability calculation
   */
  async checkAvailability(q: AvailabilityQuery): Promise<AvailabilityBreakdown & { is_sufficient?: boolean }> {
    // Use your existing complex SQL logic here
    // This is a simplified version - you can copy your full implementation
    const {
      inventory_item_id,
      product_variant_id = null,
      from_date,
      to_date,
      check_in_time = null,
      check_out_time = null,
      quantity,
    } = q;

    const sql = `
      WITH req AS (
        SELECT
          $1::text AS inventory_item_id,
          $2::text AS product_variant_id,
          $3::date AS from_date,
          $4::date AS to_date,
          COALESCE($5::time, '00:00'::time) AS check_in_time,
          COALESCE($6::time, '00:00'::time) AS check_out_time
      ),
      overlap AS (
        SELECT t.*
        FROM inventory_date_level t
        JOIN req r
          ON t.inventory_item_id = r.inventory_item_id
         AND COALESCE(t.product_variant_id, '') = COALESCE(r.product_variant_id, '')
         AND tsrange(t.from_date::timestamp + COALESCE(t.check_in_time, '00:00'::time),
                     t.to_date::timestamp + COALESCE(t.check_out_time, '00:00'::time),
                     '[)')
          && tsrange(r.from_date::timestamp + r.check_in_time,
                     r.to_date::timestamp + r.check_out_time,
                     '[)')
        WHERE t.deleted_at IS NULL
      )
      SELECT
        COALESCE(SUM(CASE WHEN status = 'capacity' THEN available_quantity ELSE 0 END), 0) AS capacity,
        COALESCE(SUM(CASE WHEN status IN ('on_hold','maintenance') THEN available_quantity ELSE 0 END), 0) AS blocks,
        COALESCE(SUM(CASE WHEN status = 'reserved' AND (expires_at IS NULL OR expires_at > now()) THEN available_quantity ELSE 0 END), 0) AS reserved,
        COALESCE(SUM(CASE WHEN status = 'allocated' THEN available_quantity ELSE 0 END), 0) AS allocated
      FROM overlap;
    `;

    const params = [
      inventory_item_id,
      product_variant_id,
      from_date,
      to_date,
      check_in_time,
      check_out_time,
    ];

    const result = await this.withClient(async (client: any) => client.query(sql, params));
    const row = result.rows[0] || { capacity: 0, blocks: 0, reserved: 0, allocated: 0 };
    const available = Math.max(0, Number(row.capacity) - Number(row.blocks) - Number(row.reserved) - Number(row.allocated));
    
    const breakdown: AvailabilityBreakdown = {
      capacity: Number(row.capacity),
      blocks: Number(row.blocks),
      reserved: Number(row.reserved),
      allocated: Number(row.allocated),
      available,
    };
    
    if (typeof quantity === "number") {
      return { ...breakdown, is_sufficient: available >= quantity };
    }
    return breakdown;
  }
}
