import { Migration } from '@mikro-orm/migrations';

export class Migration20250108000001 extends Migration {

  override async up(): Promise<void> {
    // 1) Create enum type for inventory date level status
    this.addSql(`
      DO $$ BEGIN
        CREATE TYPE inventory_date_level_status AS ENUM (
          'capacity',
          'reserved', 
          'allocated',
          'canceled',
          'on_hold',
          'maintenance'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // 2) Create the inventory_date_level table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "inventory_date_level" (
        "id" text NOT NULL,
        "inventory_item_id" text NOT NULL,
        "product_variant_id" text NULL,
        "from_date" date NOT NULL,
        "to_date" date NOT NULL,
        "check_in_time" time NULL,
        "check_out_time" time NULL,
        "available_quantity" integer NOT NULL DEFAULT 0,
        "status" inventory_date_level_status NOT NULL DEFAULT 'capacity',
        "cart_id" text NULL,
        "order_id" text NULL,
        "expires_at" timestamptz NULL,
        "notes" text NULL,
        "metadata" jsonb NOT NULL DEFAULT '{}'::jsonb,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "inventory_date_level_pkey" PRIMARY KEY ("id"),
        CONSTRAINT "chk_idl_to_after_from" CHECK ("to_date" > "from_date")
      );
    `);

    // 3) Create indexes for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_inventory_date_level_deleted_at" 
      ON "inventory_date_level" (deleted_at) 
      WHERE deleted_at IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_date_inventory_item_date_range" 
      ON "inventory_date_level" ("inventory_item_id", "from_date", "to_date");
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_date_inventory_status_expires" 
      ON "inventory_date_level" ("status", "expires_at");
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_date_inventory_product_variant" 
      ON "inventory_date_level" ("product_variant_id") 
      WHERE "product_variant_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_date_inventory_cart" 
      ON "inventory_date_level" ("cart_id") 
      WHERE "cart_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_date_inventory_order" 
      ON "inventory_date_level" ("order_id") 
      WHERE "order_id" IS NOT NULL;
    `);

    // 4) Create a function to automatically cleanup expired reservations
    this.addSql(`
      CREATE OR REPLACE FUNCTION cleanup_expired_reservations()
      RETURNS void AS $$
      BEGIN
        DELETE FROM inventory_date_level
        WHERE status = 'reserved' 
          AND expires_at IS NOT NULL 
          AND expires_at <= now();
      END;
      $$ LANGUAGE plpgsql;
    `);
  }

  override async down(): Promise<void> {
    // Drop in reverse order
    this.addSql(`DROP FUNCTION IF EXISTS cleanup_expired_reservations();`);
    this.addSql(`DROP TABLE IF EXISTS "inventory_date_level" CASCADE;`);
    this.addSql(`DROP TYPE IF EXISTS inventory_date_level_status;`);
  }
}
