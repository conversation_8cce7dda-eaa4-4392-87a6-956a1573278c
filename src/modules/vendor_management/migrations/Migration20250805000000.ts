import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to add valid_from and valid_to columns to exchange_rate table
 * 
 * This migration adds date range functionality to exchange rates by adding:
 * - valid_from: Start date for exchange rate validity
 * - valid_to: End date for exchange rate validity
 * 
 * The existing 'date' column is kept for backward compatibility but made nullable.
 */
export class Migration20250805000000 extends Migration {
  async up(): Promise<void> {
    // Add the new date range columns
    this.addSql(`
      ALTER TABLE "exchange_rate" 
      ADD COLUMN "valid_from" timestamptz,
      ADD COLUMN "valid_to" timestamptz;
    `);

    // For existing records, set valid_from and valid_to based on the existing date
    // Set valid_from to the existing date and valid_to to end of the same day
    this.addSql(`
      UPDATE "exchange_rate" 
      SET 
        "valid_from" = "date",
        "valid_to" = "date" + INTERVAL '1 day' - INTERVAL '1 second'
      WHERE "date" IS NOT NULL;
    `);

    // Make the new columns NOT NULL after populating them
    this.addSql(`
      ALTER TABLE "exchange_rate" 
      ALTER COLUMN "valid_from" SET NOT NULL,
      ALTER COLUMN "valid_to" SET NOT NULL;
    `);

    // Make the old date column nullable for backward compatibility
    this.addSql(`
      ALTER TABLE "exchange_rate" 
      ALTER COLUMN "date" DROP NOT NULL;
    `);

    // Create indexes for the new columns
    this.addSql(`
      CREATE INDEX "IDX_exchange_rate_valid_from" 
      ON "exchange_rate" ("valid_from") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_exchange_rate_valid_to" 
      ON "exchange_rate" ("valid_to") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_exchange_rate_date_range" 
      ON "exchange_rate" ("valid_from", "valid_to") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX "IDX_exchange_rate_range_currencies" 
      ON "exchange_rate" ("valid_from", "valid_to", "base_currency", "selling_currency") 
      WHERE "deleted_at" IS NULL;
    `);
  }

  async down(): Promise<void> {
    // Drop the new indexes
    this.addSql(`DROP INDEX IF EXISTS "IDX_exchange_rate_valid_from";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_exchange_rate_valid_to";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_exchange_rate_date_range";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_exchange_rate_range_currencies";`);

    // Make the date column NOT NULL again
    this.addSql(`
      ALTER TABLE "exchange_rate" 
      ALTER COLUMN "date" SET NOT NULL;
    `);

    // Drop the new columns
    this.addSql(`
      ALTER TABLE "exchange_rate" 
      DROP COLUMN "valid_from",
      DROP COLUMN "valid_to";
    `);
  }
}
