import { model } from "@camped-ai/framework/utils";
import { Package } from "./package";

export const PackageDestination = model
  .define(
    { tableName: "package_destinations", name: "PackageDestination" },
    {
      id: model.id({ prefix: "pkg_dest" }).primaryKey(),

      // Foreign keys
      destination_id: model.text().index(),

      // Relationships
      package: model.belongsTo(() => Package, {
        foreignKey: "package_id",
      }),
    }
  )
  .indexes([
    {
      name: "IDX_package_destinations_package_id",
      on: ["package_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_package_destinations_destination_id",
      on: ["destination_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_package_destinations_unique",
      on: ["package_id", "destination_id"],
      unique: true,
      where: "deleted_at IS NULL",
    },
  ]);
