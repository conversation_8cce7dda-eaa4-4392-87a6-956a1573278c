import { model } from "@camped-ai/framework/utils";
import { PackageDestination } from "./package-destination";

export const Package = model
  .define("packages", {
    id: model.id({ prefix: "pkg" }).primaryKey(),

    // Foreign keys
    package_lookup_id: model.text().index(),

    // Package details
    description: model.text(),
    valid_from_date: model.dateTime(),
    valid_to_date: model.dateTime(),

    // Relationships
    package_destinations: model.hasMany(() => PackageDestination, {
      mappedBy: "package",
    }),
  })
  .indexes([
    {
      name: "IDX_package_lookup_id",
      on: ["package_lookup_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_package_valid_from_date",
      on: ["valid_from_date"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_package_valid_to_date",
      on: ["valid_to_date"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
