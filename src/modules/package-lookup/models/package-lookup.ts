import { model } from "@camped-ai/framework/utils";

export const PackageLookup = model
  .define("package_lookups", {
    id: model.id({ prefix: "plu" }).primaryKey(),
    
    // Basic Information
    package_name: model.text(),
    description: model.text().nullable(),
    
    // Status
    is_active: model.boolean().default(true),
  })
  .indexes([
    {
      name: "IDX_package_lookup_name",
      on: ["package_name"],
      unique: true,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_package_lookup_is_active",
      on: ["is_active"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
