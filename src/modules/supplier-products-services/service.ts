import { MedusaService, MedusaError } from "@camped-ai/framework/utils";
import { withClient } from "../../utils/db";
import { SUPPLIER_MANAGEMENT_MODULE } from "../vendor_management";
import {
  ProductService,
  Category,
  UnitType,
  Tag,
  CustomField,
  ProductServiceTag,
  ProductServiceCustomField,
  ProductServiceSupplier,
  SupplierOffering,
  SupplierOfferingCostHistory,
  SupplierOfferingPricingHistory,
  ProductServiceCostHistory,
} from "./models";
import {
  CreateProductServiceInput,
  UpdateProductServiceInput,
  CreateCategoryInput,
  UpdateCategoryInput,
  CreateUnitTypeInput,
  UpdateUnitTypeInput,
  CreateTagInput,
  UpdateTagInput,
  CreateCustomFieldInput,
  UpdateCustomFieldInput,
  CreateProductServiceSupplierInput,
  UpdateProductServiceSupplierInput,
  CreateSupplierOfferingInput,
  UpdateSupplierOfferingInput,
  CreateSupplierOfferingCostHistoryInput,
  UpdateSupplierOfferingCostHistoryInput,
  CreateProductServiceCostHistoryInput,
  UpdateProductServiceCostHistoryInput,
  ProductServiceOutput,
  CategoryOutput,
  UnitTypeOutput,
  TagOutput,
  CustomFieldOutput,
  ProductServiceSupplierOutput,
  SupplierOfferingOutput,
  SupplierOfferingCostHistoryOutput,
  ProductServiceCostHistoryOutput,
  ProductServiceFilters,
  CategoryFilters,
  UnitTypeFilters,
  TagFilters,
  CustomFieldFilters,
  ProductServiceSupplierFilters,
  SupplierOfferingFilters,
  SupplierOfferingCostHistoryFilters,
  ProductServiceCostHistoryFilters,
  ProductServiceListResponse,
  CategoryListResponse,
  UnitTypeListResponse,
  TagListResponse,
  CustomFieldListResponse,
  ProductServiceSupplierListResponse,
  SupplierOfferingListResponse,
  SupplierOfferingCostHistoryListResponse,
  SupplierOfferingCostHistoryStats,
  ProductServiceCostHistoryListResponse,
  ProductServiceCostHistoryStats,
  DynamicFieldSchema,
} from "./types";
import {
  generateProductServiceName,
  generateProductServiceUniqueKey,
  generateSupplierOfferingUniqueKey,
} from "./utils/name-generator";

class SupplierProductsServicesModuleService extends MedusaService({
  ProductService,
  Category,
  UnitType,
  Tag,
  CustomField,
  ProductServiceTag,
  ProductServiceCustomField,
  ProductServiceSupplier,
  SupplierOffering,
  SupplierOfferingCostHistory,
  ProductServiceCostHistory,
}) {
  protected container_: any;

  constructor(container: any) {
    super(container);
    this.container_ = container;
  }

  // Helper function to format dates as date-only strings to avoid timezone issues
  private formatDateOnly(date: Date | string | null): string | null {
    if (!date) return null;
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return null;
    // Use local date formatting to avoid timezone shifts
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Helper function to format product service output dates
  private formatProductServiceDates(productService: any): any {
    if (!productService) return productService;

    return {
      ...productService,
      valid_from: this.formatDateOnly(productService.valid_from),
      valid_to: this.formatDateOnly(productService.valid_to),
    };
  }

  // Direct database query to fetch supplier details
  private async fetchSupplierDetails(supplierId: string) {
    try {
      console.log(`🔍 Fetching supplier details for supplier_id: ${supplierId}`);

      // Use withClient utility for direct database access (imported at top of file)

      // Query supplier table directly (only fields that exist in the schema)
      const supplierData = await withClient(async (client) => {
        const query = `
          SELECT id, name, supplier_type, status
          FROM supplier
          WHERE id = $1 AND deleted_at IS NULL
        `;

        const result = await client.query(query, [supplierId]);
        return result.rows[0] || null;
      });

      if (supplierData) {
        console.log(`✅ Successfully fetched supplier: ${supplierData.name} (${supplierData.id})`);
        return {
          id: supplierData.id,
          name: supplierData.name,
          type: supplierData.supplier_type || "unknown",
          status: supplierData.status || "unknown",
          primary_contact_name: null, // Contact info is in separate table
          primary_contact_email: null, // Contact info is in separate table
        };
      } else {
        console.warn(`⚠️ Supplier not found for ID: ${supplierId}`);
        return null;
      }
    } catch (error) {
      console.warn(`❌ Could not fetch supplier details for ${supplierId}:`, error.message);
      return null;
    }
  }
  // Simple table existence check - don't try to create tables here
  private async checkTablesExist(): Promise<boolean> {
    try {
      // Try to query the category table to see if it exists
      await this.listCategories({});
      return true;
    } catch (error) {
      console.warn("Tables don't exist yet:", error.message);
      return false;
    }
  }

  // Product/Service Management Methods
  async createProductService(
    data: CreateProductServiceInput,
    queryService?: any
  ): Promise<ProductServiceOutput> {
    try {
      // Validate required fields (name is no longer required as it will be auto-generated)
      // Note: Type field has been removed from the interface

      if (!data.category_id) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Category ID is required"
        );
      }

      if (!data.unit_type_id) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Unit Type ID is required"
        );
      }

      // Get category to access dynamic field schema and name
      const categories = await this.listCategories({ id: data.category_id });
      if (!categories || categories.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Category with id ${data.category_id} not found`
        );
      }

      const category = categories[0];
      const dynamicFieldSchema = category.dynamic_field_schema || [];

      // Use provided name or generate one if not provided
      let finalName = data.name;
      console.log("🔍 Backend received name:", data.name);
      console.log("🔍 Backend received full data:", JSON.stringify(data, null, 2));

      if (!finalName || finalName.trim() === "") {
        console.log("📝 No name provided, auto-generating...");
        // Only auto-generate if no name is provided
        finalName = await generateProductServiceName(
          category.name,
          data.custom_fields || {},
          dynamicFieldSchema as DynamicFieldSchema[],
          queryService,
          undefined, // unitTypeName - not available in backend context
          data.valid_from,
          data.valid_to
        );
        console.log("✨ Generated name:", finalName);
      } else {
        console.log("👤 Using user-provided name:", finalName);
      }

      // Note: Duplicate validation has been disabled to allow creating products with same configuration
      console.log("ℹ️ Duplicate validation disabled - allowing product creation");

      // Verify unit type exists
      const unitTypes = await this.listUnitTypes({ id: data.unit_type_id });
      if (unitTypes.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Unit Type with ID ${data.unit_type_id} not found`
        );
      }

      // Validate base_cost if provided
      if (data.base_cost !== undefined && data.base_cost < 0) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Base cost must be non-negative"
        );
      }

      // Create the product/service using the final name (user-provided or auto-generated)
      const productServiceData = {
        name: finalName, // Use user-provided name or auto-generated if not provided
        type: "Service", // Default to Service since type field is removed from interface
        description: data.description,
        valid_from: data.valid_from ? new Date(data.valid_from) : null,
        valid_to: data.valid_to ? new Date(data.valid_to) : null,
        custom_fields: data.custom_fields,
        status: data.status || "active",
        category_id: data.category_id,
        unit_type_id: data.unit_type_id,
        service_level: data.service_level || "hotel",
        hotel_id: data.hotel_id,
        destination_id: data.destination_id,

        // Pricing Calculator Fields (use gross_cost instead of base_cost for backward compatibility)
        gross_cost: data.gross_cost || data.base_cost || null,
        cost_currency: data.cost_currency || null,
        commission: data.commission || null,
        net_cost: data.net_cost || null,
        margin_rate: data.margin_rate || null,
        selling_price_cost_currency: data.selling_price_cost_currency || null,
        selling_price_gbp: data.selling_price_gbp || null,
      };

      // Use the generated method to create the product service
      const productServices = await this.createProductServices([productServiceData]);
      const productService = productServices[0];

      // Handle tags if provided
      if (data.tag_ids && data.tag_ids.length > 0) {
        for (const tagId of data.tag_ids) {
          // Verify tag exists
          const tags = await this.listTags({ id: tagId });
          if (tags.length === 0) {
            throw new MedusaError(
              MedusaError.Types.NOT_FOUND,
              `Tag with ID ${tagId} not found`
            );
          }

          // Create the junction record using the generated method
          await this.createProductServiceTags([
            {
              product_service_id: productService.id,
              tag_id: tagId,
            },
          ]);
        }
      }

      // Handle custom fields if provided (custom_fields is now a Record<string, any>)
      // Note: Custom fields are now stored directly in the product_service table as JSON
      // No need to create separate junction records

      // Return the created product/service with relations
      return await this.retrieveProductService(productService.id);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create product/service: ${error.message}`
      );
    }
  }

  async retrieveProductService(id: string): Promise<ProductServiceOutput> {
    try {
      const productServices = await this.listProductServicesWithFilters({ id });

      if (productServices.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product/Service with ID ${id} not found`
        );
      }

      // Format dates to avoid timezone issues
      const productService = this.formatProductServiceDates(productServices[0]);
      return productService as ProductServiceOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve product/service: ${error.message}`
      );
    }
  }

  async listProductServicesWithFiltersAndCount(
    filters: ProductServiceFilters = {},
    options: {
      skip?: number;
      take?: number;
      sort_by?: string;
      sort_order?: string;
    } = {}
  ): Promise<{
    data: ProductServiceOutput[];
    count: number;
    limit: number;
    offset: number;
  }> {
    try {
      const {
        skip = 0,
        take = 20,
        sort_by = "updated_at",
        sort_order = "desc",
      } = options;

      // Build filter conditions
      const whereConditions: any = {};

      if (filters.id) {
        whereConditions.id = filters.id;
      }

      if (filters.name) {
        whereConditions.name = { $like: `%${filters.name}%` };
      }

      // Handle search parameter (case-insensitive search across name and description)
      if (filters.search) {
        whereConditions.$or = [
          { name: { $ilike: `%${filters.search}%` } },
          { description: { $ilike: `%${filters.search}%` } }
        ];
      }

      if (filters.type) {
        whereConditions.type = filters.type;
      }

      if (filters.status) {
        whereConditions.status = filters.status;
      }

      if (filters.category_id) {
        whereConditions.category_id = filters.category_id;
      }

      if (filters.unit_type_id) {
        whereConditions.unit_type_id = filters.unit_type_id;
      }

      if (filters.service_level) {
        whereConditions.service_level = filters.service_level;
      }

      if (filters.hotel_id) {
        whereConditions.hotel_id = filters.hotel_id;
      }

      if (filters.destination_id) {
        whereConditions.destination_id = filters.destination_id;
      }

      // Handle tag filtering if provided
      if (filters.tag_ids) {
        // For now, we'll handle tag filtering after the main query
        // This is a simplified approach - in production, you might want to use joins
      }

      // Build order options for sorting
      const orderOptions: any = {};

      // Map sort_by to actual database fields
      switch (sort_by) {
        case "name":
          orderOptions.name = sort_order.toUpperCase() as "ASC" | "DESC";
          break;
        case "type":
          orderOptions.type = sort_order.toUpperCase() as "ASC" | "DESC";
          break;
        case "category":
          // This will need to be handled after fetching category data
          orderOptions.updated_at = sort_order.toUpperCase() as "ASC" | "DESC";
          break;
        case "base_cost":
          orderOptions.base_cost = sort_order.toUpperCase() as "ASC" | "DESC";
          break;
        case "status":
          orderOptions.status = sort_order.toUpperCase() as "ASC" | "DESC";
          break;
        case "created_at":
          orderOptions.created_at = sort_order.toUpperCase() as "ASC" | "DESC";
          break;
        case "updated_at":
        default:
          orderOptions.updated_at = sort_order.toUpperCase() as "ASC" | "DESC";
          break;
      }

      // Use the generated listAndCountProductServices method from MedusaService
      const [data, count] = await this.listAndCountProductServices(
        whereConditions,
        {
          skip,
          take,
          relations: ["category", "unit_type", "tags", "custom_fields"],
          order: orderOptions,
        }
      );

      // Manually populate the actual tags for each product service
      const enrichedData = await Promise.all(
        data.map(async (productService: any) => {
          if (productService.tags && productService.tags.length > 0) {
            // Get the actual tag entities through the junction table
            const tagIds = productService.tags.map(
              (junction: any) => junction.tag_id
            );

            // Fetch each tag individually since listTags might not support $in
            const actualTags = [];
            for (const tagId of tagIds) {
              try {
                const tagResult = await this.listTags({ id: tagId });
                if (tagResult.length > 0) {
                  actualTags.push(tagResult[0]);
                }
              } catch (error) {
                console.warn(`Failed to fetch tag ${tagId}:`, error);
              }
            }

            // Replace the junction records with actual tag entities
            productService.tags = actualTags;
          } else {
            productService.tags = [];
          }

          // Format dates to avoid timezone issues
          return this.formatProductServiceDates(productService);
        })
      );

      // Handle client-side sorting for fields that require related data
      if (sort_by === "category") {
        enrichedData.sort((a: any, b: any) => {
          const aValue = a.category?.name || "";
          const bValue = b.category?.name || "";
          const comparison = aValue.localeCompare(bValue);
          return sort_order === "desc" ? -comparison : comparison;
        });
      }

      return {
        data: enrichedData as ProductServiceOutput[],
        count,
        limit: take,
        offset: skip,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list product services: ${error.message}`
      );
    }
  }

  async updateProductService(
    id: string,
    data: UpdateProductServiceInput
  ): Promise<ProductServiceOutput> {
    try {
      // Verify product/service exists
      const existingProductServices = await this.listProductServicesWithFilters(
        { id }
      );
      if (existingProductServices.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product/Service with ID ${id} not found`
        );
      }

      // Validate type if provided
      if (data.type && !["Product", "Service"].includes(data.type)) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Type must be either 'Product' or 'Service'"
        );
      }

      // Verify category exists if provided
      if (data.category_id) {
        const categories = await this.listCategories({ id: data.category_id });
        if (categories.length === 0) {
          throw new MedusaError(
            MedusaError.Types.NOT_FOUND,
            `Category with ID ${data.category_id} not found`
          );
        }
      }

      // Verify unit type exists if provided
      if (data.unit_type_id) {
        const unitTypes = await this.listUnitTypes({ id: data.unit_type_id });
        if (unitTypes.length === 0) {
          throw new MedusaError(
            MedusaError.Types.NOT_FOUND,
            `Unit Type with ID ${data.unit_type_id} not found`
          );
        }
      }

      // Validate base_cost if provided
      if (data.base_cost !== undefined && data.base_cost < 0) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Base cost must be non-negative"
        );
      }

      // Note: Duplicate validation has been disabled for updates as well
      console.log("ℹ️ Duplicate validation disabled for updates - allowing product update");

      // Check if base_cost is changing and create cost history if needed
      const currentProductService = existingProductServices[0];
      const shouldCreateHistory =
        data.base_cost !== undefined &&
        data.base_cost !== currentProductService.base_cost;

      // Update the product/service
      const updateData: any = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.type !== undefined) updateData.type = data.type;
      if (data.description !== undefined)
        updateData.description = data.description;
      if (data.base_cost !== undefined) updateData.base_cost = data.base_cost;
      if (data.valid_from !== undefined)
        updateData.valid_from = data.valid_from || null;
      if (data.valid_to !== undefined)
        updateData.valid_to = data.valid_to || null;
      if (data.status !== undefined) updateData.status = data.status;
      if (data.category_id !== undefined)
        updateData.category_id = data.category_id;
      if (data.unit_type_id !== undefined)
        updateData.unit_type_id = data.unit_type_id;
      if (data.service_level !== undefined)
        updateData.service_level = data.service_level;
      if (data.hotel_id !== undefined) updateData.hotel_id = data.hotel_id;
      if (data.destination_id !== undefined)
        updateData.destination_id = data.destination_id;

      // Price tracking fields for flagging system
      if (data.highest_price !== undefined)
        updateData.highest_price = data.highest_price;
      if (data.highest_price_currency !== undefined)
        updateData.highest_price_currency = data.highest_price_currency;
      if (data.price_flag_active !== undefined)
        updateData.price_flag_active = data.price_flag_active;
      if (data.price_flag_created_at !== undefined)
        updateData.price_flag_created_at = data.price_flag_created_at;
      if (data.price_flag_supplier_offering_id !== undefined)
        updateData.price_flag_supplier_offering_id =
          data.price_flag_supplier_offering_id;

      // Synchronization fields for reverse mapping
      if (data.product_id !== undefined)
        updateData.product_id = data.product_id;
      if (data.product_variant_id !== undefined)
        updateData.product_variant_id = data.product_variant_id;

      // Pricing Calculator Fields (7 required fields)
      if (data.gross_cost !== undefined)
        updateData.gross_cost = data.gross_cost;
      if (data.cost_currency !== undefined)
        updateData.cost_currency = data.cost_currency;
      if (data.commission !== undefined)
        updateData.commission = data.commission;
      if (data.net_cost !== undefined)
        updateData.net_cost = data.net_cost;
      if (data.margin_rate !== undefined)
        updateData.margin_rate = data.margin_rate;
      if (data.selling_price_cost_currency !== undefined)
        updateData.selling_price_cost_currency = data.selling_price_cost_currency;
      if (data.selling_price_gbp !== undefined)
        updateData.selling_price_gbp = data.selling_price_gbp;

      // Handle custom_fields separately to ensure complete replacement
      if (data.custom_fields !== undefined) {
        console.log("🔄 Backend: Replacing custom_fields with direct SQL");
        console.log(
          "🔄 Backend: Old custom_fields:",
          existingProductServices[0].custom_fields
        );
        console.log("🔄 Backend: New custom_fields:", data.custom_fields);

        // Use direct SQL to ensure JSON field is completely replaced, not merged
        await withClient(async (client) => {
          const query = `
            UPDATE product_service
            SET custom_fields = $1, updated_at = NOW()
            WHERE id = $2
          `;
          await client.query(query, [JSON.stringify(data.custom_fields), id]);
        });

        console.log("🔄 Backend: Custom fields SQL update completed");
      }

      // Update other fields using the ORM
      if (Object.keys(updateData).length > 0) {
        await this.updateProductServices([{ id, ...updateData }]);
      }

      // Handle tags update if provided
      if (data.tag_ids !== undefined) {
        // Remove existing tags
        const existingTags = await this.listProductServiceTags({
          product_service_id: id,
        });

        if (existingTags.length > 0) {
          const existingTagIds = existingTags.map((tag) => tag.id);
          await this.deleteProductServiceTags(existingTagIds);
        }

        // Add new tags
        if (data.tag_ids.length > 0) {
          for (const tagId of data.tag_ids) {
            // Verify tag exists
            const tags = await this.listTags({ id: tagId });
            if (tags.length === 0) {
              throw new MedusaError(
                MedusaError.Types.NOT_FOUND,
                `Tag with ID ${tagId} not found`
              );
            }

            // Create the junction record
            await this.createProductServiceTags([
              {
                product_service_id: id,
                tag_id: tagId,
              },
            ]);
          }
        }
      }

      // Handle custom fields update if provided (custom_fields is now a Record<string, any>)
      // Note: Custom fields are now stored directly in the product_service table as JSON
      // No need to manage separate junction records

      // Create cost history if base_cost changed
      if (shouldCreateHistory) {
        try {
          await this.createProductServiceCostHistory(
            {
              product_service_id: id,
              previous_cost: currentProductService.base_cost,
              new_cost: data.base_cost,
              change_reason:
                data.change_reason || "Updated via admin interface",
              changed_by_user_id: data.updated_by,
            },
            true
          ); // Skip validation since we already know the product service exists
        } catch (historyError) {
          console.warn("Failed to create cost history entry:", historyError);
          // Don't fail the update if history creation fails
        }
      }

      // Return the updated product/service with relations
      return await this.retrieveProductService(id);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update product/service: ${error.message}`
      );
    }
  }

  async checkProductServiceReferences(id: string): Promise<{
    hasSupplierOfferings: boolean;
    hasProductServiceSuppliers: boolean;
    offeringCount: number;
    supplierCount: number;
  }> {
    try {
      // Check supplier offerings
      const supplierOfferings = await this.listSupplierOfferingsWithFilters({
        product_service_id: id,
      });

      // Check product service suppliers
      const productServiceSuppliers =
        await this.listProductServiceSuppliersWithFilters({
          product_service_id: id,
        });

      return {
        hasSupplierOfferings: supplierOfferings.data.length > 0,
        hasProductServiceSuppliers: productServiceSuppliers.data.length > 0,
        offeringCount: supplierOfferings.data.length,
        supplierCount: productServiceSuppliers.data.length,
      };
    } catch (error) {
      console.error("Error checking product service references:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to check product/service references: ${error.message}`
      );
    }
  }

  async deleteProductService(id: string): Promise<void> {
    try {
      // Check for references before deletion
      const references = await this.checkProductServiceReferences(id);

      if (
        references.hasSupplierOfferings ||
        references.hasProductServiceSuppliers
      ) {
        const errorMessages = [];
        if (references.hasSupplierOfferings) {
          errorMessages.push(
            `${references.offeringCount} supplier offering(s)`
          );
        }
        if (references.hasProductServiceSuppliers) {
          errorMessages.push(`${references.supplierCount} supplier link(s)`);
        }

        throw new MedusaError(
          MedusaError.Types.NOT_ALLOWED,
          `Cannot delete product/service as it is being used by ${errorMessages.join(
            " and "
          )}`
        );
      }

      // Use the generated deleteProductServices method (plural)
      await this.deleteProductServices([id]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete product/service: ${error.message}`
      );
    }
  }

  async listProductServicesWithFilters(
    filters: ProductServiceFilters = {},
    options: { skip?: number; take?: number } = {}
  ): Promise<ProductServiceOutput[]> {
    try {
      const { skip = 0, take = 20 } = options;

      // Build filter conditions
      const whereConditions: any = {};

      if (filters.id) {
        whereConditions.id = filters.id;
      }

      if (filters.name) {
        whereConditions.name = { $like: `%${filters.name}%` };
      }

      // Handle search parameter (case-insensitive search across name and description)
      if (filters.search) {
        whereConditions.$or = [
          { name: { $ilike: `%${filters.search}%` } },
          { description: { $ilike: `%${filters.search}%` } }
        ];
      }

      if (filters.type) {
        whereConditions.type = filters.type;
      }

      if (filters.status) {
        whereConditions.status = filters.status;
      }

      if (filters.category_id) {
        whereConditions.category_id = filters.category_id;
      }

      if (filters.unit_type_id) {
        whereConditions.unit_type_id = filters.unit_type_id;
      }

      if (filters.service_level) {
        whereConditions.service_level = filters.service_level;
      }

      if (filters.hotel_id) {
        whereConditions.hotel_id = filters.hotel_id;
      }

      if (filters.destination_id) {
        whereConditions.destination_id = filters.destination_id;
      }

      // Use the generated listProductServices method from MedusaService
      const data = await this.listProductServices(whereConditions, {
        skip,
        take,
        relations: ["category", "unit_type", "tags", "custom_fields"],
      });

      // Manually populate the actual tags for each product service
      const enrichedData = await Promise.all(
        data.map(async (productService: any) => {
          if (productService.tags && productService.tags.length > 0) {
            // Get the actual tag entities through the junction table
            const tagIds = productService.tags.map(
              (junction: any) => junction.tag_id
            );

            // Fetch each tag individually since listTags might not support $in
            const actualTags = [];
            for (const tagId of tagIds) {
              try {
                const tagResult = await this.listTags({ id: tagId });
                if (tagResult.length > 0) {
                  actualTags.push(tagResult[0]);
                }
              } catch (error) {
                console.warn(`Failed to fetch tag ${tagId}:`, error);
              }
            }

            // Replace the junction records with actual tag entities
            productService.tags = actualTags;
          } else {
            productService.tags = [];
          }

          // Format dates to avoid timezone issues
          return this.formatProductServiceDates(productService);
        })
      );

      return enrichedData as ProductServiceOutput[];
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list products/services: ${error.message}`
      );
    }
  }

  // Category Management Methods
  // Using the create method from MedusaService instead of createCategories
  async createCategory(data: CreateCategoryInput): Promise<CategoryOutput> {
    try {
      if (!data.name) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Name is required"
        );
      }

      // Check if category with same name already exists
      try {
        const existingCategories = await this.listCategories({
          name: data.name,
        });

        if (existingCategories.length > 0) {
          throw new MedusaError(
            MedusaError.Types.DUPLICATE_ERROR,
            `Category with name "${data.name}" already exists`
          );
        }
      } catch (error) {
        // If it's our duplicate error, re-throw it
        if (
          error instanceof MedusaError &&
          error.type === MedusaError.Types.DUPLICATE_ERROR
        ) {
          throw error;
        }
        // Otherwise, continue with creation (the list query might have failed for other reasons)
        console.warn("Could not check for existing category:", error.message);
      }

      // Try different method names to find the correct one
      let category;

      // First try createCategories (plural) - this is the most likely pattern
      if (typeof this.createCategories === "function") {
        console.log("Using createCategories method");
        const categories = await this.createCategories([
          {
            name: data.name,
            description: data.description,
            category_type: data.category_type || "Both",
            icon: data.icon,
            dynamic_field_schema: data.dynamic_field_schema,
            is_active: data.is_active !== false,
          },
        ]);
        category = categories[0];
      } else {
        // Fallback: try to find any create method for Category
        console.log("createCategories not available, trying alternatives...");

        // Check what methods are available
        const methods = Object.getOwnPropertyNames(
          Object.getPrototypeOf(this)
        ).filter((name) => typeof this[name] === "function");
        console.log(
          "Available methods:",
          methods.filter((m) => m.includes("create") || m.includes("Category"))
        );

        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "No suitable create method found for Category"
        );
      }

      return category as CategoryOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      // Check for database constraint violations
      if (
        error.message &&
        error.message.includes("duplicate key value violates unique constraint")
      ) {
        throw new MedusaError(
          MedusaError.Types.DUPLICATE_ERROR,
          `Category with name "${data.name}" already exists`
        );
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create category: ${error.message}`
      );
    }
  }

  // retrieveCategory method is auto-generated by MedusaService
  // Commenting out custom implementation to use generated method
  /*
  async retrieveCategory(id: string): Promise<CategoryOutput> {
    try {
      const categories = await this.listCategories({ id });

      if (categories.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Category with ID ${id} not found`
        );
      }

      return categories[0] as CategoryOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve category: ${error.message}`
      );
    }
  }
  */

  async updateCategory(
    id: string,
    data: UpdateCategoryInput
  ): Promise<CategoryOutput> {
    try {
      const existingCategories = await this.listCategories({ id });
      if (existingCategories.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Category with ID ${id} not found`
        );
      }

      const updateData: any = { id };
      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined)
        updateData.description = data.description;
      if (data.category_type !== undefined)
        updateData.category_type = data.category_type;
      if (data.icon !== undefined) updateData.icon = data.icon;
      if (data.dynamic_field_schema !== undefined)
        updateData.dynamic_field_schema = data.dynamic_field_schema;
      if (data.is_active !== undefined) updateData.is_active = data.is_active;

      await this.updateCategories([updateData]);

      return await this.retrieveCategory(id);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update category: ${error.message}`
      );
    }
  }

  async deleteCategory(id: string): Promise<void> {
    try {
      // Check if category is being used by any product/service
      const productServices = await this.listProductServicesWithFilters({
        category_id: id,
      });

      if (productServices.length > 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_ALLOWED,
          `Cannot delete category as it is being used by ${productServices.length} product(s)/service(s)`
        );
      }

      // Use the generated deleteCategories method (plural)
      await this.deleteCategories([id]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete category: ${error.message}`
      );
    }
  }

  // Remove custom listCategories implementation to use generated method
  // The MedusaService should generate this method automatically

  // Unit Type Management Methods
  async createUnitType(data: CreateUnitTypeInput): Promise<UnitTypeOutput> {
    try {
      if (!data.name) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Name is required"
        );
      }

      // Check if unit type with same name already exists
      try {
        const existingUnitTypes = await this.listUnitTypes({
          name: data.name,
        });

        if (existingUnitTypes.length > 0) {
          throw new MedusaError(
            MedusaError.Types.DUPLICATE_ERROR,
            `Unit type with name "${data.name}" already exists`
          );
        }
      } catch (error) {
        // If it's our duplicate error, re-throw it
        if (
          error instanceof MedusaError &&
          error.type === MedusaError.Types.DUPLICATE_ERROR
        ) {
          throw error;
        }
        // Otherwise, continue with creation (the list query might have failed for other reasons)
        console.warn("Could not check for existing unit type:", error.message);
      }

      // Use createUnitTypes (plural) method
      if (typeof this.createUnitTypes === "function") {
        const unitTypes = await this.createUnitTypes([
          {
            name: data.name,
            description: data.description,
            is_active: data.is_active !== false,
          },
        ]);
        return unitTypes[0] as UnitTypeOutput;
      } else {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "createUnitTypes method not available"
        );
      }
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      // Check for database constraint violations
      if (
        error.message &&
        error.message.includes("duplicate key value violates unique constraint")
      ) {
        throw new MedusaError(
          MedusaError.Types.DUPLICATE_ERROR,
          `Unit type with name "${data.name}" already exists`
        );
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create unit type: ${error.message}`
      );
    }
  }

  async retrieveUnitType(id: string): Promise<UnitTypeOutput> {
    try {
      const unitTypes = await this.listUnitTypes({ id });

      if (unitTypes.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Unit Type with ID ${id} not found`
        );
      }

      return unitTypes[0] as UnitTypeOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve unit type: ${error.message}`
      );
    }
  }

  async updateUnitType(
    id: string,
    data: UpdateUnitTypeInput
  ): Promise<UnitTypeOutput> {
    try {
      const existingUnitTypes = await this.listUnitTypes({ id });
      if (existingUnitTypes.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Unit Type with ID ${id} not found`
        );
      }

      // Check if unit type with same name already exists (excluding current unit type)
      if (data.name !== undefined) {
        try {
          const duplicateUnitTypes = await this.listUnitTypes({
            name: data.name,
          });

          // Filter out the current unit type being updated
          const otherUnitTypes = duplicateUnitTypes.filter(
            (ut) => ut.id !== id
          );

          if (otherUnitTypes.length > 0) {
            throw new MedusaError(
              MedusaError.Types.DUPLICATE_ERROR,
              `Unit type with name "${data.name}" already exists`
            );
          }
        } catch (error) {
          // If it's our duplicate error, re-throw it
          if (
            error instanceof MedusaError &&
            error.type === MedusaError.Types.DUPLICATE_ERROR
          ) {
            throw error;
          }
          // Otherwise, continue with update (the list query might have failed for other reasons)
          console.warn(
            "Could not check for existing unit type:",
            error.message
          );
        }
      }

      // Check if trying to deactivate a unit type that is currently being used
      if (data.is_active === false) {
        const currentUnitType = existingUnitTypes[0];

        // Only check if the unit type is currently active (to avoid unnecessary checks)
        if (currentUnitType.is_active) {
          try {
            const productServices = await this.listProductServicesWithFilters({
              unit_type_id: id,
            });

            if (productServices.length > 0) {
              throw new MedusaError(
                MedusaError.Types.NOT_ALLOWED,
                `Cannot deactivate unit type as it is being used by ${productServices.length} product(s)/service(s). Please update those items to use a different unit type first.`
              );
            }
          } catch (error) {
            // If it's our NOT_ALLOWED error, re-throw it
            if (
              error instanceof MedusaError &&
              error.type === MedusaError.Types.NOT_ALLOWED
            ) {
              throw error;
            }
            // Otherwise, continue with update (the list query might have failed for other reasons)
            console.warn("Could not check for unit type usage:", error.message);
          }
        }
      }

      const updateData: any = { id };
      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined)
        updateData.description = data.description;
      if (data.is_active !== undefined) updateData.is_active = data.is_active;

      await this.updateUnitTypes([updateData]);

      return await this.retrieveUnitType(id);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      // Check for database constraint violations
      if (
        error.message &&
        error.message.includes("duplicate key value violates unique constraint")
      ) {
        throw new MedusaError(
          MedusaError.Types.DUPLICATE_ERROR,
          `Unit type with name "${data.name}" already exists`
        );
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update unit type: ${error.message}`
      );
    }
  }

  async deleteUnitType(id: string): Promise<void> {
    try {
      // Check if unit type is being used by any product/service
      const productServices = await this.listProductServicesWithFilters({
        unit_type_id: id,
      });

      if (productServices.length > 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_ALLOWED,
          `Cannot delete unit type as it is being used by ${productServices.length} product(s)/service(s)`
        );
      }

      // Use the generated deleteUnitTypes method (plural)
      await this.deleteUnitTypes([id]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete unit type: ${error.message}`
      );
    }
  }

  // Remove custom listUnitTypes implementation to use generated method

  // Tag Management Methods
  async createTag(data: CreateTagInput): Promise<TagOutput> {
    try {
      if (!data.name) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Name is required"
        );
      }

      // Use createTags (plural) method
      if (typeof this.createTags === "function") {
        const tags = await this.createTags([
          {
            name: data.name,
            color: data.color,
            is_active: data.is_active !== false,
          },
        ]);
        return tags[0] as TagOutput;
      } else {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "createTags method not available"
        );
      }
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create tag: ${error.message}`
      );
    }
  }

  async retrieveTag(id: string): Promise<TagOutput> {
    try {
      const tags = await this.listTags({ id });

      if (tags.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Tag with ID ${id} not found`
        );
      }

      return tags[0] as TagOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve tag: ${error.message}`
      );
    }
  }

  async updateTag(id: string, data: UpdateTagInput): Promise<TagOutput> {
    try {
      const existingTags = await this.listTags({ id });
      if (existingTags.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Tag with ID ${id} not found`
        );
      }

      const updateData: any = { id };
      if (data.name !== undefined) updateData.name = data.name;
      if (data.color !== undefined) updateData.color = data.color;
      if (data.is_active !== undefined) updateData.is_active = data.is_active;

      await this.updateTags([updateData]);

      return await this.retrieveTag(id);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update tag: ${error.message}`
      );
    }
  }

  async deleteTag(id: string): Promise<void> {
    try {
      // Remove tag associations first
      const tagAssociations = await this.listProductServiceTags({
        tag_id: id,
      });

      if (tagAssociations.length > 0) {
        const associationIds = tagAssociations.map((assoc) => assoc.id);
        await this.deleteProductServiceTags(associationIds);
      }

      // Use the generated deleteTags method (plural)
      await this.deleteTags([id]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete tag: ${error.message}`
      );
    }
  }

  // Remove custom listTags implementation to use generated method

  // Custom Field Management Methods
  async createCustomField(
    data: CreateCustomFieldInput
  ): Promise<CustomFieldOutput> {
    try {
      if (!data.name) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Name is required"
        );
      }

      if (
        !data.field_type ||
        !["dropdown", "text", "number", "date", "time", "time-range", "date-range", "boolean"].includes(
          data.field_type
        )
      ) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Field type must be one of: dropdown, text, number, date, time, time-range, date-range, boolean"
        );
      }

      const customFields = await this.createCustomFields([
        {
          name: data.name,
          field_type: data.field_type,
          options: data.options,
          is_required: data.is_required || false,
          default_value: data.default_value,
          validation_rules: data.validation_rules,
          field_context: data.field_context || "supplier", // Default to supplier for backward compatibility
          is_active: data.is_active !== false,
        },
      ]);

      const customField = customFields[0];

      return customField as CustomFieldOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create custom field: ${error.message}`
      );
    }
  }

  async retrieveCustomField(id: string): Promise<CustomFieldOutput> {
    try {
      const customFields = await this.listCustomFields({ id });

      if (customFields.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Custom Field with ID ${id} not found`
        );
      }

      return customFields[0] as CustomFieldOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve custom field: ${error.message}`
      );
    }
  }

  async updateCustomField(
    id: string,
    data: UpdateCustomFieldInput
  ): Promise<CustomFieldOutput> {
    try {
      const existingCustomFields = await this.listCustomFields({ id });
      if (existingCustomFields.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Custom Field with ID ${id} not found`
        );
      }

      if (
        data.field_type &&
        !["dropdown", "text", "number", "date", "boolean"].includes(
          data.field_type
        )
      ) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Field type must be one of: dropdown, text, number, date, boolean"
        );
      }

      const updateData: any = { id };
      if (data.name !== undefined) updateData.name = data.name;
      if (data.field_type !== undefined)
        updateData.field_type = data.field_type;
      if (data.options !== undefined) updateData.options = data.options;
      if (data.is_required !== undefined)
        updateData.is_required = data.is_required;
      if (data.default_value !== undefined)
        updateData.default_value = data.default_value;
      if (data.validation_rules !== undefined)
        updateData.validation_rules = data.validation_rules;
      if (data.is_active !== undefined) updateData.is_active = data.is_active;

      await this.updateCustomFields([updateData]);

      return await this.retrieveCustomField(id);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update custom field: ${error.message}`
      );
    }
  }

  async deleteCustomField(id: string): Promise<void> {
    try {
      // Remove custom field associations first
      const customFieldAssociations = await this.listProductServiceCustomFields(
        {
          custom_field_id: id,
        }
      );

      if (customFieldAssociations.length > 0) {
        const associationIds = customFieldAssociations.map((assoc) => assoc.id);
        await this.deleteProductServiceCustomFields(associationIds);
      }

      // Use the generated deleteCustomFields method (plural)
      await this.deleteCustomFields([id]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete custom field: ${error.message}`
      );
    }
  }

  async listCustomFields(
    filters: CustomFieldFilters = {},
    options: { skip?: number; take?: number } = {}
  ): Promise<CustomFieldListResponse> {
    try {
      const { skip = 0, take = 20 } = options;

      const whereConditions: any = {};

      if (filters.name) {
        whereConditions.name = { $like: `%${filters.name}%` };
      }

      if (filters.field_type) {
        whereConditions.field_type = filters.field_type;
      }

      if (filters.field_context) {
        whereConditions.field_context = filters.field_context;
      }

      if (filters.is_active !== undefined) {
        whereConditions.is_active = filters.is_active;
      }

      // Use the base class listCustomFields method (generated)
      const data = await super.listCustomFields(whereConditions, {
        skip,
        take,
      });
      const count = data.length;

      return {
        data: data as CustomFieldOutput[],
        count,
        limit: take,
        offset: skip,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list custom fields: ${error.message}`
      );
    }
  }

  // ProductServiceSupplier methods - using direct database queries
  async listProductServiceSuppliersWithFilters(
    filters: ProductServiceSupplierFilters = {}
  ): Promise<ProductServiceSupplierListResponse> {
    try {
      console.log(
        "🔍 listProductServiceSuppliersWithFilters called with filters:",
        filters
      );

      const { limit = 25, offset = 0, ...whereFilters } = filters;

      // Build SQL query conditions
      const conditions: string[] = ["deleted_at IS NULL"];
      const params: any[] = [];
      let paramIndex = 1;

      if (whereFilters.id) {
        conditions.push(`id = $${paramIndex}`);
        params.push(whereFilters.id);
        paramIndex++;
      }
      if (whereFilters.product_service_id) {
        conditions.push(`product_service_id = $${paramIndex}`);
        params.push(whereFilters.product_service_id);
        paramIndex++;
      }
      if (whereFilters.supplier_id) {
        conditions.push(`supplier_id = $${paramIndex}`);
        params.push(whereFilters.supplier_id);
        paramIndex++;
      }
      if (whereFilters.is_active !== undefined) {
        conditions.push(`is_active = $${paramIndex}`);
        params.push(whereFilters.is_active);
        paramIndex++;
      }
      if (whereFilters.is_preferred !== undefined) {
        conditions.push(`is_preferred = $${paramIndex}`);
        params.push(whereFilters.is_preferred);
        paramIndex++;
      }
      if (whereFilters.season) {
        conditions.push(`season = $${paramIndex}`);
        params.push(whereFilters.season);
        paramIndex++;
      }

      const whereClause =
        conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

      console.log("🔍 Built SQL conditions:", whereClause);
      console.log("🔍 Query params:", params);
      console.log("🔍 Query options:", { limit, offset });

      // Use direct SQL query through the database connection
      const query = `
        SELECT * FROM "product_service_supplier"
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      params.push(limit, offset);

      console.log("🔍 Final SQL query:", query);

      // Execute the query using the database connection from utils
      const productServiceSuppliers = await withClient(async (client) => {
        const result = await client.query(query, params);
        return result.rows;
      });

      console.log("🔍 Raw result from database:", productServiceSuppliers);
      console.log(
        "🔍 Result type:",
        typeof productServiceSuppliers,
        "Is array:",
        Array.isArray(productServiceSuppliers)
      );

      const count = Array.isArray(productServiceSuppliers)
        ? productServiceSuppliers.length
        : 0;

      const result = {
        data: (Array.isArray(productServiceSuppliers)
          ? productServiceSuppliers
          : []) as ProductServiceSupplierOutput[],
        count,
        limit,
        offset,
      };

      console.log("✅ Returning result:", {
        count,
        dataLength: result.data.length,
        limit,
        offset,
      });
      return result;
    } catch (error) {
      console.error(
        "❌ Error in listProductServiceSuppliersWithFilters:",
        error
      );
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list product service suppliers: ${error.message}`
      );
    }
  }

  async getProductServiceSupplier(
    id: string
  ): Promise<ProductServiceSupplierOutput> {
    try {
      const productServiceSuppliers =
        await this.listProductServiceSuppliersWithFilters({ id });

      if (productServiceSuppliers.data.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          "Product service supplier link not found"
        );
      }

      return productServiceSuppliers.data[0] as ProductServiceSupplierOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get product service supplier: ${error.message}`
      );
    }
  }

  async createProductServiceSupplier(
    data: CreateProductServiceSupplierInput
  ): Promise<ProductServiceSupplierOutput> {
    try {
      // Check if the product service exists using generated method
      const productServices = await this.listProductServices({
        id: data.product_service_id,
      });

      if (productServices.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          "Product service not found"
        );
      }

      // Check if this supplier is already linked to this product service
      const existingLinks = await this.listProductServiceSuppliersWithFilters({
        product_service_id: data.product_service_id,
        supplier_id: data.supplier_id,
      });

      if (existingLinks.data.length > 0) {
        throw new MedusaError(
          MedusaError.Types.DUPLICATE_ERROR,
          "This supplier is already linked to this product/service"
        );
      }

      // Create using the generated method
      const productServiceSuppliers = await this.createProductServiceSuppliers([
        {
          ...data,
          currency_code: data.currency_code || "CHF",
          is_active: data.is_active ?? true,
          is_preferred: data.is_preferred ?? false,
        },
      ]);

      return productServiceSuppliers[0] as ProductServiceSupplierOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create product service supplier: ${error.message}`
      );
    }
  }

  async updateProductServiceSupplier(
    id: string,
    data: UpdateProductServiceSupplierInput
  ): Promise<ProductServiceSupplierOutput> {
    try {
      // Check if the product service supplier exists
      const existingSuppliers =
        await this.listProductServiceSuppliersWithFilters({ id });

      if (existingSuppliers.data.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          "Product service supplier link not found"
        );
      }

      // Update using the generated method
      const updatedSuppliers = await this.updateProductServiceSuppliers([
        {
          id,
          ...data,
        },
      ]);

      return updatedSuppliers[0] as ProductServiceSupplierOutput;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update product service supplier: ${error.message}`
      );
    }
  }

  async deleteProductServiceSupplier(
    id: string
  ): Promise<{ deleted: boolean; id: string }> {
    try {
      // Check if the product service supplier exists
      const existingSuppliers =
        await this.listProductServiceSuppliersWithFilters({ id });

      if (existingSuppliers.data.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          "Product service supplier link not found"
        );
      }

      // Delete using the generated method
      await this.deleteProductServiceSuppliers([id]);

      return { deleted: true, id };
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete product service supplier: ${error.message}`
      );
    }
  }

  async getProductServiceSuppliersForProduct(
    productServiceId: string,
    filters: Omit<ProductServiceSupplierFilters, "product_service_id"> = {}
  ): Promise<ProductServiceSupplierListResponse> {
    return this.listProductServiceSuppliersWithFilters({
      ...filters,
      product_service_id: productServiceId,
    });
  }

  // ===== SUPPLIER OFFERING METHODS =====

  async createSupplierOffering(
    data: CreateSupplierOfferingInput
  ): Promise<SupplierOfferingOutput> {
    try {
      // Check if table exists first
      try {
        await this.listSupplierOfferings({}, { take: 1 });
      } catch (error) {
        if (
          error.message &&
          error.message.includes(
            'relation "public.supplier_offering" does not exist'
          )
        ) {
          throw new MedusaError(
            MedusaError.Types.DB_ERROR,
            `Supplier offering table does not exist. Please create the table first.`
          );
        }
        throw error;
      }

      // Validate that product service exists
      const productServices = await this.listProductServices({
        id: data.product_service_id,
      });

      if (!productServices || productServices.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product/Service with id ${data.product_service_id} not found`
        );
      }

      // Get the product/service to access its category
      const productService = productServices[0];

      // Get the category to access dynamic field schema
      const category = await this.retrieveCategory(productService.category_id);
      if (!category) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Category with id ${productService.category_id} not found`
        );
      }

      // Implement comprehensive uniqueness validation
      const dynamicFieldSchemaForCreate = Array.isArray(
        category.dynamic_field_schema
      )
        ? category.dynamic_field_schema
        : [];
      await this.validateSupplierOfferingUniqueness(
        data.supplier_id,
        data.product_service_id,
        data.active_from,
        data.active_to,
        data.custom_fields || {},
        dynamicFieldSchemaForCreate
      );

      // Inherit locked custom fields from product/service
      const finalCustomFields = { ...(data.custom_fields || {}) };

      // Get fields that are used in supplier offerings
      const offeringFields = dynamicFieldSchemaForCreate.filter(
        (field: any) => field.used_in_supplier_offering
      );

      // Inherit values for locked fields from product/service
      offeringFields.forEach((field: any) => {
        if (
          field.locked_in_offerings &&
          productService.custom_fields?.[field.key] !== undefined
        ) {
          // Inherit value from product/service for locked fields
          finalCustomFields[field.key] =
            productService.custom_fields[field.key];
        }
      });

      // Handle date conversion while preserving null values for open-ended offerings
      // Convert incoming decimals to cents for DB storage per requirement (only touch monetary fields)
      const calculatedSellingPriceSellingCurrency =
        (data as any).calculated_selling_price_selling_currency ??
        data.selling_price_selling_currency ??
        data.selling_price;

      const offeringData: any = {
        ...data,
        custom_fields: finalCustomFields,

        // Persist in CENTS:
        // - gross_price = round(gross_price * 100)
        // - net_cost = round(net_cost * 100)
        // - selling_price = round(calculated_selling_price_selling_currency * 100)
        gross_price: this.toCents(data.gross_price),
        net_cost: this.toCents(data.net_cost),
        // selling_price rule (store calculated_selling_price_selling_currency)
        selling_price: this.toCents(calculatedSellingPriceSellingCurrency),
        // also persist selling_price_selling_currency in cents if provided
        selling_price_selling_currency: this.toCents(
          data.selling_price_selling_currency
        ),

        // Dates (unchanged)
        active_from: data.active_from
          ? typeof data.active_from === "string"
            ? new Date(data.active_from)
            : data.active_from
          : data.active_from,
        active_to: data.active_to
          ? typeof data.active_to === "string"
            ? new Date(data.active_to)
            : data.active_to
          : data.active_to,
      };

      console.log("🔍 Service - Creating offering with user-provided values (no calculations):", {
        commission: data.commission,
        gross_price: data.gross_price,
        net_cost: data.net_cost,
        margin_rate: data.margin_rate,
        selling_price: data.selling_price,
        selling_price_selling_currency: data.selling_price_selling_currency,
        exchange_rate: data.exchange_rate,
        exchange_rate_date: data.exchange_rate_date
      });

      // Use the generated createSupplierOfferings method
      const offerings = await this.createSupplierOfferings([offeringData]);
      return await this.transformSupplierOfferingOutput(offerings[0]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error creating supplier offering:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create supplier offering: ${error.message}`
      );
    }
  }

  async updateSupplierOffering(
    id: string,
    data: UpdateSupplierOfferingInput
  ): Promise<SupplierOfferingOutput> {
    try {
      // Check if offering exists
      const existingOfferings = await this.listSupplierOfferingsWithFilters({
        id,
      });

      if (
        !existingOfferings ||
        !existingOfferings.data ||
        existingOfferings.data.length === 0
      ) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Supplier offering with id ${id} not found`
        );
      }

      const offering = existingOfferings.data[0];

      // Get the product/service to access its category for validation
      const productServices = await this.listProductServices({
        id: offering.product_service_id,
      });
      if (!productServices || productServices.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product/Service with id ${offering.product_service_id} not found`
        );
      }

      const productService = productServices[0];

      // Get the category to access dynamic field schema
      const category = await this.retrieveCategory(productService.category_id);
      if (!category) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Category with id ${productService.category_id} not found`
        );
      }

      // Validate locked fields cannot be modified
      if (data.custom_fields) {
        const dynamicFieldSchema = Array.isArray(category.dynamic_field_schema)
          ? category.dynamic_field_schema
          : [];

        // Determine which product service to use for validation
        let productServiceForValidation = offering.product_service;

        // If product_service_id is being changed, fetch the new product service
        if (data.product_service_id && data.product_service_id !== offering.product_service_id) {
          productServiceForValidation = await this.retrieveProductService(data.product_service_id);
        } else {
          console.log(`🔄 Using EXISTING Product/Service for validation: ${offering.product_service_id}`);
        }

        await this.validateLockedFieldsNotModified(
          data.custom_fields,
          offering.custom_fields || {},
          dynamicFieldSchema,
          productServiceForValidation
        );
      }

      // Check for uniqueness conflicts if key fields are being updated
      if (
        data.product_service_id !== undefined ||
        data.supplier_id !== undefined ||
        data.active_from !== undefined ||
        data.active_to !== undefined ||
        data.custom_fields !== undefined
      ) {
        const updatedData = {
          product_service_id:
            data.product_service_id !== undefined
              ? data.product_service_id
              : offering.product_service_id,
          supplier_id:
            data.supplier_id !== undefined
              ? data.supplier_id
              : offering.supplier_id,
          active_from:
            data.active_from !== undefined
              ? data.active_from
              : offering.active_from,
          // Handle active_to specially to support open-ended periods (null)
          active_to:
            data.active_to !== undefined ? data.active_to : offering.active_to,
          custom_fields:
            data.custom_fields !== undefined
              ? data.custom_fields
              : offering.custom_fields || {},
        };

        // Validate uniqueness (excluding current offering)
        const dynamicFieldSchemaForUpdate = Array.isArray(
          category.dynamic_field_schema
        )
          ? category.dynamic_field_schema
          : [];
        await this.validateSupplierOfferingUniqueness(
          updatedData.supplier_id,
          updatedData.product_service_id,
          updatedData.active_from,
          updatedData.active_to,
          updatedData.custom_fields,
          dynamicFieldSchemaForUpdate,
          id // Exclude current offering from uniqueness check
        );
      }

      // Track pricing changes for history
      const shouldCreateHistory =
        (data.cost !== undefined && data.cost !== offering.cost) ||
        (data.commission !== undefined &&
          data.commission !== offering.commission) ||
        (data.gross_price !== undefined &&
          data.gross_price !== offering.gross_price) ||
        (data.supplier_price !== undefined &&
          data.supplier_price !== offering.supplier_price) ||
        (data.net_price !== undefined &&
          data.net_price !== offering.net_price) ||
        (data.margin_rate !== undefined &&
          data.margin_rate !== offering.margin_rate) ||
        (data.selling_price !== undefined &&
          data.selling_price !== offering.selling_price) ||
        (data.custom_prices !== undefined &&
          JSON.stringify(data.custom_prices) !==
            JSON.stringify(offering.custom_prices)) ||
        (data.currency !== undefined && data.currency !== offering.currency);

      if (shouldCreateHistory) {
        try {
          // Create legacy cost history for backward compatibility
          await this.createSupplierOfferingCostHistory({
            supplier_offering_id: id,
            previous_cost: offering.cost,
            new_cost: data.cost !== undefined ? data.cost : offering.cost,
            previous_currency: offering.currency,
            new_currency:
              data.currency !== undefined ? data.currency : offering.currency,
            change_reason: data.change_reason || "Updated via admin interface",
            changed_by_user_id: data.updated_by,
          });

          // TODO: Create enhanced pricing history entry
          // This would use the new SupplierOfferingPricingHistory model
          // when the migration is run and the table is created
        } catch (historyError) {
          console.warn("Failed to create pricing history entry:", historyError);
          // Don't fail the update if history creation fails
        }
      }

      // Inherit locked custom fields from product/service if custom_fields are being updated
      if (data.custom_fields !== undefined) {
        const finalCustomFields = { ...(data.custom_fields || {}) };

        // Determine which product service to use for locked field inheritance
        let productServiceForInheritance = productService;

        // If product_service_id is being changed, fetch the new product service
        if (data.product_service_id && data.product_service_id !== offering.product_service_id) {
          const newProductServices = await this.listProductServices({
            id: data.product_service_id,
          });
          if (newProductServices && newProductServices.length > 0) {
            productServiceForInheritance = newProductServices[0];
          }
        } else {
          console.log(`🔄 Using EXISTING Product/Service for locked field inheritance: ${offering.product_service_id}`);
        }

        // Get fields that are used in supplier offerings
        const dynamicFieldSchema = Array.isArray(category.dynamic_field_schema)
          ? category.dynamic_field_schema
          : [];
        const offeringFields = dynamicFieldSchema.filter(
          (field: any) => field.used_in_supplier_offering
        );

        // Inherit values for locked fields from the correct product/service
        offeringFields.forEach((field: any) => {
          if (
            field.locked_in_offerings &&
            productServiceForInheritance.custom_fields?.[field.key] !== undefined
          ) {
            // Inherit value from the correct product/service for locked fields
            finalCustomFields[field.key] =
              productServiceForInheritance.custom_fields[field.key];
            console.log(`🔒 Inheriting locked field ${field.key}:`, productServiceForInheritance.custom_fields[field.key]);
          }
        });

        // Update the custom_fields with inherited values
        data.custom_fields = finalCustomFields;
      }

      // Use direct SQL update to avoid issues with generated methods
      await withClient(async (client) => {
        const updateFields: string[] = [];
        const updateValues: any[] = [];
        let paramIndex = 1;

        // Build dynamic update query based on provided data
        if (data.product_service_id !== undefined) {
          updateFields.push(`product_service_id = $${paramIndex++}`);
          updateValues.push(data.product_service_id);
        }
        if (data.supplier_id !== undefined) {
          updateFields.push(`supplier_id = $${paramIndex++}`);
          updateValues.push(data.supplier_id);
        }
        if (data.active_from !== undefined) {
          updateFields.push(`active_from = $${paramIndex++}`);
          updateValues.push(data.active_from);
        }
        if (data.active_to !== undefined) {
          updateFields.push(`active_to = $${paramIndex++}`);
          updateValues.push(data.active_to);
        }
        if (data.availability_notes !== undefined) {
          updateFields.push(`availability_notes = $${paramIndex++}`);
          updateValues.push(data.availability_notes);
        }
        // Legacy cost field (leave as-is)
        if ((data as any).cost !== undefined) {
          updateFields.push(`cost = $${paramIndex++}`);
          updateValues.push((data as any).cost);
        }

        // Enhanced pricing fields (MONETARY to cents)
        if (data.commission !== undefined) {
          updateFields.push(`commission = $${paramIndex++}`);
          updateValues.push(data.commission);
        }
        if (data.gross_price !== undefined) {
          updateFields.push(`gross_price = $${paramIndex++}`);
          updateValues.push(this.toCents(data.gross_price));
        }
        if (data.net_cost !== undefined) {
          updateFields.push(`net_cost = $${paramIndex++}`);
          updateValues.push(this.toCents(data.net_cost));
        }
        if ((data as any).net_price !== undefined) {
          updateFields.push(`net_price = $${paramIndex++}`);
          updateValues.push(this.toCents((data as any).net_price));
        }
        if ((data as any).supplier_price !== undefined) {
          updateFields.push(`supplier_price = $${paramIndex++}`);
          updateValues.push(this.toCents((data as any).supplier_price));
        }
        if (data.margin_rate !== undefined) {
          updateFields.push(`margin_rate = $${paramIndex++}`);
          updateValues.push(data.margin_rate);
        }
        if (data.selling_price !== undefined || (data as any).calculated_selling_price_selling_currency !== undefined) {
          // selling_price must store calculated_selling_price_selling_currency * 100
          const calc = (data as any).calculated_selling_price_selling_currency ?? data.selling_price;
          updateFields.push(`selling_price = $${paramIndex++}`);
          updateValues.push(this.toCents(calc));
        }
        if (data.custom_prices !== undefined) {
          updateFields.push(`custom_prices = $${paramIndex++}`);
          updateValues.push(JSON.stringify(data.custom_prices));
        }

        // Currency fields
        if (data.currency !== undefined) {
          updateFields.push(`currency = $${paramIndex++}`);
          updateValues.push(data.currency);
        }
        if (data.currency_override !== undefined) {
          updateFields.push(`currency_override = $${paramIndex++}`);
          updateValues.push(data.currency_override);
        }

        // Selling currency fields
        if (data.selling_currency !== undefined) {
          updateFields.push(`selling_currency = $${paramIndex++}`);
          updateValues.push(data.selling_currency);
        }
        if (data.selling_price_selling_currency !== undefined) {
          updateFields.push(`selling_price_selling_currency = $${paramIndex++}`);
          updateValues.push(this.toCents(data.selling_price_selling_currency));
        }
        if (data.exchange_rate !== undefined) {
          updateFields.push(`exchange_rate = $${paramIndex++}`);
          updateValues.push(data.exchange_rate);
        }
        if (data.exchange_rate_date !== undefined) {
          updateFields.push(`exchange_rate_date = $${paramIndex++}`);
          updateValues.push(data.exchange_rate_date);
        }
        if (data.status !== undefined) {
          updateFields.push(`status = $${paramIndex++}`);
          updateValues.push(data.status);
        }
        if (data.custom_fields !== undefined) {
          updateFields.push(`custom_fields = $${paramIndex++}`);
          updateValues.push(JSON.stringify(data.custom_fields));
        }
        if (data.add_ons !== undefined) {
          updateFields.push(`add_ons = $${paramIndex++}`);
          updateValues.push(JSON.stringify(data.add_ons));
        }
        if (data.updated_by !== undefined) {
          updateFields.push(`updated_by = $${paramIndex++}`);
          updateValues.push(data.updated_by);
        }

        // Always update the updated_at timestamp
        updateFields.push(`updated_at = NOW()`);
        updateValues.push(id);

        const query = `
          UPDATE supplier_offering
          SET ${updateFields.join(", ")}
          WHERE id = $${paramIndex} AND deleted_at IS NULL
        `;

        await client.query(query, updateValues);
      });

      // Fetch and return the updated offering
      const updatedOfferings = await this.listSupplierOfferingsWithFilters({
        id,
      });
      if (!updatedOfferings.data || updatedOfferings.data.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Supplier offering with id ${id} not found after update`
        );
      }

      return await this.transformSupplierOfferingOutput(
        updatedOfferings.data[0]
      );
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error updating supplier offering:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update supplier offering: ${error.message}`
      );
    }
  }

  async deleteSupplierOffering(id: string): Promise<void> {
    try {
      // Check if offering exists
      const existingOfferings = await this.listSupplierOfferingsWithFilters({
        id,
      });

      if (
        !existingOfferings ||
        !existingOfferings.data ||
        existingOfferings.data.length === 0
      ) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Supplier offering with id ${id} not found`
        );
      }

      // Use the generated deleteSupplierOfferings method
      await this.deleteSupplierOfferings([id]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error deleting supplier offering:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete supplier offering: ${error.message}`
      );
    }
  }

  async getSupplierOffering(id: string): Promise<SupplierOfferingOutput> {
    try {
      // Use the generated listSupplierOfferingsWithFilters method with relations
      const offeringsResponse = await this.listSupplierOfferingsWithFilters({
        id,
      });

      if (
        !offeringsResponse ||
        !offeringsResponse.data ||
        offeringsResponse.data.length === 0
      ) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Supplier offering with id ${id} not found`
        );
      }

      return await this.transformSupplierOfferingOutput(
        offeringsResponse.data[0]
      );
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error getting supplier offering:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get supplier offering: ${error.message}`
      );
    }
  }

  async listSupplierOfferingsWithFilters(
    filters: SupplierOfferingFilters = {},
    options: {
      limit?: number;
      offset?: number;
      sort_by?: string;
      sort_order?: string;
    } = {}
  ): Promise<SupplierOfferingListResponse> {
    try {
      const {
        limit = 25,
        offset = 0,
        sort_by = "updated_at",
        sort_order = "desc",
      } = options;

      // Check if the supplier_offering table exists
      try {
        // Build proper database filters
        const whereConditions: any = {};

        if (filters.id) {
          whereConditions.id = filters.id;
        }

        if (filters.supplier_id) {
          whereConditions.supplier_id = filters.supplier_id;
        }

        if (filters.product_service_id) {
          whereConditions.product_service_id = filters.product_service_id;
        }

        if (filters.status) {
          whereConditions.status = filters.status;
        }

        if (filters.created_by) {
          whereConditions.created_by = filters.created_by;
        }

        if (filters.updated_by) {
          whereConditions.updated_by = filters.updated_by;
        }

        // Store date filters for post-processing since complex OR conditions
        // might not work with the current ORM setup
        const dateFilters = {
          bookingStart: filters.active_from,
          bookingEnd: filters.active_to,
        };



        // Build order options for sorting
        const orderOptions: any = {};

        // Map sort_by to actual database fields
        switch (sort_by) {
          case "product_service_name":
            // This will need to be handled after fetching product service data
            orderOptions.updated_at = sort_order.toUpperCase() as
              | "ASC"
              | "DESC";
            break;
          case "validity":
            orderOptions.active_to = sort_order.toUpperCase() as "ASC" | "DESC";
            break;
          case "net_cost":
            orderOptions.net_cost = sort_order.toUpperCase() as "ASC" | "DESC";
            break;
          case "updated_at":
          default:
            orderOptions.updated_at = sort_order.toUpperCase() as
              | "ASC"
              | "DESC";
            break;
        }

        // Use listAndCountSupplierOfferings for proper pagination with total count
        const [offerings, totalCount] =
          await this.listAndCountSupplierOfferings(whereConditions, {
            skip: offset,
            take: limit,
            relations: ["product_service", "product_service.category"],
            order: orderOptions,
          });

        console.log("🔍 Service - Database returned:", {
          offeringsLength: offerings.length,
          totalCount,
          offset,
          limit,
        });

        // Handle category filtering (requires join with product_service)
        let filteredOfferings = offerings;
        let actualCount = totalCount;

        if (filters.category_id) {
          filteredOfferings = offerings.filter(
            (offering: any) =>
              offering.product_service?.category_id === filters.category_id
          );
          actualCount = filteredOfferings.length;
        }

        // Handle date filtering for overlap checking (post-processing)
        if (dateFilters.bookingStart || dateFilters.bookingEnd) {
          try {
            // Ensure dates are properly converted to Date objects
            const bookingStart = dateFilters.bookingStart ? new Date(dateFilters.bookingStart) : null;
            const bookingEnd = dateFilters.bookingEnd ? new Date(dateFilters.bookingEnd) : null;

            // Validate that dates are valid
            if (bookingStart && isNaN(bookingStart.getTime())) {
              console.warn("Invalid bookingStart date:", dateFilters.bookingStart);
              // Skip date filtering if invalid date
            } else if (bookingEnd && isNaN(bookingEnd.getTime())) {
              console.warn("Invalid bookingEnd date:", dateFilters.bookingEnd);
              // Skip date filtering if invalid date
            } else {
              console.log("🔍 Post-processing date filter:", {
                bookingStart,
                bookingEnd,
                offeringsBeforeFilter: filteredOfferings.length,
              });

          filteredOfferings = filteredOfferings.filter((offering: any) => {
            // An offering is available if its validity period overlaps with the booking period
            // Overlap occurs when: offering_start <= booking_end AND offering_end >= booking_start

            const offeringStart = offering.active_from ? new Date(offering.active_from) : null;
            const offeringEnd = offering.active_to ? new Date(offering.active_to) : null;

            // If no booking dates provided, include all offerings
            if (!bookingStart && !bookingEnd) {
              return true;
            }

            // If only booking start provided
            if (bookingStart && !bookingEnd) {
              // Offering must not end before booking starts
              return !offeringEnd || offeringEnd >= bookingStart;
            }

            // If only booking end provided
            if (!bookingStart && bookingEnd) {
              // Offering must not start after booking ends
              return !offeringStart || offeringStart <= bookingEnd;
            }

            // Both booking dates provided - check for overlap
            if (bookingStart && bookingEnd) {
              // Offering is available if:
              // (offering has no end date OR offering ends after booking starts) AND
              // (offering has no start date OR offering starts before booking ends)
              const endsAfterBookingStarts = !offeringEnd || offeringEnd >= bookingStart;
              const startsBeforeBookingEnds = !offeringStart || offeringStart <= bookingEnd;

              return endsAfterBookingStarts && startsBeforeBookingEnds;
            }

            return true;
          });

          actualCount = filteredOfferings.length;

          console.log("🔍 After date filtering:", {
            offeringsAfterFilter: filteredOfferings.length,
            sampleOfferings: filteredOfferings.slice(0, 2).map(o => ({
              id: o.id,
              supplier_name: o.supplier?.name,
              active_from: o.active_from,
              active_to: o.active_to,
            })),
          });
        }
      } catch (error) {
        console.error("Error in date filtering:", error);
        // Continue without date filtering if there's an error
      }
    }

        // Handle search filtering (client-side for now, can be optimized later)
        if (filters.search) {
          const searchTerm = filters.search.toLowerCase();
          filteredOfferings = filteredOfferings.filter(
            (offering: any) =>
              offering.product_service?.name
                ?.toLowerCase()
                .includes(searchTerm) ||
              offering.product_service?.category?.name
                ?.toLowerCase()
                .includes(searchTerm)
          );
          actualCount = filteredOfferings.length;
        }

        // Handle client-side sorting for fields that require related data
        if (["product_service_name"].includes(sort_by)) {
          filteredOfferings.sort((a: any, b: any) => {
            let aValue = "";
            let bValue = "";

            switch (sort_by) {
              case "product_service_name":
                aValue = a.product_service?.name || "";
                bValue = b.product_service?.name || "";
                break;
            }

            const comparison = aValue.localeCompare(bValue);
            return sort_order === "desc" ? -comparison : comparison;
          });
        }

        const transformedOfferings = await Promise.all(
          filteredOfferings.map((offering: any) =>
            this.transformSupplierOfferingOutput(offering)
          )
        );

        console.log("🔍 Service - Final result:", {
          transformedLength: transformedOfferings.length,
          actualCount,
          limit,
          offset,
        });

        return {
          data: transformedOfferings,
          count: actualCount,
          limit,
          offset,
        };
      } catch (error) {
        // If the table doesn't exist, return empty results
        if (
          error.message &&
          error.message.includes(
            'relation "public.supplier_offering" does not exist'
          )
        ) {
          console.warn(
            "supplier_offering table does not exist yet, returning empty results"
          );
          return {
            data: [],
            count: 0,
            limit,
            offset,
          };
        }
        throw error;
      }
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error listing supplier offerings:", error);

      // If it's a table not found error, return empty results gracefully
      if (
        error.message &&
        error.message.includes(
          'relation "public.supplier_offering" does not exist'
        )
      ) {
        console.warn(
          "supplier_offering table does not exist yet, returning empty results"
        );
        return {
          data: [],
          count: 0,
          limit,
          offset,
        };
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list supplier offerings: ${error.message}`
      );
    }
  }

  // Helper method to validate supplier offering uniqueness
  private async validateSupplierOfferingUniqueness(
    supplierId: string,
    productServiceId: string,
    activeFrom?: Date,
    activeTo?: Date,
    customFields: Record<string, any> = {},
    dynamicFieldSchema: DynamicFieldSchema[] = [],
    excludeId?: string
  ): Promise<void> {
    // Get all existing offerings for this supplier and product
    const existingOfferings = await this.listSupplierOfferingsWithFilters({
      product_service_id: productServiceId,
      supplier_id: supplierId,
    });

    if (
      existingOfferings &&
      existingOfferings.data &&
      existingOfferings.data.length > 0
    ) {
      // Check each existing offering for conflicts
      for (const existingOffering of existingOfferings.data) {
        // Skip the current offering if we're updating
        if (excludeId && existingOffering.id === excludeId) {
          continue;
        }

        // Check if this is the "same offering" by comparing all mandatory fields
        const isSameOffering = this.areOfferingsIdentical(
          customFields,
          existingOffering.custom_fields || {},
          dynamicFieldSchema
        );

        // Only check for date overlaps if the offerings are considered identical
        if (isSameOffering) {
          // Prepare date variables for validation
          const newStart = activeFrom ? new Date(activeFrom) : null;
          const newEnd = activeTo ? new Date(activeTo) : null;
          const existingStart = existingOffering.active_from
            ? new Date(existingOffering.active_from)
            : null;
          const existingEnd = existingOffering.active_to
            ? new Date(existingOffering.active_to)
            : null;

          // Check for date range overlaps
          const hasOverlap = this.doDateRangesOverlap(
            newStart,
            newEnd,
            existingStart,
            existingEnd
          );

          if (hasOverlap) {
            const formatDate = (date?: Date | null) =>
              date ? date.toISOString().split("T")[0] : "open-ended";

            const errorMessage = `Date range conflict: An identical offering already exists with overlapping validity period from ${formatDate(
              existingStart
            )} to ${formatDate(existingEnd)}. Offerings with the same mandatory field values cannot have overlapping date ranges.`;

            throw new MedusaError(
              MedusaError.Types.DUPLICATE_ERROR,
              errorMessage
            );
          }
        }
      }
    }
  }

  // Helper method to validate that locked fields are not modified
  private async validateLockedFieldsNotModified(
    newCustomFields: Record<string, any>,
    existingCustomFields: Record<string, any>,
    dynamicFieldSchema: DynamicFieldSchema[],
    productService?: any
  ): Promise<void> {
    const lockedFields = dynamicFieldSchema.filter(
      (field) => field.locked_in_offerings && field.used_in_supplier_offering
    );

    const modifiedLockedFields: string[] = [];

    for (const field of lockedFields) {
      const existingValue = existingCustomFields[field.key];
      const newValue = newCustomFields[field.key];

      // Check if the locked field value has been modified
      if (JSON.stringify(existingValue) !== JSON.stringify(newValue)) {
        // If we have product service data, check if the new value matches the expected value
        if (productService?.custom_fields?.[field.key] !== undefined) {
          const expectedValue = productService.custom_fields[field.key];

          // Allow the update if the new value matches the current product/service value
          if (JSON.stringify(newValue) === JSON.stringify(expectedValue)) {
            continue; // Skip adding to modifiedLockedFields
          }
        }

        modifiedLockedFields.push(field.label || field.key);
      }
    }

    if (modifiedLockedFields.length > 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        `The following fields are locked and cannot be modified in supplier offerings: ${modifiedLockedFields.join(
          ", "
        )}. These fields inherit their values from the product/service configuration.`
      );
    }
  }

  // Helper method to check if two offerings are identical based on mandatory fields
  private areOfferingsIdentical(
    newCustomFields: Record<string, any>,
    existingCustomFields: Record<string, any>,
    dynamicFieldSchema: DynamicFieldSchema[]
  ): boolean {
    // Get mandatory custom fields that are used in supplier offerings
    const mandatoryFields = dynamicFieldSchema.filter(
      (field) =>
        field.used_in_supplier_offering &&
        field.required === true &&
        (!field.field_context || field.field_context === "supplier")
    );

    // Compare each mandatory custom field
    for (const field of mandatoryFields) {
      const newValue = newCustomFields[field.key];
      const existingValue = existingCustomFields[field.key];

      // Normalize values for comparison
      const normalizedNewValue = this.normalizeFieldValue(newValue);
      const normalizedExistingValue = this.normalizeFieldValue(existingValue);

      // If values don't match, offerings are different
      if (!this.areFieldValuesEqual(normalizedNewValue, normalizedExistingValue)) {
        return false;
      }
    }

    // All mandatory fields match - offerings are identical
    return true;
  }

  // Helper method to normalize field values for comparison
  private normalizeFieldValue(value: any): any {
    if (value === null || value === undefined || value === "") {
      return null;
    }

    // Handle arrays by sorting them for consistent comparison
    if (Array.isArray(value)) {
      return value.slice().sort();
    }

    // Handle strings by trimming whitespace
    if (typeof value === "string") {
      return value.trim();
    }

    return value;
  }

  // Helper method to compare field values
  private areFieldValuesEqual(value1: any, value2: any): boolean {
    // Both null/undefined/empty
    if (value1 === null && value2 === null) {
      return true;
    }

    // One null, one not
    if (value1 === null || value2 === null) {
      return false;
    }

    // Array comparison
    if (Array.isArray(value1) && Array.isArray(value2)) {
      if (value1.length !== value2.length) {
        return false;
      }
      return value1.every((item, index) => item === value2[index]);
    }

    // Direct comparison for other types
    return value1 === value2;
  }

  // Helper method to check if two date ranges overlap
  private doDateRangesOverlap(
    newStart: Date | null,
    newEnd: Date | null,
    existingStart: Date | null,
    existingEnd: Date | null
  ): boolean {
    // Two ranges overlap if: newStart < existingEnd AND newEnd > existingStart
    // Handle null dates as open-ended (extends indefinitely)

    // If both ranges are completely open-ended, they overlap
    if (!newStart && !newEnd && !existingStart && !existingEnd) {
      return true;
    }

    // Check for non-overlapping conditions first (easier to reason about)
    // Case 1: New range ends before existing range starts
    if (newEnd && existingStart && newEnd < existingStart) {
      return false;
    }

    // Case 2: New range starts after existing range ends
    if (newStart && existingEnd && newStart > existingEnd) {
      return false;
    }

    // If we reach here, the ranges overlap
    return true;
  }

  // Helper method to check if two dates are identical (handling null/undefined)
  private areDatesIdentical(date1?: Date, date2?: Date): boolean {
    // Both null/undefined
    if (!date1 && !date2) return true;

    // One null, one not
    if (!date1 || !date2) return false;

    // Both have values - compare timestamps
    return new Date(date1).getTime() === new Date(date2).getTime();
  }

  private async transformProductServiceOutput(
    productService: any
  ): Promise<ProductServiceOutput> {
    // Transform the product service data to match the expected output format
    const output: ProductServiceOutput = {
      id: productService.id,
      name: productService.name,
      type: productService.type,
      description: productService.description,
      base_cost: productService.base_cost,
      custom_fields: productService.custom_fields,
      status: productService.status,
      category_id: productService.category_id,
      unit_type_id: productService.unit_type_id,
      service_level: productService.service_level,
      hotel_id: productService.hotel_id,
      destination_id: productService.destination_id,
      created_at: productService.created_at,
      updated_at: productService.updated_at,
    };

    // Add relationships if available
    if (productService.category) {
      output.category = productService.category;
    }

    if (productService.unit_type) {
      output.unit_type = productService.unit_type;
    }

    if (productService.tags) {
      output.tags = productService.tags;
    }

    if (productService.suppliers) {
      output.suppliers = productService.suppliers;
    }

    return output;
  }

  /**
   * Money helpers
   * - toCents: decimal -> integer cents (round half up)
   * - fromCents: integer cents -> decimal
   */
  private toCents(value: any): number | undefined {
    if (value === null || value === undefined) return undefined;
    const n = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(n)) return undefined;
    return Math.round(n * 100);
  }

  private fromCents(value: any): number | undefined {
    if (value === null || value === undefined) return undefined;
    const n = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(n)) return undefined;
    return n / 100;
  }

  /**
   * Normalizes money for output (UI).
   * Database stores values as integers representing cents.
   * Intelligently converts cents to decimal format, preventing double conversion.
   */
  private normalizeMoneyOut(value: any): number | undefined {
    if (value === null || value === undefined) return undefined;
    const n = typeof value === "string" ? parseFloat(value) : value;
    if (isNaN(n)) return undefined;

    // Smart conversion logic to prevent double division:
    // - If the value is an integer and >= 1000, assume it's in cents and divide by 100
    // - If the value is a decimal or < 1000, assume it's already converted
    // This prevents double conversion while handling both raw DB values and already-converted values
    if (Number.isInteger(n) && Math.abs(n) >= 1000) {
      // Large integer values are likely in cents, convert to decimal
      return n / 100;
    } else {
      // Small values or decimals are likely already converted, return as-is
      return n;
    }
  }

  private async transformSupplierOfferingOutput(
    offering: any
  ): Promise<SupplierOfferingOutput> {
    // Debug: Log the raw offering data to understand the values
    console.log(`🔍 [SERVICE CURRENCY DEBUG] Raw offering data for ${offering.id}:`, {
      gross_price: offering.gross_price,
      net_cost: offering.net_cost,
      commission: offering.commission,
      margin_rate: offering.margin_rate,
      selling_price: offering.selling_price,
      selling_price_selling_currency: offering.selling_price_selling_currency,
      currency: offering.currency,
      selling_currency: offering.selling_currency,
    });

    // Add transformation tracking to prevent double conversion
    if (offering._transformed) {
      console.warn(`⚠️ [DOUBLE CONVERSION WARNING] Offering ${offering.id} has already been transformed, returning as-is to prevent double conversion`);
      return offering;
    }

    // Fetch supplier information if supplier_id is available
    let supplierInfo = undefined;

    if (offering.supplier_id) {
      try {
        // Use direct database query to fetch supplier details
        const supplier = await this.fetchSupplierDetails(offering.supplier_id);

        if (supplier) {
          supplierInfo = supplier;
        } else {
          console.warn(`⚠️ Supplier not found for ID: ${offering.supplier_id}`);
          throw new Error('Supplier not found');
        }
      } catch (error) {
        console.warn(`❌ Could not fetch supplier details for ${offering.supplier_id}:`, error.message);
        // Fallback to user-friendly supplier info
        supplierInfo = {
          id: offering.supplier_id,
          name: `Supplier ${offering.supplier_id.slice(-8)}`, // Show last 8 chars of ID as fallback
          type: "unknown",
          status: "unknown",
          primary_contact_name: null,
          primary_contact_email: null,
        };
      }
    }

    // Use normalizeMoneyOut to handle both cents and decimal values properly
    const transformedOffering = {
      id: offering.id,
      product_service_id: offering.product_service_id,
      supplier_id: offering.supplier_id,
      active_from: offering.active_from,
      active_to: offering.active_to,
      availability_notes: offering.availability_notes,
      product_service_name: offering.product_service?.name || null,

      // Legacy cost field: normalize for output
      cost: this.normalizeMoneyOut(offering.cost),

      // Enhanced pricing fields (return DECIMALS to UI) - use normalizeMoneyOut to prevent double division
      commission: offering.commission,
      gross_price: this.normalizeMoneyOut(offering.gross_price),
      net_cost: this.normalizeMoneyOut(offering.net_cost),
      net_price: this.normalizeMoneyOut(offering.net_price),
      margin_rate: typeof offering.margin_rate === "string" ? parseFloat(offering.margin_rate) : offering.margin_rate,
      // selling side fields returned in DECIMAL
      selling_price: this.normalizeMoneyOut(offering.selling_price),
      custom_prices: offering.custom_prices,

      // Calculated fields (computed on the fly) - use normalized values
      calculated_supplier_price: (() => {
        // Commission amount calculation
        const grossPrice = this.normalizeMoneyOut(offering.gross_price);
        if (grossPrice != null && offering.commission != null) {
          const commissionRate =
            typeof offering.commission === "string"
              ? parseFloat(offering.commission)
              : offering.commission;
          if (typeof commissionRate === "number") {
            return grossPrice * commissionRate;
          }
        }
        return undefined;
      })(),
      calculated_net_price: (() => {
        // Prefer stored net_cost; else compute from gross_price and commission
        const netCost = this.normalizeMoneyOut(offering.net_cost);
        if (netCost != null) return netCost;

        const grossPrice = this.normalizeMoneyOut(offering.gross_price);
        if (grossPrice != null && offering.commission != null) {
          const commissionRate =
            typeof offering.commission === "string"
              ? parseFloat(offering.commission)
              : offering.commission;
          if (typeof commissionRate === "number") {
            return grossPrice - (grossPrice * commissionRate);
          }
        }

        const supplierPrice = this.normalizeMoneyOut(offering.supplier_price);
        if (supplierPrice != null) return supplierPrice;

        const netPrice = this.normalizeMoneyOut(offering.net_price);
        if (netPrice != null) return netPrice;

        return undefined;
      })(),
      calculated_selling_price: (() => {
        // If selling_price exists in DB, return normalized value
        const sellingPrice = this.normalizeMoneyOut(offering.selling_price);
        if (sellingPrice != null) return sellingPrice;

        const marginRate =
          typeof offering.margin_rate === "string"
            ? parseFloat(offering.margin_rate)
            : offering.margin_rate;
        if (marginRate == null || marginRate < 0 || marginRate >= 1) return undefined;

        // Determine net cost
        let netCost = this.normalizeMoneyOut(offering.net_cost);
        if (netCost == null) {
          const grossPrice = this.normalizeMoneyOut(offering.gross_price);
          if (grossPrice != null && offering.commission != null) {
            const commissionRate =
              typeof offering.commission === "string"
                ? parseFloat(offering.commission)
                : offering.commission;
            if (typeof commissionRate === "number") {
              netCost = grossPrice - (grossPrice * commissionRate);
            }
          } else {
            const supplierPrice = this.normalizeMoneyOut(offering.supplier_price);
            if (supplierPrice != null) {
              netCost = supplierPrice;
            }
          }
        }
        if (netCost == null) return undefined;

        // selling = net / (1 - margin)
        return netCost / (1 - marginRate);
      })(),
      is_pricing_complete: !!(
        offering.commission &&
        offering.gross_price &&
        offering.margin_rate
      ),
      pricing_errors: [], // TODO: Add validation logic here if needed

      // Currency fields
      currency: offering.currency,
      currency_override: offering.currency_override,

      // Selling currency fields
      selling_currency: offering.selling_currency,
      selling_price_selling_currency: this.normalizeMoneyOut(offering.selling_price_selling_currency),
      exchange_rate: offering.exchange_rate
        ? parseFloat(offering.exchange_rate.toString())
        : undefined,
      exchange_rate_date: offering.exchange_rate_date,

      // Calculated selling currency field
      calculated_selling_price_selling_currency: (() => {
        // If selling_price_selling_currency exists in DB, return normalized value
        const sellingPriceSellingCurrency = this.normalizeMoneyOut(offering.selling_price_selling_currency);
        if (sellingPriceSellingCurrency != null) {
          return sellingPriceSellingCurrency;
        }

        // Else use selling_price or compute selling price
        let sellingPrice = this.normalizeMoneyOut(offering.selling_price);
        if (sellingPrice == null) {
          const marginRate =
            typeof offering.margin_rate === "string"
              ? parseFloat(offering.margin_rate)
              : offering.margin_rate;
          if (marginRate == null || marginRate < 0 || marginRate >= 1) return undefined;

          // compute net cost first
          let netCost = this.normalizeMoneyOut(offering.net_cost);
          if (netCost == null) {
            const grossPrice = this.normalizeMoneyOut(offering.gross_price);
            if (grossPrice != null && offering.commission != null) {
              const commissionRate =
                typeof offering.commission === "string"
                  ? parseFloat(offering.commission)
                  : offering.commission;
              if (typeof commissionRate === "number") {
                netCost = grossPrice - (grossPrice * commissionRate);
              }
            } else {
              const supplierPrice = this.normalizeMoneyOut(offering.supplier_price);
              if (supplierPrice != null) {
                netCost = supplierPrice;
              }
            }
          }
          if (netCost == null) return undefined;
          sellingPrice = netCost / (1 - marginRate);
        }

        // If same currency, return selling price; otherwise convert by exchange rate
        if (!offering.selling_currency || offering.selling_currency === offering.currency) {
          return sellingPrice;
        }
        if (!offering.exchange_rate) return undefined;
        return sellingPrice * Number(offering.exchange_rate);
      })(),
      status: offering.status,
      custom_fields: offering.custom_fields,

      // Add-ons Configuration (JSON storage for addon line items with mandatory/optional settings)
      add_ons: offering.add_ons, // JSON storage for addon line items with pricing and mandatory flags

      created_by: offering.created_by,
      updated_by: offering.updated_by,
      created_at: offering.created_at,
      updated_at: offering.updated_at,
      product_service: offering.product_service
        ? {
            id: offering.product_service.id,
            name: offering.product_service.name,
            type: offering.product_service.type,
            description: offering.product_service.description,
            base_cost: offering.product_service.base_cost,
            custom_fields: offering.product_service.custom_fields,
            status: offering.product_service.status,
            category_id: offering.product_service.category_id,
            unit_type_id: offering.product_service.unit_type_id,
            category: offering.product_service.category,
            unit_type: offering.product_service.unit_type,
            tags: offering.product_service.tags,
            suppliers: offering.product_service.suppliers,
            created_at: offering.product_service.created_at,
            updated_at: offering.product_service.updated_at,
          }
        : undefined,
      supplier: supplierInfo,
      supplier_name: supplierInfo?.name || null, // Add supplier_name for backward compatibility
    };

    // Debug: Log the final transformed output
    console.log(`🔍 [SERVICE CURRENCY DEBUG] Final transformed output for ${offering.id}:`, {
      selling_price: transformedOffering.selling_price,
      selling_price_selling_currency: transformedOffering.selling_price_selling_currency,
      calculated_selling_price_selling_currency: transformedOffering.calculated_selling_price_selling_currency,
      gross_price: transformedOffering.gross_price,
      currency: transformedOffering.currency,
      selling_currency: transformedOffering.selling_currency,
    });

    // Mark as transformed to prevent double conversion
    (transformedOffering as any)._transformed = true;

    return transformedOffering;
  }

  // Bulk import methods for supplier offerings
  async bulkCreateSupplierOfferings(
    offeringsData: CreateSupplierOfferingInput[]
  ): Promise<{
    created: SupplierOfferingOutput[];
    errors: Array<{ index: number; message: string; data: any }>;
  }> {
    const results = {
      created: [] as SupplierOfferingOutput[],
      errors: [] as Array<{ index: number; message: string; data: any }>,
    };

    for (let i = 0; i < offeringsData.length; i++) {
      const offeringData = offeringsData[i];

      try {
        const created = await this.createSupplierOffering(offeringData);
        results.created.push(created);
      } catch (error) {
        results.errors.push({
          index: i + 1,
          message:
            error instanceof Error ? error.message : "Unknown error occurred",
          data: offeringData,
        });
      }
    }

    return results;
  }

  // Helper method to validate bulk import data
  async validateBulkSupplierOfferingsData(
    offeringsData: any[]
  ): Promise<
    Array<{ index: number; field: string; message: string; value?: any }>
  > {
    const errors: Array<{
      index: number;
      field: string;
      message: string;
      value?: any;
    }> = [];

    for (let i = 0; i < offeringsData.length; i++) {
      const offering = offeringsData[i];
      const index = i + 1;

      // Required field validations
      if (!offering.supplier_id) {
        errors.push({
          index,
          field: "supplier_id",
          message: "Supplier ID is required",
          value: offering.supplier_id,
        });
      }

      if (!offering.product_service_id) {
        errors.push({
          index,
          field: "product_service_id",
          message: "Product/Service ID is required",
          value: offering.product_service_id,
        });
      }

      // Validate product/service exists and get category for dynamic field validation
      if (offering.product_service_id) {
        try {
          const productServices = await this.listProductServices({
            id: offering.product_service_id,
          });
          if (!productServices || productServices.length === 0) {
            errors.push({
              index,
              field: "product_service_id",
              message: `Product/Service with ID ${offering.product_service_id} not found`,
              value: offering.product_service_id,
            });
          } else {
            const productService = productServices[0];

            // Get category for dynamic field validation
            if (productService.category_id) {
              try {
                const category = await this.retrieveCategory(
                  productService.category_id
                );

                // Validate dynamic fields if category has schema
                if (category.dynamic_field_schema && offering.custom_fields) {
                  category.dynamic_field_schema.forEach((field: any) => {
                    const fieldValue = offering.custom_fields[field.key];

                    // Required field validation
                    if (
                      field.required &&
                      (!fieldValue || fieldValue.toString().trim() === "")
                    ) {
                      errors.push({
                        index,
                        field: `custom_fields.${field.key}`,
                        message: `${field.label} is required`,
                        value: fieldValue,
                      });
                    }

                    // Type-specific validation
                    if (fieldValue && fieldValue.toString().trim() !== "") {
                      switch (field.type) {
                        case "number":
                          if (isNaN(parseFloat(fieldValue))) {
                            errors.push({
                              index,
                              field: `custom_fields.${field.key}`,
                              message: `${field.label} must be a valid number`,
                              value: fieldValue,
                            });
                          }
                          break;
                        case "dropdown":
                          if (
                            field.options &&
                            !field.options.includes(fieldValue)
                          ) {
                            errors.push({
                              index,
                              field: `custom_fields.${field.key}`,
                              message: `${
                                field.label
                              } must be one of: ${field.options.join(", ")}`,
                              value: fieldValue,
                            });
                          }
                          break;
                        case "date":
                          if (isNaN(Date.parse(fieldValue))) {
                            errors.push({
                              index,
                              field: `custom_fields.${field.key}`,
                              message: `${field.label} must be a valid date`,
                              value: fieldValue,
                            });
                          }
                          break;
                      }
                    }
                  });
                }
              } catch (categoryError) {
                // Category not found or error retrieving it
                console.warn(
                  `Could not validate category ${productService.category_id} for product/service ${offering.product_service_id}`
                );
              }
            }
          }
        } catch (productServiceError) {
          errors.push({
            index,
            field: "product_service_id",
            message: `Error validating product/service: ${
              productServiceError instanceof Error
                ? productServiceError.message
                : "Unknown error"
            }`,
            value: offering.product_service_id,
          });
        }
      }

      // Date validations
      if (offering.active_from) {
        try {
          new Date(offering.active_from);
        } catch {
          errors.push({
            index,
            field: "active_from",
            message: "Active from must be a valid date",
            value: offering.active_from,
          });
        }
      }

      if (offering.active_to) {
        try {
          new Date(offering.active_to);
        } catch {
          errors.push({
            index,
            field: "active_to",
            message: "Active to must be a valid date",
            value: offering.active_to,
          });
        }
      }

      // Status validation
      if (
        offering.status &&
        !["active", "inactive"].includes(offering.status)
      ) {
        errors.push({
          index,
          field: "status",
          message: 'Status must be either "active" or "inactive"',
          value: offering.status,
        });
      }
    }

    return errors;
  }

  // ===== SUPPLIER OFFERING COST HISTORY METHODS =====

  async createSupplierOfferingCostHistory(
    data: CreateSupplierOfferingCostHistoryInput
  ): Promise<SupplierOfferingCostHistoryOutput> {
    try {
      // Validate that supplier offering exists
      const existingOfferings = await this.listSupplierOfferingsWithFilters({
        id: data.supplier_offering_id,
      });

      if (
        !existingOfferings ||
        !existingOfferings.data ||
        existingOfferings.data.length === 0
      ) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Supplier offering with id ${data.supplier_offering_id} not found`
        );
      }

      // Use the generated createSupplierOfferingCostHistories method
      const histories = await this.createSupplierOfferingCostHistories([data]);
      return await this.transformSupplierOfferingCostHistoryOutput(
        histories[0]
      );
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error creating supplier offering cost history:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create supplier offering cost history: ${error.message}`
      );
    }
  }

  async listSupplierOfferingCostHistoryWithFilters(
    filters: SupplierOfferingCostHistoryFilters = {}
  ): Promise<SupplierOfferingCostHistoryListResponse> {
    try {
      const { limit = 25, offset = 0, ...whereFilters } = filters;

      // Build where conditions
      const whereConditions: any = {};

      if (whereFilters.id) {
        whereConditions.id = whereFilters.id;
      }

      if (whereFilters.supplier_offering_id) {
        whereConditions.supplier_offering_id =
          whereFilters.supplier_offering_id;
      }

      if (whereFilters.changed_by_user_id) {
        whereConditions.changed_by_user_id = whereFilters.changed_by_user_id;
      }

      if (whereFilters.date_from) {
        whereConditions.created_at = { $gte: whereFilters.date_from };
      }

      if (whereFilters.date_to) {
        whereConditions.created_at = {
          ...whereConditions.created_at,
          $lte: whereFilters.date_to,
        };
      }

      // Use listAndCountSupplierOfferingCostHistories for proper pagination
      const [histories, totalCount] =
        await this.listAndCountSupplierOfferingCostHistories(whereConditions, {
          skip: offset,
          take: limit,
          relations: ["supplier_offering"],
          orderBy: { created_at: "DESC" },
        });

      const transformedHistories = await Promise.all(
        histories.map((history: any) =>
          this.transformSupplierOfferingCostHistoryOutput(history)
        )
      );

      return {
        data: transformedHistories,
        count: totalCount,
        limit,
        offset,
      };
    } catch (error) {
      // If the table doesn't exist, return empty results
      if (
        error.message &&
        error.message.includes(
          'relation "public.supplier_offering_cost_history" does not exist'
        )
      ) {
        console.warn(
          "supplier_offering_cost_history table does not exist yet, returning empty results"
        );
        return {
          data: [],
          count: 0,
          limit: filters.limit || 25,
          offset: filters.offset || 0,
        };
      }

      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error listing supplier offering cost history:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list supplier offering cost history: ${error.message}`
      );
    }
  }

  async getSupplierOfferingCostHistoryStats(
    supplierOfferingId?: string
  ): Promise<SupplierOfferingCostHistoryStats> {
    try {
      const filters: SupplierOfferingCostHistoryFilters = {};
      if (supplierOfferingId) {
        filters.supplier_offering_id = supplierOfferingId;
      }

      const historyResponse =
        await this.listSupplierOfferingCostHistoryWithFilters({
          ...filters,
          limit: 1000, // Get a large number for stats calculation
        });

      const histories = historyResponse.data;
      const totalChanges = histories.length;

      let costIncreases = 0;
      let costDecreases = 0;
      let currencyChanges = 0;
      let totalCostChangePercentage = 0;
      let costChangeCount = 0;

      const userChangeCounts: Record<string, number> = {};

      histories.forEach((history) => {
        // Count cost changes
        if (history.previous_cost !== null && history.new_cost !== null) {
          if (history.new_cost > history.previous_cost) {
            costIncreases++;
          } else if (history.new_cost < history.previous_cost) {
            costDecreases++;
          }

          // Calculate percentage change
          if (history.previous_cost > 0) {
            const percentageChange =
              ((history.new_cost - history.previous_cost) /
                history.previous_cost) *
              100;
            totalCostChangePercentage += Math.abs(percentageChange);
            costChangeCount++;
          }
        }

        // Count currency changes
        if (
          history.previous_currency &&
          history.new_currency &&
          history.previous_currency !== history.new_currency
        ) {
          currencyChanges++;
        }

        // Count changes by user
        if (history.changed_by_user_id) {
          userChangeCounts[history.changed_by_user_id] =
            (userChangeCounts[history.changed_by_user_id] || 0) + 1;
        }
      });

      // Find most frequent changer
      let mostFrequentChanger:
        | { user_id: string; change_count: number }
        | undefined;
      let maxChanges = 0;
      Object.entries(userChangeCounts).forEach(([userId, count]) => {
        if (count > maxChanges) {
          maxChanges = count;
          mostFrequentChanger = { user_id: userId, change_count: count };
        }
      });

      return {
        total_changes: totalChanges,
        cost_increases: costIncreases,
        cost_decreases: costDecreases,
        currency_changes: currencyChanges,
        average_cost_change_percentage:
          costChangeCount > 0
            ? totalCostChangePercentage / costChangeCount
            : undefined,
        most_recent_change: histories.length > 0 ? histories[0] : undefined,
        most_frequent_changer: mostFrequentChanger,
      };
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error(
        "Error getting supplier offering cost history stats:",
        error
      );
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get supplier offering cost history stats: ${error.message}`
      );
    }
  }

  // Helper method to transform cost history output
  private async transformSupplierOfferingCostHistoryOutput(
    history: any
  ): Promise<SupplierOfferingCostHistoryOutput> {
    const output: SupplierOfferingCostHistoryOutput = {
      id: history.id,
      supplier_offering_id: history.supplier_offering_id,
      previous_cost: history.previous_cost,
      new_cost: history.new_cost,
      previous_currency: history.previous_currency,
      new_currency: history.new_currency,
      change_reason: history.change_reason,
      changed_by_user_id: history.changed_by_user_id,
      created_at: history.created_at,
      updated_at: history.updated_at,
    };

    // Add supplier offering relationship if available
    if (history.supplier_offering) {
      output.supplier_offering = await this.transformSupplierOfferingOutput(
        history.supplier_offering
      );
    }

    return output;
  }

  // Product Service Cost History Methods
  async createProductServiceCostHistory(
    data: CreateProductServiceCostHistoryInput,
    skipValidation: boolean = false
  ): Promise<ProductServiceCostHistoryOutput> {
    try {
      // Validate that product service exists (unless skipped for internal calls)
      if (!skipValidation) {
        try {
          await this.retrieveProductService(data.product_service_id);
        } catch (retrieveError) {
          throw new MedusaError(
            MedusaError.Types.NOT_FOUND,
            `Product service with id ${data.product_service_id} not found`
          );
        }
      }

      // Use the generated createProductServiceCostHistories method
      const histories = await this.createProductServiceCostHistories([data]);
      return await this.transformProductServiceCostHistoryOutput(histories[0]);
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error creating product service cost history:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create product service cost history: ${error.message}`
      );
    }
  }

  async listProductServiceCostHistoryWithFilters(
    filters: ProductServiceCostHistoryFilters
  ): Promise<ProductServiceCostHistoryListResponse> {
    try {
      const query: any = {};

      // Apply filters
      if (filters.id) {
        query.id = filters.id;
      }

      if (filters.product_service_id) {
        query.product_service_id = filters.product_service_id;
      }

      if (filters.changed_by_user_id) {
        query.changed_by_user_id = filters.changed_by_user_id;
      }

      if (filters.date_from || filters.date_to) {
        query.created_at = {};
        if (filters.date_from) {
          query.created_at.$gte = filters.date_from;
        }
        if (filters.date_to) {
          query.created_at.$lte = filters.date_to;
        }
      }

      if (filters.has_cost_change !== undefined) {
        if (filters.has_cost_change) {
          query.$or = [
            { previous_cost: { $ne: null } },
            { new_cost: { $ne: null } },
          ];
        }
      }

      const limit = filters.limit || 25;
      const offset = filters.offset || 0;

      const [histories, count] = await Promise.all([
        this.listProductServiceCostHistories(query, {
          skip: offset,
          take: limit,
          orderBy: { created_at: "DESC" },
          relations: ["product_service"],
        }),
        this.listProductServiceCostHistories(query, { count: true }),
      ]);

      const transformedHistories = await Promise.all(
        histories.map((history) =>
          this.transformProductServiceCostHistoryOutput(history)
        )
      );

      return {
        data: transformedHistories,
        count: Array.isArray(count) ? count.length : (count as any),
        limit,
        offset,
      };
    } catch (error) {
      // If the table doesn't exist, return empty results
      if (
        error.message &&
        error.message.includes(
          'relation "public.product_service_cost_history" does not exist'
        )
      ) {
        console.warn(
          "product_service_cost_history table does not exist yet, returning empty results"
        );
        return {
          data: [],
          count: 0,
          limit: filters.limit || 25,
          offset: filters.offset || 0,
        };
      }

      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error listing product service cost history:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list product service cost history: ${error.message}`
      );
    }
  }

  async getProductServiceCostHistoryStats(
    productServiceId?: string
  ): Promise<ProductServiceCostHistoryStats> {
    try {
      const filters: ProductServiceCostHistoryFilters = {};
      if (productServiceId) {
        filters.product_service_id = productServiceId;
      }

      const historyResponse =
        await this.listProductServiceCostHistoryWithFilters({
          ...filters,
          limit: 1000, // Get a large number for stats calculation
        });

      const histories = historyResponse.data;
      const totalChanges = histories.length;

      let costIncreases = 0;
      let costDecreases = 0;
      let totalCostChangePercentage = 0;
      let costChangeCount = 0;

      const userChangeCounts: Record<string, number> = {};

      histories.forEach((history) => {
        // Count cost changes
        if (history.previous_cost !== null && history.new_cost !== null) {
          if (history.new_cost > history.previous_cost) {
            costIncreases++;
          } else if (history.new_cost < history.previous_cost) {
            costDecreases++;
          }

          // Calculate percentage change
          if (history.previous_cost > 0) {
            const percentageChange =
              ((history.new_cost - history.previous_cost) /
                history.previous_cost) *
              100;
            totalCostChangePercentage += Math.abs(percentageChange);
            costChangeCount++;
          }
        }

        // Count user changes
        if (history.changed_by_user_id) {
          userChangeCounts[history.changed_by_user_id] =
            (userChangeCounts[history.changed_by_user_id] || 0) + 1;
        }
      });

      // Find most frequent changer
      let mostFrequentChanger:
        | { user_id: string; change_count: number }
        | undefined;
      let maxChanges = 0;
      Object.entries(userChangeCounts).forEach(([userId, count]) => {
        if (count > maxChanges) {
          maxChanges = count;
          mostFrequentChanger = { user_id: userId, change_count: count };
        }
      });

      return {
        total_changes: totalChanges,
        cost_increases: costIncreases,
        cost_decreases: costDecreases,
        average_cost_change_percentage:
          costChangeCount > 0
            ? totalCostChangePercentage / costChangeCount
            : undefined,
        most_recent_change: histories.length > 0 ? histories[0] : undefined,
        most_frequent_changer: mostFrequentChanger,
      };
    } catch (error) {
      console.error("Error getting product service cost history stats:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get product service cost history stats: ${error.message}`
      );
    }
  }

  private async transformProductServiceCostHistoryOutput(
    history: any
  ): Promise<ProductServiceCostHistoryOutput> {
    const output: ProductServiceCostHistoryOutput = {
      id: history.id,
      product_service_id: history.product_service_id,
      previous_cost: history.previous_cost,
      new_cost: history.new_cost,
      change_reason: history.change_reason,
      changed_by_user_id: history.changed_by_user_id,
      created_at: history.created_at,
      updated_at: history.updated_at,
    };

    // Add product service relationship if available
    if (history.product_service) {
      output.product_service = await this.transformProductServiceOutput(
        history.product_service
      );
    }

    return output;
  }

  // Enhanced Pricing Calculation Methods

  /**
   * Calculate net cost from commission and gross price
   * Formula: Net Cost = Gross Price - (Gross Price × Commission)
   */
  calculateSupplierPrice(commission: number, grossPrice: number): number {
    if (!commission || !grossPrice) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Commission and gross price are required for net cost calculation"
      );
    }

    if (commission < 0 || commission > 1) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Commission must be between 0 and 1 (0% to 100%)"
      );
    }

    if (grossPrice < 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Gross price must be non-negative"
      );
    }

    // Calculate difference value: Gross Price × Commission
    const differenceValue = grossPrice * commission;
    // Calculate Net Cost: Gross Price - Difference Value
    return grossPrice - differenceValue;
  }

  /**
   * Calculate selling price from supplier price and margin rate
   * Formula: Selling Price = Supplier Price ÷ (1 - Margin Rate)
   */
  calculateSellingPrice(supplierPrice: number, marginRate: number): number {
    if (!supplierPrice || supplierPrice < 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Supplier price must be a positive number"
      );
    }

    if (!marginRate || marginRate < 0 || marginRate >= 1) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Margin rate must be between 0 and 1 (0% to 99.99%)"
      );
    }

    return supplierPrice / (1 - marginRate);
  }

  /**
   * Calculate all pricing fields for a supplier offering
   */
  calculateAllPricing(input: {
    commission?: number;
    grossPrice?: number;
    supplierPrice?: number;
    marginRate?: number;
  }): {
    calculatedSupplierPrice?: number;
    calculatedNetPrice?: number;
    calculatedSellingPrice?: number;
    isPricingComplete: boolean;
    pricingErrors: string[];
  } {
    const errors: string[] = [];
    let calculatedSupplierPrice: number | undefined;
    let calculatedNetPrice: number | undefined;
    let calculatedSellingPrice: number | undefined;

    try {
      // Calculate net cost if commission and gross price are provided
      if (input.commission && input.grossPrice) {
        calculatedSupplierPrice = this.calculateSupplierPrice(
          input.commission,
          input.grossPrice
        );
      }

      // Use provided supplier price or calculated one
      const supplierPrice = input.supplierPrice || calculatedSupplierPrice;

      // Calculate selling price if supplier price and margin rate are available
      if (supplierPrice && input.marginRate) {
        calculatedSellingPrice = this.calculateSellingPrice(
          supplierPrice,
          input.marginRate
        );
      }
    } catch (error) {
      if (error instanceof MedusaError) {
        errors.push(error.message);
      } else {
        errors.push("Unknown pricing calculation error");
      }
    }

    const isPricingComplete = !!(
      input.commission &&
      input.grossPrice &&
      input.marginRate &&
      calculatedSupplierPrice &&
      calculatedNetPrice &&
      calculatedSellingPrice
    );

    return {
      calculatedSupplierPrice,
      calculatedNetPrice,
      calculatedSellingPrice,
      isPricingComplete,
      pricingErrors: errors,
    };
  }

  /**
   * Validate pricing data for supplier offering
   */
  validatePricingData(data: {
    commission?: number;
    grossPrice?: number;
    supplierPrice?: number;
    netPrice?: number;
    marginRate?: number;
    sellingPrice?: number;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate commission
    if (data.commission !== undefined) {
      if (
        typeof data.commission !== "number" ||
        data.commission < 0 ||
        data.commission > 1
      ) {
        errors.push("Commission must be between 0 and 1 (0% to 100%)");
      }
    }

    // Validate gross price
    if (data.grossPrice !== undefined) {
      if (typeof data.grossPrice !== "number" || data.grossPrice < 0) {
        errors.push("Gross price must be a non-negative number");
      }
    }

    // Validate margin rate
    if (data.marginRate !== undefined) {
      if (
        typeof data.marginRate !== "number" ||
        data.marginRate < 0 ||
        data.marginRate >= 1
      ) {
        errors.push("Margin rate must be between 0 and 1 (0% to 99.99%)");
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Convert amount from one currency to another using exchange rate
   */
  convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
    exchangeRate: number
  ): number {
    if (!amount || !exchangeRate) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Amount and exchange rate are required for currency conversion"
      );
    }

    if (exchangeRate <= 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Exchange rate must be positive"
      );
    }

    // If same currency, return original amount
    if (fromCurrency === toCurrency) {
      return amount;
    }

    return amount * exchangeRate;
  }

  /**
   * Calculate selling price in selling currency
   */
  calculateSellingPriceInSellingCurrency(
    sellingPrice: number,
    costCurrency: string,
    sellingCurrency: string,
    exchangeRate?: number
  ): number {
    if (!sellingPrice) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Selling price is required"
      );
    }

    // If same currency, return original price
    if (costCurrency === sellingCurrency) {
      return sellingPrice;
    }

    if (!exchangeRate) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Exchange rate is required for different currencies"
      );
    }

    return this.convertCurrency(
      sellingPrice,
      costCurrency,
      sellingCurrency,
      exchangeRate
    );
  }

  /**
   * Get default exchange rate (placeholder for future API integration)
   */
  async getExchangeRate(
    fromCurrency: string,
    toCurrency: string
  ): Promise<number> {
    // TODO: Integrate with external exchange rate API (e.g., ECB, CurrencyAPI)
    // For now, return 1.0 for same currency, throw error for different currencies

    if (fromCurrency === toCurrency) {
      return 1.0;
    }

    // Placeholder exchange rates for common currency pairs
    const exchangeRates: Record<string, Record<string, number>> = {
      CHF: { EUR: 1.085, USD: 1.12, GBP: 0.875 },
      EUR: { CHF: 0.9217, USD: 1.0323, GBP: 0.8065 },
      USD: { CHF: 0.8929, EUR: 0.9687, GBP: 0.7813 },
      GBP: { CHF: 1.1429, EUR: 1.2398, USD: 1.28 },
    };

    const rate = exchangeRates[fromCurrency]?.[toCurrency];
    if (rate) {
      return rate;
    }

    throw new MedusaError(
      MedusaError.Types.NOT_FOUND,
      `Exchange rate not available for ${fromCurrency} to ${toCurrency}. Please provide manual exchange rate.`
    );
  }

  /**
   * Update exchange rate and recalculate selling price in selling currency
   */
  async updateExchangeRateAndRecalculate(
    offeringId: string,
    newExchangeRate: number
  ): Promise<SupplierOfferingOutput> {
    try {
      const offering = await this.getSupplierOffering(offeringId);

      if (!offering.selling_price || !offering.selling_currency) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Offering must have selling price and selling currency to update exchange rate"
        );
      }

      const newSellingPriceInSellingCurrency =
        this.calculateSellingPriceInSellingCurrency(
          offering.selling_price,
          offering.currency || "CHF",
          offering.selling_currency,
          newExchangeRate
        );

      return await this.updateSupplierOffering(offeringId, {
        exchange_rate: newExchangeRate,
        exchange_rate_date: new Date(),
        selling_price_selling_currency: newSellingPriceInSellingCurrency,
        change_reason: "Exchange rate updated",
      });
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      console.error("Error updating exchange rate:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update exchange rate: ${error.message}`
      );
    }
  }
}

export default SupplierProductsServicesModuleService;
