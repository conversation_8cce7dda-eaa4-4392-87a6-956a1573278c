import { DynamicFieldSchema } from "../types";

/**
 * Generates a product/service name based on category and dynamic fields marked for product naming
 * @param categoryName - The name of the category
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @param queryService - Optional query service for hotel/destination name resolution
 * @param unitTypeName - Optional unit type name to include in the name
 * @param validFrom - Optional valid from date to include in the name for uniqueness
 * @param validTo - Optional valid to date to include in the name for uniqueness
 * @returns Generated name string
 */
export async function generateProductServiceName(
  categoryName: string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = [],
  queryService?: any,
  unitTypeName?: string,
  validFrom?: Date | string | null,
  validTo?: Date | string | null
): Promise<string> {

  const nameParts: string[] = [categoryName];

  // Get fields that should be used in product/service names (all required fields)
  const nameFields = dynamicFieldSchema.filter(
    (field) => field.required
  );

  // Sort fields by order if available, otherwise by key for consistent ordering
  const sortedNameFields = nameFields.sort((a, b) => {
    // If both have order, sort by order
    if (a.order !== undefined && b.order !== undefined) {
      return a.order - b.order;
    }
    // If only one has order, prioritize it
    if (a.order !== undefined) return -1;
    if (b.order !== undefined) return 1;
    // Otherwise sort alphabetically by key
    return a.key.localeCompare(b.key);
  });


  // Add values from fields marked for product naming
  for (const field of sortedNameFields) {
    const value = customFields[field.key];

    if (value !== undefined && value !== null && value !== "") {
      // Handle different field types
      let displayValue: string;

      switch (field.type) {
        case "hotels":
          displayValue = await resolveHotelNames(value, queryService);
          break;
        case "destinations":
          displayValue = await resolveDestinationNames(value, queryService);
          break;
        case "addons":
          displayValue = await resolveAddonNames(value, queryService);
          break;
        case "multi-select":
          displayValue = Array.isArray(value)
            ? value.join(", ")
            : String(value);
          break;
        case "number-range":
          if (
            typeof value === "object" &&
            value.min !== undefined &&
            value.max !== undefined
          ) {
            displayValue = `${value.min}-${value.max}`;
          } else {
            displayValue = String(value);
          }
          break;
        case "boolean":
          displayValue = value ? "Yes" : "No";
          break;
        case "date":
          if (value instanceof Date) {
            displayValue = value.toLocaleDateString();
          } else {
            displayValue = String(value);
          }
          break;
        default:
          displayValue = String(value);
      }

      if (displayValue.trim()) {
        nameParts.push(displayValue.trim());
      }
    }
  }

  // Add unit type name if provided (after all dynamic fields)
  if (unitTypeName && unitTypeName.trim()) {
    nameParts.push(`(${unitTypeName.trim()})`);
  }

  // Add date range if provided (for uniqueness)
  if (validFrom || validTo) {
    const formatDate = (date: Date | string | null): string => {
      if (!date) return "";
      const dateObj = date instanceof Date ? date : new Date(date);
      if (isNaN(dateObj.getTime())) return "";
      // Use local date formatting to avoid timezone shifts
      const year = dateObj.getFullYear();
      const month = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day = String(dateObj.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`; // YYYY-MM-DD format
    };

    const fromStr = formatDate(validFrom);
    const toStr = formatDate(validTo);

    if (fromStr && toStr) {
      nameParts.push(`[${fromStr} to ${toStr}]`);
    } else if (fromStr) {
      nameParts.push(`[from ${fromStr}]`);
    } else if (toStr) {
      nameParts.push(`[to ${toStr}]`);
    }
  }

  const finalName = nameParts.join(" – ");
  return finalName;
}

/**
 * Resolves hotel IDs to hotel names
 * @param value - Hotel ID(s) as string, array, or JSON string
 * @param queryService - Query service for resolving hotel names
 * @returns Comma-separated hotel names or original value if resolution fails
 */
async function resolveHotelNames(
  value: any,
  queryService?: any
): Promise<string> {
  if (!queryService) {
    return Array.isArray(value) ? value.join(", ") : String(value);
  }

  try {
    // Parse hotel IDs from various formats
    let hotelIds: string[] = [];
    if (Array.isArray(value)) {
      hotelIds = value;
    } else if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        hotelIds = Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        hotelIds = [value];
      }
    } else {
      hotelIds = [String(value)];
    }
    // Use the provided query service to resolve hotel names
    const hotelNames: string[] = [];

    for (const hotelId of hotelIds) {
      try {
        const result = await queryService.graph({
          entity: "hotel",
          filters: { id: hotelId },
          fields: ["id", "name"],
        });

        if (result.data && result.data.length > 0) {
          const hotelName = result.data[0].name;
          hotelNames.push(hotelName);
        } else {
          // Fallback to ID if hotel not found
          hotelNames.push(hotelId);
        }
      } catch (error) {
        hotelNames.push(hotelId);
      }
    }

    const result = hotelNames.join(", ");
    return result;
  } catch (error) {
    return Array.isArray(value) ? value.join(", ") : String(value);
  }
}

/**
 * Resolves destination IDs to destination names
 * @param value - Destination ID(s) as string, array, or JSON string
 * @param queryService - Query service for resolving destination names
 * @returns Comma-separated destination names or original value if resolution fails
 */
async function resolveDestinationNames(
  value: any,
  queryService?: any
): Promise<string> {
  if (!queryService) {
    return Array.isArray(value) ? value.join(", ") : String(value);
  }

  try {
    // Parse destination IDs from various formats
    let destinationIds: string[] = [];
    if (Array.isArray(value)) {
      destinationIds = value;
    } else if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        destinationIds = Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        destinationIds = [value];
      }
    } else {
      destinationIds = [String(value)];
    }
    // Use the provided query service to resolve destination names
    const destinationNames: string[] = [];

    for (const destinationId of destinationIds) {
      try {
        const result = await queryService.graph({
          entity: "destination",
          filters: { id: destinationId },
          fields: ["id", "name"],
        });

        if (result.data && result.data.length > 0) {
          const destinationName = result.data[0].name;
          destinationNames.push(destinationName);
        } else {
          // Fallback to ID if destination not found
          destinationNames.push(destinationId);
        }
      } catch (error) {
        console.warn(
          `Failed to resolve destination name for ID ${destinationId}:`,
          error
        );
        destinationNames.push(destinationId);
      }
    }

    const result = destinationNames.join(", ");
    return result;
  } catch (error) {
    console.warn("❌ Error resolving destination names:", error);
    return Array.isArray(value) ? value.join(", ") : String(value);
  }
}

/**
 * Resolves addon/product service IDs to their names
 * @param value - Product service ID(s) as string, array, or JSON string
 * @param queryService - Query service for resolving product service names
 * @returns Comma-separated product service names or original value if resolution fails
 */
async function resolveAddonNames(
  value: any,
  queryService?: any
): Promise<string> {
  if (!queryService) {
    console.warn("No query service available for addon name resolution, falling back to IDs");
    return Array.isArray(value) ? value.join(", ") : String(value);
  }

  try {
    // Parse addon IDs from various formats
    let addonIds: string[] = [];
    if (Array.isArray(value)) {
      addonIds = value;
    } else if (typeof value === "string") {
      try {
        const parsed = JSON.parse(value);
        addonIds = Array.isArray(parsed) ? parsed : [parsed];
      } catch {
        addonIds = [value];
      }
    } else {
      addonIds = [String(value)];
    }

    // Use the provided query service to resolve addon names
    const addonNames: string[] = [];

    for (const addonId of addonIds) {
      try {
        // Query as product_service since addons field type refers to product services
        const result = await queryService.graph({
          entity: "product_service",
          filters: { id: addonId },
          fields: ["id", "name"],
        });

        if (result.data && result.data.length > 0) {
          const addonName = result.data[0].name;
          addonNames.push(addonName);
        } else {
          // Fallback to ID if addon not found
          addonNames.push(addonId);
        }
      } catch (error) {
        console.warn(`❌ Failed to resolve addon name for ID ${addonId}:`, error);
        addonNames.push(addonId);
      }
    }

    const result = addonNames.join(", ");
    return result;
  } catch (error) {
    console.warn("❌ Error resolving addon names:", error);
    return Array.isArray(value) ? value.join(", ") : String(value);
  }
}

/**
 * Generates a unique key for product/service uniqueness validation
 * @param categoryId - The category ID
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns Unique key string for validation
 */
export function generateProductServiceUniqueKey(
  categoryId: string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): string {
  const keyParts: string[] = [categoryId];

  // Get fields that should be used in uniqueness validation
  // Use a new flag 'used_in_uniqueness' if available, otherwise fall back to core identifying fields
  const uniqueFields = dynamicFieldSchema.filter((field) => {
    // If the field has a specific uniqueness flag, use that
    if (field.hasOwnProperty('used_in_uniqueness')) {
      return field.used_in_uniqueness;
    }

    // Otherwise, use fields that are required but exclude time-sensitive/instance-specific fields
    if (field.required) {
      // Exclude fields that are typically instance-specific and shouldn't affect uniqueness
      const excludedFields = [
        'flight_number', 'flight_time', 'date_of_transfer_requested', 'booking_date',
        'service_date', 'departure_time', 'arrival_time', 'pickup_time', 'dropoff_time',
        'notes', 'special_requests', 'customer_notes', 'internal_notes'
      ];
      return !excludedFields.includes(field.key);
    }

    return false;
  });



  // Sort fields by order if available, otherwise by key for consistent ordering
  const sortedFields = uniqueFields.sort((a, b) => {
    // If both have order, sort by order
    if (a.order !== undefined && b.order !== undefined) {
      return a.order - b.order;
    }
    // If only one has order, prioritize it
    if (a.order !== undefined) return -1;
    if (b.order !== undefined) return 1;
    // Otherwise sort alphabetically by key
    return a.key.localeCompare(b.key);
  });

  // Add values from fields marked for product naming
  for (const field of sortedFields) {
    const value = customFields[field.key];
    let keyValue: string;

    if (value === undefined || value === null || value === "") {
      keyValue = "__EMPTY__";
    } else {
      // Normalize values for consistent comparison
      switch (field.type) {
        case "multi-select":
          keyValue = Array.isArray(value)
            ? value.sort().join("|")
            : String(value);
          break;
        case "number-range":
          if (
            typeof value === "object" &&
            value.min !== undefined &&
            value.max !== undefined
          ) {
            keyValue = `${value.min}:${value.max}`;
          } else {
            keyValue = String(value);
          }
          break;
        case "boolean":
          keyValue = value ? "true" : "false";
          break;
        case "date":
          if (value instanceof Date) {
            keyValue = value.toISOString().split("T")[0]; // YYYY-MM-DD format
          } else {
            keyValue = String(value);
          }
          break;
        default:
          keyValue = String(value).toLowerCase().trim();
      }

      keyParts.push(`${field.key}:${keyValue}`);
    }
  }

  return keyParts.join("||");
}

/**
 * Generates a unique key for supplier offering uniqueness validation
 * @param supplierId - The supplier ID
 * @param productServiceId - The product/service ID
 * @param activeFrom - The active from date
 * @param activeTo - The active to date
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns Unique key string for validation
 */
export function generateSupplierOfferingUniqueKey(
  supplierId: string,
  productServiceId: string,
  activeFrom?: Date | string,
  activeTo?: Date | string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): string {
  const keyParts: string[] = [supplierId, productServiceId];

  // Add date range to uniqueness key
  const fromDate = activeFrom
    ? activeFrom instanceof Date
      ? activeFrom.toISOString().split("T")[0]
      : activeFrom
    : "__NO_FROM__";
  const toDate = activeTo
    ? activeTo instanceof Date
      ? activeTo.toISOString().split("T")[0]
      : activeTo
    : "__NO_TO__";
  keyParts.push(`${fromDate}:${toDate}`);

  // Get mandatory fields that should be used in supplier offering uniqueness
  const mandatoryFields = dynamicFieldSchema.filter(
    (field) => field.required && field.used_in_supplier_offering
  );

  // Sort fields by key to ensure consistent ordering
  const sortedFields = mandatoryFields.sort((a, b) =>
    a.key.localeCompare(b.key)
  );

  // Add values from mandatory fields
  for (const field of sortedFields) {
    const value = customFields[field.key];
    let keyValue: string;

    if (value === undefined || value === null || value === "") {
      keyValue = "__EMPTY__";
    } else {
      // Normalize values for consistent comparison
      switch (field.type) {
        case "multi-select":
          keyValue = Array.isArray(value)
            ? value.sort().join("|")
            : String(value);
          break;
        case "number-range":
          if (
            typeof value === "object" &&
            value.min !== undefined &&
            value.max !== undefined
          ) {
            keyValue = `${value.min}:${value.max}`;
          } else {
            keyValue = String(value);
          }
          break;
        case "boolean":
          keyValue = value ? "true" : "false";
          break;
        case "date":
          if (value instanceof Date) {
            keyValue = value.toISOString().split("T")[0]; // YYYY-MM-DD format
          } else {
            keyValue = String(value);
          }
          break;
        default:
          keyValue = String(value).toLowerCase().trim();
      }

      keyParts.push(`${field.key}:${keyValue}`);
    }
  }

  return keyParts.join("||");
}
