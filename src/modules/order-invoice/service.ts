import { MedusaService } from "@camped-ai/framework/utils";
import { OrderInvoice } from "./models/order-invoice";

/**
 * Order Invoice Service
 *
 * Service for managing order invoices and their integration with external
 * providers like Xero.
 */
class OrderInvoiceService extends MedusaService({
  OrderInvoice,
}) {
  /**
   * Create an invoice for an order
   */
  async createInvoice(data: {
    order_id: string;
    provider?: string;
    reference_id?: string;
    metadata?: Record<string, any>;
  }) {
    return await this.create({
      order_id: data.order_id,
      provider: data.provider || "xero",
      reference_id: data.reference_id,
      metadata: data.metadata,
    });
  }

  /**
   * Get invoices for a specific order
   */
  async getInvoicesByOrderId(order_id: string) {
    return await this.list({
      order_id,
    });
  }

  /**
   * Update invoice reference ID (e.g., when Xero invoice is created)
   */
  async updateReferenceId(invoice_id: string, reference_id: string) {
    return await this.update(invoice_id, {
      reference_id,
    });
  }

  /**
   * Get invoice by reference ID
   */
  async getInvoiceByReferenceId(reference_id: string, provider = "xero") {
    const invoices = await this.list({
      reference_id,
      provider,
    });
    return invoices[0] || null;
  }
}

export default OrderInvoiceService;
