import { model } from "@camped-ai/framework/utils";

/**
 * Order Invoice Model
 *
 * Represents invoices generated for orders, with integration to external
 * providers like Xero for invoice management and tracking.
 */
export const OrderInvoice = model
  .define("order_invoice", {
    id: model.id({ prefix: "oinv" }).primaryKey(),
    
    // Reference to the main Medusa order
    order_id: model.text().index(),
    
    // Provider information (hardcoded to "xero" by default)
    provider: model.text().default("xero"),
    
    // External reference ID from the provider (e.g., Xero invoice ID)
    reference_id: model.text().nullable(),
    
    // Metadata for storing additional invoice data
    metadata: model.json().nullable(),
  })
  .indexes([
    {
      name: "IDX_order_invoice_order_id",
      on: ["order_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_order_invoice_reference_id",
      on: ["reference_id"],
      unique: false,
      where: "deleted_at IS NULL AND reference_id IS NOT NULL",
    },
    {
      name: "IDX_order_invoice_provider",
      on: ["provider"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_order_invoice_order_provider",
      on: ["order_id", "provider"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
