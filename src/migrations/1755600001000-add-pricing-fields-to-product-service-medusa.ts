import { Migration } from "@mikro-orm/migrations";

/**
 * Medusa-compatible migration to:
 * 1) Rename product_service.base_cost -> product_service.gross_cost
 * 2) Add pricing calculator fields to product_service:
 *    - currency TEXT NOT NULL DEFAULT 'GBP'
 *    - commission NUMERIC(5,4) NULL
 *    - net_cost NUMERIC(12,2) NULL
 *    - margin_rate NUMERIC(5,4) NULL
 *    - selling_price_cost_currency NUMERIC(12,2) NULL
 *    - selling_price_gbp NUMERIC(12,2) NULL
 * 3) Add required CHECK constraints
 * 4) Replace base_cost index with gross_cost index
 *
 * This migration is designed to be idempotent and safe to run in environments where
 * some parts might already exist.
 */
export class Migration1755600001000 extends Migration {
  async up(): Promise<void> {
    // 1) Rename base_cost -> gross_cost if present
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1
          FROM information_schema.columns
          WHERE table_schema = 'public'
            AND table_name = 'product_service'
            AND column_name = 'base_cost'
        ) THEN
          ALTER TABLE "product_service" RENAME COLUMN "base_cost" TO "gross_cost";
        END IF;
      END$$;
    `);

    // 2) Add required pricing columns
    this.addSql(`
      ALTER TABLE "product_service"
        ADD COLUMN IF NOT EXISTS "currency" TEXT NOT NULL DEFAULT 'GBP',
        ADD COLUMN IF NOT EXISTS "commission" NUMERIC(5,4) NULL,
        ADD COLUMN IF NOT EXISTS "net_cost" NUMERIC(12,2) NULL,
        ADD COLUMN IF NOT EXISTS "margin_rate" NUMERIC(5,4) NULL,
        ADD COLUMN IF NOT EXISTS "selling_price_cost_currency" NUMERIC(12,2) NULL,
        ADD COLUMN IF NOT EXISTS "selling_price_gbp" NUMERIC(12,2) NULL;
    `);

    // 3) Constraints: ranges and non-negativity
    this.addSql(`
      DO $$
      BEGIN
        -- Commission between 0 and 1 (inclusive)
        IF NOT EXISTS (
          SELECT 1 FROM pg_constraint
          WHERE conname = 'chk_product_service_commission_range'
        ) THEN
          ALTER TABLE "product_service"
            ADD CONSTRAINT "chk_product_service_commission_range"
            CHECK ("commission" IS NULL OR ("commission" >= 0 AND "commission" <= 1));
        END IF;

        -- Margin rate between 0 and 1 (exclusive upper bound)
        IF NOT EXISTS (
          SELECT 1 FROM pg_constraint
          WHERE conname = 'chk_product_service_margin_rate_range'
        ) THEN
          ALTER TABLE "product_service"
            ADD CONSTRAINT "chk_product_service_margin_rate_range"
            CHECK ("margin_rate" IS NULL OR ("margin_rate" >= 0 AND "margin_rate" < 1));
        END IF;

        -- Non-negative monetary checks
        IF NOT EXISTS (
          SELECT 1 FROM pg_constraint
          WHERE conname = 'chk_product_service_gross_cost_nonneg'
        ) THEN
          ALTER TABLE "product_service"
            ADD CONSTRAINT "chk_product_service_gross_cost_nonneg"
            CHECK ("gross_cost" IS NULL OR "gross_cost" >= 0);
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM pg_constraint
          WHERE conname = 'chk_product_service_net_cost_nonneg'
        ) THEN
          ALTER TABLE "product_service"
            ADD CONSTRAINT "chk_product_service_net_cost_nonneg"
            CHECK ("net_cost" IS NULL OR "net_cost" >= 0);
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM pg_constraint
          WHERE conname = 'chk_product_service_selling_price_cost_curr_nonneg'
        ) THEN
          ALTER TABLE "product_service"
            ADD CONSTRAINT "chk_product_service_selling_price_cost_curr_nonneg"
            CHECK ("selling_price_cost_currency" IS NULL OR "selling_price_cost_currency" >= 0);
        END IF;

        IF NOT EXISTS (
          SELECT 1 FROM pg_constraint
          WHERE conname = 'chk_product_service_selling_price_gbp_nonneg'
        ) THEN
          ALTER TABLE "product_service"
            ADD CONSTRAINT "chk_product_service_selling_price_gbp_nonneg"
            CHECK ("selling_price_gbp" IS NULL OR "selling_price_gbp" >= 0);
        END IF;
      END$$;
    `);

    // 4) Replace index on base_cost with index on gross_cost
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1
          FROM pg_indexes
          WHERE schemaname = 'public'
            AND indexname = 'IDX_product_service_base_cost'
        ) THEN
          DROP INDEX IF EXISTS "IDX_product_service_base_cost";
        END IF;
      END$$;

      CREATE INDEX IF NOT EXISTS "IDX_product_service_gross_cost"
      ON "product_service" ("gross_cost")
      WHERE "deleted_at" IS NULL AND "gross_cost" IS NOT NULL;
    `);
  }

  async down(): Promise<void> {
    // Drop new index
    this.addSql(`DROP INDEX IF EXISTS "IDX_product_service_gross_cost";`);

    // Drop constraints
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_product_service_commission_range') THEN
          ALTER TABLE "product_service" DROP CONSTRAINT "chk_product_service_commission_range";
        END IF;
        IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_product_service_margin_rate_range') THEN
          ALTER TABLE "product_service" DROP CONSTRAINT "chk_product_service_margin_rate_range";
        END IF;
        IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_product_service_gross_cost_nonneg') THEN
          ALTER TABLE "product_service" DROP CONSTRAINT "chk_product_service_gross_cost_nonneg";
        END IF;
        IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_product_service_net_cost_nonneg') THEN
          ALTER TABLE "product_service" DROP CONSTRAINT "chk_product_service_net_cost_nonneg";
        END IF;
        IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_product_service_selling_price_cost_curr_nonneg') THEN
          ALTER TABLE "product_service" DROP CONSTRAINT "chk_product_service_selling_price_cost_curr_nonneg";
        END IF;
        IF EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'chk_product_service_selling_price_gbp_nonneg') THEN
          ALTER TABLE "product_service" DROP CONSTRAINT "chk_product_service_selling_price_gbp_nonneg";
        END IF;
      END$$;
    `);

    // Drop added columns
    this.addSql(`
      ALTER TABLE "product_service"
        DROP COLUMN IF EXISTS "currency",
        DROP COLUMN IF EXISTS "commission",
        DROP COLUMN IF EXISTS "net_cost",
        DROP COLUMN IF EXISTS "margin_rate",
        DROP COLUMN IF EXISTS "selling_price_cost_currency",
        DROP COLUMN IF EXISTS "selling_price_gbp";
    `);

    // Rename gross_cost back to base_cost if present
    this.addSql(`
      DO $$
      BEGIN
        IF EXISTS (
          SELECT 1
          FROM information_schema.columns
          WHERE table_schema = 'public'
            AND table_name = 'product_service'
            AND column_name = 'gross_cost'
        ) THEN
          ALTER TABLE "product_service" RENAME COLUMN "gross_cost" TO "base_cost";
        END IF;
      END$$;
    `);

    // Restore old index on base_cost
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_product_service_base_cost"
      ON "product_service" ("base_cost")
      WHERE "deleted_at" IS NULL AND "base_cost" IS NOT NULL;
    `);
  }
}
