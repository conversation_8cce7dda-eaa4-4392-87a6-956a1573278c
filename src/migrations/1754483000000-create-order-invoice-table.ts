import { Migration } from "@mikro-orm/migrations";

/**
 * Migration to create order_invoice table
 * 
 * This migration creates the order_invoice table for storing invoice data
 * related to orders, with integration to external providers like Xero.
 * 
 * Table structure:
 * - id: Primary key (text/UUID)
 * - order_id: Foreign key referencing the orders table
 * - metadata: JSON column for storing additional invoice data
 * - provider: String column hardcoded to "xero" for all records
 * - reference_id: String column for storing external invoice reference IDs
 * - Standard audit fields (created_at, updated_at, deleted_at)
 */
export class Migration1754483000000CreateOrderInvoiceTable extends Migration {
  async up(): Promise<void> {
    // Create order_invoice table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "order_invoice" (
        "id" text NOT NULL,
        "order_id" text NOT NULL,
        "metadata" jsonb NULL,
        "provider" varchar(255) NOT NULL DEFAULT 'xero',
        "reference_id" varchar(255) NULL,
        "created_at" timestamptz NOT NULL DEFAULT now(),
        "updated_at" timestamptz NOT NULL DEFAULT now(),
        "deleted_at" timestamptz NULL,
        CONSTRAINT "order_invoice_pkey" PRIMARY KEY ("id")
      );
    `);

    // Create indexes for performance
    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_order_invoice_order_id" 
      ON "order_invoice" ("order_id") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_order_invoice_reference_id" 
      ON "order_invoice" ("reference_id") 
      WHERE "deleted_at" IS NULL AND "reference_id" IS NOT NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_order_invoice_provider" 
      ON "order_invoice" ("provider") 
      WHERE "deleted_at" IS NULL;
    `);

    this.addSql(`
      CREATE INDEX IF NOT EXISTS "IDX_order_invoice_deleted_at" 
      ON "order_invoice" ("deleted_at");
    `);

    // Add foreign key constraint linking order_id to the orders table
    this.addSql(`
      ALTER TABLE "order_invoice" 
      ADD CONSTRAINT "FK_order_invoice_order_id" 
      FOREIGN KEY ("order_id") REFERENCES "order" ("id") 
      ON DELETE CASCADE ON UPDATE CASCADE;
    `);

    console.log("✅ Created order_invoice table with indexes and foreign key constraints");
  }

  async down(): Promise<void> {
    // Drop foreign key constraint first
    this.addSql(`
      ALTER TABLE "order_invoice" 
      DROP CONSTRAINT IF EXISTS "FK_order_invoice_order_id";
    `);

    // Drop indexes
    this.addSql(`
      DROP INDEX IF EXISTS "IDX_order_invoice_order_id";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_order_invoice_reference_id";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_order_invoice_provider";
    `);

    this.addSql(`
      DROP INDEX IF EXISTS "IDX_order_invoice_deleted_at";
    `);

    // Drop the table
    this.addSql('DROP TABLE IF EXISTS "order_invoice" CASCADE;');

    console.log("✅ Dropped order_invoice table and all related constraints");
  }
}
