import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { createProductServiceSyncService } from "../utils/service-registration";

/**
 * Subscriber for product service created events
 * Automatically syncs new product services to add-ons
 */
export async function productServiceCreatedHandler({
  event: { data },
  container,
}: SubscriberArgs<{ id: string; product_service: any }>) {
  try {
    console.log(`🔄 Product service created event received for ID: ${data.id}`);

    // Get the sync service with proper service registration
    const syncService = createProductServiceSyncService(container);

    // Check if the product service already has product_id and product_variant_id populated
    // (this would happen if the workflow sync step already completed successfully)
    const productService = data.product_service || { id: data.id };

    if (productService.product_id && productService.product_variant_id) {
      console.log(`✅ Product service ${data.id} already has reverse mapping - skipping event-based sync`);
      return;
    }

    // If reverse mapping is not yet complete, perform it now
    // This serves as a fallback in case the workflow sync step failed
    console.log(`🔄 Reverse mapping not found, performing event-based sync for product service ${data.id}`);
    await syncService.syncProductServiceToAddOn(data.id);

    console.log(`✅ Product service ${data.id} event-based sync completed successfully`);

  } catch (error) {
    console.error(`❌ Error syncing product service ${data.id}:`, error);
    // Don't throw error to prevent breaking the event flow
    // The error is already logged in the sync service
  }
}

// Default export for the main handler
export default productServiceCreatedHandler;

export const config: SubscriberConfig = {
  event: "product-service-old.created",
};
