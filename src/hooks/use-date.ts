import { useMemo } from "react"

export interface DateOptions {
  dateStyle?: "full" | "long" | "medium" | "short"
  timeStyle?: "full" | "long" | "medium" | "short"
  locale?: string
}

export const useDate = () => {
  const formatDate = useMemo(() => {
    return (date: Date | string | number, options?: DateOptions) => {
      const dateObj = new Date(date)
      
      if (isNaN(dateObj.getTime())) {
        return "Invalid Date"
      }

      const locale = options?.locale || "en-GB"
      
      try {
        return new Intl.DateTimeFormat(locale, {
          dateStyle: options?.dateStyle || "medium",
          timeStyle: options?.timeStyle,
        }).format(dateObj)
      } catch (error) {
        // Fallback to simple date formatting
        return dateObj.toLocaleDateString(locale)
      }
    }
  }, [])

  const formatRelativeTime = useMemo(() => {
    return (date: Date | string | number) => {
      const dateObj = new Date(date)
      const now = new Date()
      const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)

      if (diffInSeconds < 60) {
        return "just now"
      } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        return `${minutes} minute${minutes > 1 ? "s" : ""} ago`
      } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600)
        return `${hours} hour${hours > 1 ? "s" : ""} ago`
      } else if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400)
        return `${days} day${days > 1 ? "s" : ""} ago`
      } else {
        return formatDate(date, { dateStyle: "medium" })
      }
    }
  }, [formatDate])

  const getDateRange = useMemo(() => {
    return (startDate: Date | string, endDate: Date | string) => {
      const start = new Date(startDate)
      const end = new Date(endDate)
      
      return {
        start: formatDate(start),
        end: formatDate(end),
        range: `${formatDate(start)} - ${formatDate(end)}`
      }
    }
  }, [formatDate])

  return {
    formatDate,
    formatRelativeTime,
    getDateRange,
  }
}
