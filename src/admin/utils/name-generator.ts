import { DynamicFieldSchema } from "../components/supplier-management/dynamic-field-renderer";

/**
 * Generates a product/service name based on category and mandatory dynamic fields
 * This is the frontend version of the name generator utility
 * @param categoryName - The name of the category
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @param hotelsData - Optional hotels data for name resolution
 * @param destinationsData - Optional destinations data for name resolution
 * @param productServicesData - Optional product services data for name resolution
 * @param validFrom - Optional valid from date to include in the name for uniqueness
 * @param validTo - Optional valid to date to include in the name for uniqueness
 * @returns Generated name string
 */
export function generateProductServiceName(
  categoryName: string,
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = [],
  hotelsData?: Array<{ id: string; name: string }>,
  destinationsData?: Array<{ id: string; name: string }>,
  productServicesData?: Array<{ id: string; name: string }>,
  validFrom?: Date | string | null,
  validTo?: Date | string | null
): string {
  if (!categoryName) {
    return "";
  }

  const nameParts: string[] = [categoryName];

  // Get fields that should be used in product/service names (both required and optional)
  const productFields = dynamicFieldSchema.filter(
    field => field.used_in_product
  );

  // Sort fields by order if specified, otherwise maintain original order
  const sortedFields = productFields.sort((a, b) => {
    if (a.order !== undefined && b.order !== undefined) {
      return a.order - b.order;
    }
    if (a.order !== undefined) return -1;
    if (b.order !== undefined) return 1;
    return 0;
  });

  // Add values from product fields
  for (const field of sortedFields) {
    const value = customFields[field.key];
    if (value !== undefined && value !== null && value !== '') {
      // Handle different field types
      let displayValue: string;

      switch (field.type) {
        case 'multi-select':
          displayValue = Array.isArray(value) ? value.sort().join(', ') : String(value);
          break;
        case 'hotels':
          displayValue = resolveHotelNames(value, hotelsData);
          break;
        case 'destinations':
          displayValue = resolveDestinationNames(value, destinationsData);
          break;
        case 'addons':
          displayValue = resolveAddonNames(value, productServicesData);
          break;
        case 'number-range':
          if (typeof value === 'object' && value.min !== undefined && value.max !== undefined) {
            displayValue = `${value.min}-${value.max}`;
          } else {
            displayValue = String(value);
          }
          break;
        case 'boolean':
          displayValue = value ? 'Yes' : 'No';
          break;
        case 'date':
          if (value instanceof Date) {
            displayValue = value.toLocaleDateString();
          } else if (typeof value === 'string') {
            // Handle date strings
            const date = new Date(value);
            displayValue = isNaN(date.getTime()) ? value : date.toLocaleDateString();
          } else {
            displayValue = String(value);
          }
          break;
        default:
          displayValue = String(value);
      }

      if (displayValue.trim()) {
        nameParts.push(displayValue.trim());
      }
    }
  }

  // Add date range if provided (for uniqueness)
  if (validFrom || validTo) {
    const formatDate = (date: Date | string | null | undefined): string => {
      if (!date) return "";
      const dateObj = date instanceof Date ? date : new Date(date);
      if (isNaN(dateObj.getTime())) return "";
      return dateObj.toISOString().split('T')[0]; // YYYY-MM-DD format
    };

    const fromStr = formatDate(validFrom);
    const toStr = formatDate(validTo);

    if (fromStr && toStr) {
      nameParts.push(`[${fromStr} to ${toStr}]`);
    } else if (fromStr) {
      nameParts.push(`[from ${fromStr}]`);
    } else if (toStr) {
      nameParts.push(`[to ${toStr}]`);
    }
  }

  return nameParts.join(' – ');
}

/**
 * Resolves hotel IDs to their display names
 * @param value - Hotel ID or array of hotel IDs
 * @param hotelsData - Array of hotel objects with id and name
 * @returns Resolved hotel names as a string
 */
function resolveHotelNames(
  value: any,
  hotelsData?: Array<{ id: string; name: string }>
): string {
  if (!hotelsData || hotelsData.length === 0) {
    // Fallback to IDs if no hotel data available
    return Array.isArray(value) ? value.sort().join(', ') : String(value);
  }

  const hotelIds = Array.isArray(value) ? value : [value];
  const hotelNames: string[] = [];

  for (const hotelId of hotelIds) {
    const hotel = hotelsData.find(h => h.id === hotelId);
    if (hotel) {
      hotelNames.push(hotel.name);
    } else {
      // Fallback to ID if hotel not found
      hotelNames.push(hotelId);
    }
  }

  return hotelNames.sort().join(', ');
}

/**
 * Resolves destination IDs to their display names
 * @param value - Destination ID or array of destination IDs
 * @param destinationsData - Array of destination objects with id and name
 * @returns Resolved destination names as a string
 */
function resolveDestinationNames(
  value: any,
  destinationsData?: Array<{ id: string; name: string }>
): string {
  if (!destinationsData || destinationsData.length === 0) {
    // Fallback to IDs if no destination data available
    return Array.isArray(value) ? value.sort().join(', ') : String(value);
  }

  const destinationIds = Array.isArray(value) ? value : [value];
  const destinationNames: string[] = [];

  for (const destinationId of destinationIds) {
    const destination = destinationsData.find(d => d.id === destinationId);
    if (destination) {
      destinationNames.push(destination.name);
    } else {
      // Fallback to ID if destination not found
      destinationNames.push(destinationId);
    }
  }

  return destinationNames.sort().join(', ');
}

/**
 * Resolves addon/product service IDs to their display names
 * @param value - Product service ID or array of product service IDs
 * @param productServicesData - Array of product service objects with id and name
 * @returns Resolved product service names as a string
 */
function resolveAddonNames(
  value: any,
  productServicesData?: Array<{ id: string; name: string }>
): string {
  if (!productServicesData || productServicesData.length === 0) {
    // Fallback to IDs if no product services data available
    return Array.isArray(value) ? value.sort().join(', ') : String(value);
  }

  const addonIds = Array.isArray(value) ? value : [value];
  const addonNames: string[] = [];

  for (const addonId of addonIds) {
    const productService = productServicesData.find(ps => ps.id === addonId);
    if (productService) {
      addonNames.push(productService.name);
    } else {
      // Fallback to ID if product service not found
      addonNames.push(addonId);
    }
  }

  return addonNames.sort().join(', ');
}

/**
 * Checks if all required fields for name generation are filled
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns True if all required fields are filled
 */
export function areRequiredFieldsForNameFilled(
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): boolean {
  const mandatoryFields = dynamicFieldSchema.filter(
    field => field.required && field.used_in_product
  );

  return mandatoryFields.every(field => {
    const value = customFields[field.key];
    return value !== undefined && value !== null && value !== '';
  });
}

/**
 * Gets the list of required fields that are missing for name generation
 * @param customFields - The custom field values
 * @param dynamicFieldSchema - The schema defining which fields to use
 * @returns Array of missing field labels
 */
export function getMissingRequiredFields(
  customFields: Record<string, any> = {},
  dynamicFieldSchema: DynamicFieldSchema[] = []
): string[] {
  const mandatoryFields = dynamicFieldSchema.filter(
    field => field.required && field.used_in_product
  );

  return mandatoryFields
    .filter(field => {
      const value = customFields[field.key];
      return value === undefined || value === null || value === '';
    })
    .map(field => field.label);
}
