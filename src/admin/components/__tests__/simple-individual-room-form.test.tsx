import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimpleIndividualRoomForm from '../simple-individual-room-form';

// Mock the dependencies
jest.mock('../custom-select', () => {
  return function MockCustomSelect({ value, onChange, options, placeholder }: any) {
    return (
      <select
        data-testid="custom-select"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        aria-label={placeholder}
      >
        {options.map((option: any) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    );
  };
});

jest.mock('../ai-enhanced-inputs', () => ({
  TextareaField: ({ value, onChange, placeholder }: any) => (
    <textarea
      data-testid="textarea-field"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
    />
  ),
}));

// Mock fetch
global.fetch = jest.fn();

describe('SimpleIndividualRoomForm - Next Room Field', () => {
  const defaultProps = {
    hotelId: 'hotel_123',
    roomConfigId: 'room_cfg_123',
    onComplete: jest.fn(),
    renderFooterButtons: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock successful API responses
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      status: 200,
      json: () => Promise.resolve({ rooms: [] }),
    });
  });

  it('should render Next Room field instead of Left Room and Right Room', async () => {
    render(<SimpleIndividualRoomForm {...defaultProps} />);

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Room Relationships')).toBeInTheDocument();
    });

    // Check that Next Room field is present
    expect(screen.getByText('Next Room')).toBeInTheDocument();

    // Check that Left Room and Right Room fields are NOT present (commented out)
    expect(screen.queryByText('Left Room')).not.toBeInTheDocument();
    expect(screen.queryByText('Right Room')).not.toBeInTheDocument();

    // Check that other relationship fields are still present
    expect(screen.getByText('Opposite Room')).toBeInTheDocument();
    expect(screen.getByText('Connected Room')).toBeInTheDocument();
  });

  it('should handle Next Room field selection', async () => {
    const mockRooms = [
      { id: 'room_1', name: 'Room 101', room_number: '101' },
      { id: 'room_2', name: 'Room 102', room_number: '102' },
    ];

    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      status: 200,
      json: () => Promise.resolve({ rooms: mockRooms }),
    });

    render(<SimpleIndividualRoomForm {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Next Room')).toBeInTheDocument();
    });

    // Find the Next Room select field
    const nextRoomSelects = screen.getAllByTestId('custom-select');
    const nextRoomSelect = nextRoomSelects.find(select => 
      select.getAttribute('aria-label') === 'Select a room'
    );

    expect(nextRoomSelect).toBeInTheDocument();

    // Select a room
    fireEvent.change(nextRoomSelect!, { target: { value: 'room_1' } });

    expect(nextRoomSelect).toHaveValue('room_1');
  });

  it('should include next_room field in form submission', async () => {
    const onComplete = jest.fn();
    
    // Mock successful room creation
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({ rooms: [] }),
      })
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve({ room: { id: 'new_room_123' } }),
      });

    render(
      <SimpleIndividualRoomForm 
        {...defaultProps} 
        onComplete={onComplete}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Next Room')).toBeInTheDocument();
    });

    // Fill in required fields
    const nameInput = screen.getByLabelText(/name/i);
    const roomNumberInput = screen.getByLabelText(/room number/i);
    const floorInput = screen.getByLabelText(/floor/i);

    fireEvent.change(nameInput, { target: { value: 'Test Room' } });
    fireEvent.change(roomNumberInput, { target: { value: '101' } });
    fireEvent.change(floorInput, { target: { value: '1' } });

    // Select a next room
    const nextRoomSelects = screen.getAllByTestId('custom-select');
    const nextRoomSelect = nextRoomSelects.find(select => 
      select.getAttribute('aria-label') === 'Select a room'
    );
    fireEvent.change(nextRoomSelect!, { target: { value: 'room_1' } });

    // Submit the form
    const submitButton = screen.getByText(/create room/i);
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/admin/direct-rooms',
        expect.objectContaining({
          method: 'POST',
          body: expect.stringContaining('"next_room":"room_1"'),
        })
      );
    });
  });

  it('should initialize next_room field from initialData', async () => {
    const initialData = {
      id: 'existing_room',
      name: 'Existing Room',
      room_number: '101',
      floor: '1',
      next_room: 'room_2',
      left_room: 'room_1',
      right_room: 'room_3',
    };

    render(
      <SimpleIndividualRoomForm 
        {...defaultProps} 
        initialData={initialData}
        isEdit={true}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Next Room')).toBeInTheDocument();
    });

    // Check that the next_room field is initialized with the correct value
    const nextRoomSelects = screen.getAllByTestId('custom-select');
    const nextRoomSelect = nextRoomSelects.find(select => 
      select.getAttribute('aria-label') === 'Select a room'
    );

    expect(nextRoomSelect).toHaveValue('room_2');
  });
});
