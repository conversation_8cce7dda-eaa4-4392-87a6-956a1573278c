import React from "react";
import {
  FocusModal,
  Text,
  Badge,
  Table,
  Container,
  Heading,
} from "@camped-ai/ui";
import {
  Calendar,
  Package,
  DollarSign,
  User,
  MapPin,
  FileText,
} from "lucide-react";
import { useSupplierOrder } from "../../../hooks/vendor-management/use-supplier-orders";
import { formatCurrencyDisplay } from "../../../utils/currency-helpers";

// Enhanced order interface that includes supplier information from the API
interface EnhancedSupplierOrder {
  id: string;
  order_number: string;
  status: string;
  order_type: string;
  supplier_id: string;
  supplier_name?: string;
  supplier_email?: string;
  supplier_phone?: string;
  supplier_address?: string;
  subtotal: number;
  tax_amount?: number;
  total_amount: number;
  currency_code: string;
  requested_delivery_date?: string;
  actual_delivery_date?: string;
  delivery_address?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  notes?: string;
  internal_notes?: string;
  items?: any[];
  items_count?: number;
  created_at: string;
  updated_at: string;
}

interface ViewSupplierOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
}

export const ViewSupplierOrderModal: React.FC<ViewSupplierOrderModalProps> = ({
  isOpen,
  onClose,
  orderId,
}) => {
  const { data, isLoading, error } = useSupplierOrder(orderId);
  const order = data?.order as EnhancedSupplierOrder;

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "in_progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getOrderTypeBadgeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "product":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "service":
        return "bg-green-100 text-green-800 border-green-200";
      case "mixed":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    // Use the standardized currency helper
    return formatCurrencyDisplay(amount, currency, "en-US");
  };

  if (error) {
    return (
      <FocusModal open={isOpen} onOpenChange={onClose}>
        <FocusModal.Content>
          <FocusModal.Header>
            <FocusModal.Title>Error</FocusModal.Title>
          </FocusModal.Header>
          <FocusModal.Body>
            <Text className="text-red-600">
              Failed to load order details: {error.message}
            </Text>
          </FocusModal.Body>
        </FocusModal.Content>
      </FocusModal>
    );
  }

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content className=" max-h-[90vh] flex flex-col">
        <FocusModal.Header className="flex-shrink-0">
          <FocusModal.Title>
            {isLoading ? "Loading..." : `Order ${order?.order_number}`}
          </FocusModal.Title>
          <FocusModal.Description>
            View comprehensive supplier order details
          </FocusModal.Description>
        </FocusModal.Header>

        <FocusModal.Body className="space-y-6 overflow-y-auto flex-1 min-h-0 p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Text>Loading order details...</Text>
            </div>
          ) : order ? (
            <>
              {/* Order Overview */}
              <Container className="p-4 bg-ui-bg-subtle rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-ui-fg-muted" />
                    <div>
                      <Text className="text-sm text-ui-fg-muted">
                        Order Number
                      </Text>
                      <Text className="font-medium">{order.order_number}</Text>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div>
                      <Text className="text-sm text-ui-fg-muted">Status</Text>
                      <Badge
                        className={`rounded-full ${getStatusBadgeColor(
                          order.status
                        )}`}
                      >
                        {order.status}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div>
                      <Text className="text-sm text-ui-fg-muted">Type</Text>
                      <Badge
                        className={`rounded-full ${getOrderTypeBadgeColor(
                          order.order_type
                        )}`}
                      >
                        {order.order_type}
                      </Badge>
                    </div>
                  </div>
                </div>
              </Container>

              {/* Supplier Information */}
              <div>
                <Heading level="h3" className="mb-3">
                  Supplier Information
                </Heading>
                <Container className="p-4 space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Text className="text-sm text-ui-fg-muted">
                        Supplier Name
                      </Text>
                      <Text className="font-medium">{order.supplier_name}</Text>
                    </div>
                    {order.supplier_email && (
                      <div>
                        <Text className="text-sm text-ui-fg-muted">Email</Text>
                        <Text>{order.supplier_email}</Text>
                      </div>
                    )}
                    {order.supplier_phone && (
                      <div>
                        <Text className="text-sm text-ui-fg-muted">Phone</Text>
                        <Text>{order.supplier_phone}</Text>
                      </div>
                    )}
                    {order.supplier_address && (
                      <div>
                        <Text className="text-sm text-ui-fg-muted">
                          Address
                        </Text>
                        <Text>{order.supplier_address}</Text>
                      </div>
                    )}
                  </div>
                </Container>
              </div>

              {/* Financial Information */}
              <div>
                <Heading level="h3" className="mb-3">
                  Financial Details
                </Heading>
                <Container className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-ui-fg-muted" />
                      <div>
                        <Text className="text-sm text-ui-fg-muted">
                          Subtotal
                        </Text>
                        <Text className="font-medium">
                          {formatCurrency(order.subtotal, order.currency_code)}
                        </Text>
                      </div>
                    </div>
                    <div>
                      <Text className="text-sm text-ui-fg-muted">
                        Tax Amount
                      </Text>
                      <Text className="font-medium">
                        {formatCurrency(
                          order.tax_amount || 0,
                          order.currency_code
                        )}
                      </Text>
                    </div>
                    <div>
                      <Text className="text-sm text-ui-fg-muted">
                        Total Amount
                      </Text>
                      <Text className="font-medium text-lg">
                        {formatCurrency(
                          order.total_amount,
                          order.currency_code
                        )}
                      </Text>
                    </div>
                  </div>
                </Container>
              </div>

              {/* Delivery Information */}
              <div>
                <Heading level="h3" className="mb-3">
                  Delivery Information
                </Heading>
                <Container className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-ui-fg-muted" />
                      <div>
                        <Text className="text-sm text-ui-fg-muted">
                          Requested Date
                        </Text>
                        <Text>
                          {order.requested_delivery_date
                            ? formatDate(order.requested_delivery_date)
                            : "Not specified"}
                        </Text>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-ui-fg-muted" />
                      <div>
                        <Text className="text-sm text-ui-fg-muted">
                          Actual Date
                        </Text>
                        <Text>
                          {order.actual_delivery_date
                            ? formatDate(order.actual_delivery_date)
                            : "Not delivered"}
                        </Text>
                      </div>
                    </div>
                    {order.delivery_address && (
                      <div className="md:col-span-2 flex items-start gap-2">
                        <MapPin className="h-4 w-4 text-ui-fg-muted mt-1" />
                        <div>
                          <Text className="text-sm text-ui-fg-muted">
                            Delivery Address
                          </Text>
                          <Text>{order.delivery_address}</Text>
                        </div>
                      </div>
                    )}
                  </div>
                </Container>
              </div>

              {/* Customer Information */}
              {(order.customer_name ||
                order.customer_email ||
                order.customer_phone) && (
                <div>
                  <Heading level="h3" className="mb-3">
                    Customer Information
                  </Heading>
                  <Container className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {order.customer_name && (
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-ui-fg-muted" />
                          <div>
                            <Text className="text-sm text-ui-fg-muted">
                              Name
                            </Text>
                            <Text>{order.customer_name}</Text>
                          </div>
                        </div>
                      )}
                      {order.customer_email && (
                        <div>
                          <Text className="text-sm text-ui-fg-muted">
                            Email
                          </Text>
                          <Text>{order.customer_email}</Text>
                        </div>
                      )}
                      {order.customer_phone && (
                        <div>
                          <Text className="text-sm text-ui-fg-muted">
                            Phone
                          </Text>
                          <Text>{order.customer_phone}</Text>
                        </div>
                      )}
                    </div>
                  </Container>
                </div>
              )}

              {/* Order Items */}
              {order.items && order.items.length > 0 && (
                <div>
                  <Heading level="h3" className="mb-3">
                    Order Items
                  </Heading>
                  <Table>
                    <Table.Header>
                      <Table.Row>
                        <Table.HeaderCell>Item</Table.HeaderCell>
                        <Table.HeaderCell>Type</Table.HeaderCell>
                        <Table.HeaderCell>Quantity</Table.HeaderCell>
                        <Table.HeaderCell>Unit Price</Table.HeaderCell>
                        <Table.HeaderCell>Total</Table.HeaderCell>
                      </Table.Row>
                    </Table.Header>
                    <Table.Body>
                      {order.items.map((item: any) => (
                        <Table.Row key={item.id}>
                          <Table.Cell>
                            <div>
                              <Text className="font-medium">
                                {item.item_name}
                              </Text>
                              {item.item_description && (
                                <Text className="text-sm text-ui-fg-muted">
                                  {item.item_description}
                                </Text>
                              )}
                            </div>
                          </Table.Cell>
                          <Table.Cell>
                            <Badge
                              variant={
                                item.item_type === "product" ? "blue" : "green"
                              }
                            >
                              {item.item_type}
                            </Badge>
                          </Table.Cell>
                          <Table.Cell>{item.quantity}</Table.Cell>
                          <Table.Cell>
                            {formatCurrency(
                              item.unit_price,
                              order.currency_code
                            )}
                          </Table.Cell>
                          <Table.Cell>
                            {formatCurrency(
                              item.total_price,
                              order.currency_code
                            )}
                          </Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              )}

              {/* Notes */}
              {(order.notes || order.internal_notes) && (
                <div>
                  <Heading level="h3" className="mb-3">
                    Notes
                  </Heading>
                  <Container className="p-4 space-y-4">
                    {order.notes && (
                      <div className="flex items-start gap-2">
                        <FileText className="h-4 w-4 text-ui-fg-muted mt-1" />
                        <div>
                          <Text className="text-sm text-ui-fg-muted">
                            Order Notes
                          </Text>
                          <Text className="whitespace-pre-wrap">
                            {order.notes}
                          </Text>
                        </div>
                      </div>
                    )}
                    {order.internal_notes && (
                      <div className="flex items-start gap-2">
                        <FileText className="h-4 w-4 text-ui-fg-muted mt-1" />
                        <div>
                          <Text className="text-sm text-ui-fg-muted">
                            Internal Notes
                          </Text>
                          <Text className="whitespace-pre-wrap">
                            {order.internal_notes}
                          </Text>
                        </div>
                      </div>
                    )}
                  </Container>
                </div>
              )}

              {/* Order Timestamps */}
              <div>
                <Heading level="h3" className="mb-3">
                  Order Timeline
                </Heading>
                <Container className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-ui-fg-muted" />
                      <div>
                        <Text className="text-sm text-ui-fg-muted">
                          Created
                        </Text>
                        <Text>{formatDate(order.created_at)}</Text>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-ui-fg-muted" />
                      <div>
                        <Text className="text-sm text-ui-fg-muted">
                          Last Updated
                        </Text>
                        <Text>{formatDate(order.updated_at)}</Text>
                      </div>
                    </div>
                  </div>
                </Container>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <Text>Order not found</Text>
            </div>
          )}
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};
