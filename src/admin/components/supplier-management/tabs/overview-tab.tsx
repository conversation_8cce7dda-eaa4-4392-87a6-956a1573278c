import React from "react";
import { Heading, Text, Badge, Container } from "@camped-ai/ui";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../hooks/supplier-products-services/use-destinations";
import { useProductsServices } from "../../../hooks/supplier-products-services/use-products-services";
import { formatCustomFieldValue } from "../../../utils/format-custom-field-value";

interface OverviewTabProps {
  productService: any;
}

// Enhanced helper function to format field values with name resolution
const formatFieldValueWithResolution = (
  value: any,
  field: any,
  customFields: any,
  hotels: any[],
  destinations: any[],
  productServices: any[]
) => {
  if (value === null || value === undefined) return "—";

  // Priority 1: Check for API-resolved names with standard naming pattern (field_key_names)
  const namesField = `${field.key}_names`;
  const resolvedNames = customFields[namesField];

  // Priority 2: Handle specific known fields with API-resolved names
  let specificResolvedNames = null;

  // Handle to_resorts field specifically (API provides to_resorts_names)
  if (field.key === "to_resorts" && customFields.to_resorts_names) {
    specificResolvedNames = customFields.to_resorts_names;
  }
  // Handle from_resorts field specifically (API might provide from_resorts_names)
  else if (field.key === "from_resorts" && customFields.from_resorts_names) {
    specificResolvedNames = customFields.from_resorts_names;
  }
  // Handle to field specifically (API provides to_names)
  else if (field.key === 'to' && customFields.to_names) {
    specificResolvedNames = customFields.to_names;
  }
  // Handle from field specifically (API provides from_names)
  else if (field.key === 'from' && customFields.from_names) {
    specificResolvedNames = customFields.from_names;
  }
  // Handle other resort fields
  else if (field.key.includes("resort") && customFields[`${field.key}_names`]) {
    specificResolvedNames = customFields[`${field.key}_names`];
  }
  // Handle hotel fields
  else if (field.key.includes("hotel") && customFields[`${field.key}_names`]) {
    specificResolvedNames = customFields[`${field.key}_names`];
  }
  // Handle destination fields
  else if (
    field.key.includes("destination") &&
    customFields[`${field.key}_names`]
  ) {
    specificResolvedNames = customFields[`${field.key}_names`];
  }

  // Use resolved names if available (prioritize specific field names, then standard pattern)
  const availableResolvedNames = specificResolvedNames || resolvedNames;

  if (
    availableResolvedNames &&
    Array.isArray(availableResolvedNames) &&
    availableResolvedNames.length > 0
  ) {
    // Filter out any null/undefined/empty values
    const validNames = availableResolvedNames.filter(
      (name) => name && typeof name === "string" && name.trim()
    );
    if (validNames.length > 0) {
      return validNames.join(", ");
    }
  }

  // Use the formatCustomFieldValue utility for comprehensive formatting
  const formattedValue = formatCustomFieldValue(value, field, {
    hotels,
    destinations,
    productServices,
  });

  if (
    formattedValue === (Array.isArray(value) ? value.join(", ") : String(value))
  ) {
    if (field.type === "hotels" && hotels.length > 0) {
      let hotelIds: string[] = [];
      if (Array.isArray(value)) {
        hotelIds = value;
      } else if (typeof value === "string") {
        try {
          const parsed = JSON.parse(value);
          hotelIds = Array.isArray(parsed) ? parsed : [parsed];
        } catch {
          hotelIds = [value];
        }
      }

      const hotelNames = hotelIds
        .map((id) => hotels.find((hotel) => hotel.id === id)?.name)
        .filter(Boolean);

      if (hotelNames.length > 0) {
        return hotelNames.join(", ");
      }
    }

    if (field.type === "destinations" && destinations.length > 0) {
      let destinationIds: string[] = [];
      if (Array.isArray(value)) {
        destinationIds = value;
      } else if (typeof value === "string") {
        try {
          const parsed = JSON.parse(value);
          destinationIds = Array.isArray(parsed) ? parsed : [parsed];
        } catch {
          destinationIds = [value];
        }
      }

      const destinationNames = destinationIds
        .map((id) => destinations.find((dest) => dest.id === id)?.name)
        .filter(Boolean);

      if (destinationNames.length > 0) {
        return destinationNames.join(", ");
      }
    }

    if (field.type === "addons" && productServices.length > 0) {
      let addonIds: string[] = [];
      if (Array.isArray(value)) {
        addonIds = value;
      } else if (typeof value === "string") {
        try {
          const parsed = JSON.parse(value);
          addonIds = Array.isArray(parsed) ? parsed : [parsed];
        } catch {
          addonIds = [value];
        }
      }

      const addonNames = addonIds
        .map((id) => productServices.find((addon) => addon.id === id)?.name)
        .filter(Boolean);

      if (addonNames.length > 0) {
        return addonNames.join(", ");
      }
    }
  }

  return formattedValue;
};

const OverviewTab: React.FC<OverviewTabProps> = ({ productService }) => {
  // Fetch hotels, destinations, and product services for name resolution
  const { data: hotelsResponse } = useHotels({ is_active: true });
  const { data: destinationsResponse } = useDestinations({ is_active: true });
  const { data: productServicesResponse } = useProductsServices({
    status: "active",
    limit: 1000,
  });

  const hotels = hotelsResponse?.hotels || [];
  const destinations = destinationsResponse?.destinations || [];
  const productServices = productServicesResponse?.product_services || [];

  // Get category information
  const category = productService.category || {};
  const categoryName = category.name || "Unknown";
  const dynamicFields = category.dynamic_field_schema || [];

  // Get basic product/service information
  const baseCost = parseFloat(productService.base_cost) || 0;
  const currency = "CHF"; // Assuming CHF as default currency
  const unitType = productService.unit_type?.name || "Per unit";
  const serviceLevel = productService.service_level || "Unknown";
  const status = productService.status || "Unknown";
  const customFields = productService.custom_fields || {};

  // Format dates
  const createdDate = new Date(productService.created_at).toLocaleDateString(
    "en-GB",
    {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    }
  );

  const updatedDate = new Date(productService.updated_at).toLocaleDateString(
    "en-GB",
    {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    }
  );

  // Filter dynamic fields that are used in product and have values
  const displayFields = dynamicFields
    .filter(
      (field: any) =>
        field.used_in_product &&
        customFields[field.key] !== undefined &&
        customFields[field.key] !== null
    )
    .sort((a: any, b: any) => (a.order || 999) - (b.order || 999));

  return (
    <div className="space-y-6 mt-5">
      {/* Basic Information */}
      <Container className="p-6 rounded-lg border shadow-sm">
        <Heading level="h3" className="mb-4">
          Basic Information
        </Heading>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Category
            </Text>
            <Badge color="blue">{category.name || "—"}</Badge>
          </div>
          <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Type
            </Text>
            <Badge color="green">{productService.type || "—"}</Badge>
          </div>
          <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Status
            </Text>
            <Badge color={status === "active" ? "green" : "grey"}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
          </div>
          {/* <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Service Level
            </Text>
            <Text size="small">{serviceLevel}</Text>
          </div> */}
          <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Base Cost
            </Text>
            <Text size="small" weight="plus">
              {currency} {baseCost} {unitType}
            </Text>
          </div>
        </div>
      </Container>

      {/* Dynamic Fields */}
      {displayFields.length > 0 && (
        <Container className="p-6 rounded-lg border shadow-sm">
          <Heading level="h3" className="mb-4">
            {categoryName} Details
          </Heading>
          <div className="space-y-3">
            {displayFields.map((field: any) => {
              const value = customFields[field.key];
              const formattedValue = formatFieldValueWithResolution(
                value,
                field,
                customFields,
                hotels,
                destinations,
                productServices
              );

              // Determine if field should render as badges for multiple values
              const isLocationField =
                field.type === "hotels" ||
                field.type === "destinations" ||
                field.key.includes("resort") ||
                field.key.includes("hotel") ||
                field.key.includes("destination");

              const shouldRenderAsBadges =
                isLocationField && formattedValue.includes(", ");

              // Determine badge color based on field type/key
              const getBadgeColor = () => {
                if (field.type === "hotels" || field.key.includes("hotel"))
                  return "blue";
                if (
                  field.type === "destinations" ||
                  field.key.includes("destination")
                )
                  return "green";
                if (field.key.includes("resort")) return "purple";
                return "grey";
              };

              return (
                <div
                  key={field.key}
                  className="flex justify-between items-start"
                >
                  <Text size="small" className="text-gray-600">
                    {field.label}
                  </Text>
                  <div className="text-right">
                    {shouldRenderAsBadges ? (
                      <div className="flex flex-wrap gap-1 justify-end">
                        {formattedValue
                          .split(", ")
                          .map((name: string, index: number) => (
                            <Badge
                              key={index}
                              color={getBadgeColor()}
                              className="text-xs"
                            >
                              {name.trim()}
                            </Badge>
                          ))}
                      </div>
                    ) : (
                      <Text size="small">{formattedValue}</Text>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </Container>
      )}

      {/* Description */}
      {productService.description && (
        <Container className="p-6 rounded-lg border shadow-sm">
          <Heading level="h3" className="mb-4">
            Description
          </Heading>
          <Text className="text-gray-700">{productService.description}</Text>
        </Container>
      )}

      {/* Tags */}
      {productService.tags && productService.tags.length > 0 && (
        <Container className="p-6 rounded-lg border shadow-sm">
          <Heading level="h3" className="mb-4">
            Tags
          </Heading>
          <div className="flex flex-wrap gap-2">
            {productService.tags.map((tag: any) => (
              <Badge
                key={tag.id}
                color="blue"
                className="flex items-center gap-1"
              >
                {tag.color && (
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: tag.color }}
                  />
                )}
                {tag.name}
              </Badge>
            ))}
          </div>
        </Container>
      )}

      {/* System Information */}
      <Container className="p-6 rounded-lg border shadow-sm">
        <Heading level="h3" className="mb-4">
          System Information
        </Heading>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Created
            </Text>
            <Text size="small">{createdDate}</Text>
          </div>
          <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Updated
            </Text>
            <Text size="small">{updatedDate}</Text>
          </div>
          {/* <div className="flex justify-between items-center">
            <Text size="small" className="text-gray-600">
              Service ID
            </Text>
            <div className="flex items-center gap-2">
              <Badge color="blue" className="font-mono text-xs">
                {serviceId}
              </Badge>
              <button
                onClick={copyServiceId}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
                title="Copy Service ID"
              >
                <Copy className="w-3 h-3 text-gray-500" />
              </button>
            </div>
          </div> */}
        </div>
      </Container>
    </div>
  );
};

export default OverviewTab;
