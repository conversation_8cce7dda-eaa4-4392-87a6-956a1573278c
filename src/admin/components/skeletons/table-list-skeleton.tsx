import { clx, Container } from "@camped-ai/ui";

interface SkeletonProps {
  className?: string;
}

const Skeleton = ({ className }: SkeletonProps) => {
  return (
    <div className={clx("animate-pulse rounded-md bg-gray-200", className)} />
  );
};

type TableListSkeletonProps = {
  rows?: number;
};

/**
 * Concierge Bookings table skeleton aligned with provided pattern.
 * - Non-skeleton title
 * - Header/action placeholders
 * - Visible table header
 * - rows placeholders (default 10)
 * - Pagination placeholders
 */
export const TableListSkeleton = ({ rows = 10 }: TableListSkeletonProps) => {
  return (
    <Container className="space-y-3 p-0 divide-y">
      {/* Header with non-skeleton title and action placeholders */}
      <div className="flex items-center justify-between px-6 py-3">
        <Skeleton className="h-6 w-40" />
        <div className="flex space-x-3">
          <Skeleton className="h-9 w-16" />
          <Skeleton className="h-9 w-16" />
          <Skeleton className="h-9 w-16" />
        </div>
      </div>

      <div className="flex items-center justify-between px-6 py-3">
        <Skeleton className="h-9 w-20" />
        <div className="flex space-x-3">
          <Skeleton className="h-9 w-48 hidden md:block" />
          <Skeleton className="h-9 w-9" />
        </div>
      </div>

      {/* Table header (visible labels) */}
      <div className="px-6 overflow-x-auto">
        <div className="min-w-[900px] w-full">
          {/* Row skeletons */}
          {Array.from({ length: rows }).map((_, i) => (
            <div
              key={i}
              className="flex items-center space-x-0 px-0 py-4 border-b"
            >
              <div className="pr-4 w-[160px]">
                <Skeleton className="h-3 w-32" />
              </div>
              <div className="pr-4 w-[140px]">
                <Skeleton className="h-3 w-24" />
              </div>
              <div className="pr-4 w-[140px]">
                <Skeleton className="h-3 w-24" />
              </div>
              <div className="pr-4 w-[180px]">
                <Skeleton className="h-3 w-40" />
              </div>
              <div className="pr-4 w-[120px]">
                <Skeleton className="h-3 w-20" />
              </div>
              <div className="pr-4 w-[120px]">
                <Skeleton className="h-3 w-20" />
              </div>
              <div className="pr-4 w-[120px]">
                <Skeleton className="h-3 w-20" />
              </div>
              <div className="pr-4 w-[120px]">
                <Skeleton className="h-3 w-24" />
              </div>

              {/* Status */}
              <div className="pr-4 w-[120px]">
                <Skeleton className="h-5 w-20 rounded-full" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination footer */}
      <div className="flex items-center justify-between px-6 py-3">
        <Skeleton className="h-4 w-28" />
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>
    </Container>
  );
};
