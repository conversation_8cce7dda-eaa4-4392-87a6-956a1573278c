import React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Container, Text, Badge, Button } from "@camped-ai/ui";
import { Calendar, User, MapPin, DollarSign, Clock } from "lucide-react";
import { format } from "date-fns";
import { BookingScreenData } from "../../routes/concierge-management/bookings/loader";
import { UserDisplayName } from "./user-display-name";

interface KanbanCardProps {
  booking: BookingScreenData;
  onClick: () => void;
  isDragging?: boolean;
}

export const KanbanCard: React.FC<KanbanCardProps> = ({
  booking,
  onClick,
  isDragging = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: booking.id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging || isSortableDragging ? 0.5 : 1,
  };

  // Get status color
  const getStatusColor = (status: string) => {
    const statusColors: Record<string, string> = {
      pending: "orange",
      confirmed: "blue",
      in_progress: "blue", 
      completed: "green",
      cancelled: "red",
      on_hold: "yellow",
    };
    return statusColors[status?.toLowerCase()] || "gray";
  };

  // Format currency
  const formatCurrency = (amount: number | undefined, currencyCode: string = "USD") => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  };

  // Format date range
  const formatDateRange = (checkIn?: string, checkOut?: string) => {
    if (!checkIn || !checkOut) return "Dates TBD";

    try {
      const checkInDate = new Date(checkIn);
      const checkOutDate = new Date(checkOut);

      if (checkInDate.getMonth() === checkOutDate.getMonth() && checkInDate.getFullYear() === checkOutDate.getFullYear()) {
        return `${format(checkInDate, "dd/MM")} - ${format(checkOutDate, "dd/MM/yyyy")}`;
      } else {
        return `${format(checkInDate, "dd/MM/yyyy")} - ${format(checkOutDate, "dd/MM/yyyy")}`;
      }
    } catch {
      return "Invalid dates";
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow duration-200 ${
        isDragging || isSortableDragging ? "shadow-lg" : ""
      }`}
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <Text className="font-medium text-sm truncate">
            Booking #{booking.order?.display_id || booking.order_id}
          </Text>
          <Text className="text-xs text-gray-500 truncate">
            {booking.customer_first_name} {booking.customer_last_name}
          </Text>
        </div>
        <Badge
          size="small"
          color={getStatusColor(booking.status)}
          className="ml-2 text-xs"
        >
          {booking.status}
        </Badge>
      </div>

      {/* Hotel Info */}
      {booking.hotel_name && (
        <div className="flex items-center gap-2 mb-2">
          <MapPin className="w-3 h-3 text-gray-400" />
          <Text className="text-xs text-gray-600 truncate">
            {booking.hotel_name}
          </Text>
        </div>
      )}

      {/* Date Range */}
      <div className="flex items-center gap-2 mb-2">
        <Calendar className="w-3 h-3 text-gray-400" />
        <Text className="text-xs text-gray-600">
          {formatDateRange(booking.check_in_date, booking.check_out_date)}
        </Text>
      </div>

      {/* Total Amount */}
      {booking.order_total && (
        <div className="flex items-center gap-2 mb-2">
          <DollarSign className="w-3 h-3 text-gray-400" />
          <Text className="text-xs text-gray-600">
            {formatCurrency(booking.order_total, booking.order_currency_code)}
          </Text>
        </div>
      )}

      {/* Assigned To */}
      {booking.assigned_to && (
        <div className="flex items-center gap-2 mb-2">
          <User className="w-3 h-3 text-gray-400" />
          <Text className="text-xs text-gray-600 truncate">
            <UserDisplayName userId={booking.assigned_to} />
          </Text>
        </div>
      )}

      {/* Created Date */}
      <div className="flex items-center gap-2 pt-2 border-t border-gray-100">
        <Clock className="w-3 h-3 text-gray-400" />
        <Text className="text-xs text-gray-500">
          {format(new Date(booking.created_at), "MMM d, yyyy")}
        </Text>
      </div>

      {/* Add-ons Count */}
      {booking.concierge_order_items && booking.concierge_order_items.length > 0 && (
        <div className="mt-2">
          <Badge size="small" color="blue" className="text-xs">
            {booking.concierge_order_items.length} add-on{booking.concierge_order_items.length !== 1 ? 's' : ''}
          </Badge>
        </div>
      )}
    </div>
  );
};
