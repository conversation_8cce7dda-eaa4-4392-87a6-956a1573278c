import React, { useState } from "react";
import {
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  But<PERSON>,
  Table,
  Text,
  usePrompt,
  toast,
} from "@camped-ai/ui";
import { PencilSquare, Trash, Plus } from "@camped-ai/icons";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import {
  useConciergeTasks,
  useDeleteConciergeTask,
  ConciergeTask
} from "../../hooks/api/concierge-tasks";
import { TaskFormModal } from "./task-form-modal";
import { UserDisplayName } from "./user-display-name";
import { useRbac } from "../../hooks/use-rbac";
import "../../styles/task-modal.css";

const TaskManagement: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { hasPermission } = useRbac();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<ConciergeTask | null>(null);

  // Use real API hook
  const { data, isLoading, isError, error } = useConciergeTasks({
    limit: 50,
    sort_order: "desc",
  });

  const deleteTaskMutation = useDeleteConciergeTask();

  const tasks = data?.tasks || [];

  const handleDelete = async (task: ConciergeTask) => {
    const confirmed = await prompt({
      title: "Delete Task",
      description: `Are you sure you want to delete the task "${task.title}"?`,
      confirmText: "Delete",
      cancelText: "Cancel",
    });

    if (confirmed) {
      try {
        await deleteTaskMutation.mutateAsync(task.id);
        toast.success("Task deleted successfully");
      } catch (error) {
        toast.error("Failed to delete task");
      }
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "Pending", color: "grey" as const },
      in_progress: { label: "In Progress", color: "blue" as const },
      review: { label: "Review", color: "orange" as const },
      completed: { label: "Completed", color: "green" as const },
      cancelled: { label: "Cancelled", color: "red" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: "Low", color: "grey" as const },
      medium: { label: "Medium", color: "blue" as const },
      high: { label: "High", color: "orange" as const },
      urgent: { label: "Urgent", color: "red" as const },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  if (isError) {
    return (
      <Container className="p-6">
        <Text>Error loading tasks: {error?.message}</Text>
      </Container>
    );
  }

  return (
    <>
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h1">Task Management</Heading>
          {hasPermission("concierge_management:create") && (
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Task
            </Button>
          )}
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          {isLoading ? (
            <div className="p-6 text-center">
              <Text>Loading tasks...</Text>
            </div>
          ) : tasks.length === 0 ? (
            <div className="p-6 text-center">
              <Text className="text-ui-fg-subtle">No tasks found</Text>
              <Button
                className="mt-4"
                onClick={() => setIsCreateModalOpen(true)}
              >
                Create First Task
              </Button>
            </div>
          ) : (
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Title</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell>Priority</Table.HeaderCell>
                  <Table.HeaderCell>Entity Type</Table.HeaderCell>
                  <Table.HeaderCell>Assigned To</Table.HeaderCell>
                  <Table.HeaderCell>Due Date</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {tasks.map((task) => (
                  <Table.Row
                    key={task.id}
                    className="cursor-pointer hover:bg-ui-bg-subtle"
                    onClick={() => navigate(`/concierge-management/tasks/${task.id}`)}
                  >
                    <Table.Cell>
                      <div>
                        <div className="font-medium">{task.title}</div>
                        {task.description && (
                          <div className="text-sm text-ui-fg-subtle mt-1">
                            {task.description}
                          </div>
                        )}
                      </div>
                    </Table.Cell>
                    <Table.Cell>{getStatusBadge(task.status)}</Table.Cell>
                    <Table.Cell>{getPriorityBadge(task.priority)}</Table.Cell>
                    <Table.Cell>
                      <span className={task.entity_type ? "" : "text-ui-fg-subtle"}>
                        {task.entity_type ? task.entity_type.charAt(0).toUpperCase() + task.entity_type.slice(1) : "General"}
                      </span>
                    </Table.Cell>
                    <Table.Cell>
                      <UserDisplayName userId={task.assigned_to} />
                    </Table.Cell>
                    <Table.Cell>
                      <span className={task.due_date ? "" : "text-ui-fg-subtle"}>
                        {task.due_date ? formatDate(task.due_date) : "No due date"}
                      </span>
                    </Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
                        {hasPermission("concierge_management:edit") && (
                          <Button
                            size="small"
                            variant="secondary"
                            onClick={() => setEditingTask(task)}
                          >
                            <PencilSquare className="h-4 w-4" />
                          </Button>
                        )}
                        {hasPermission("concierge_management:delete") && (
                          <Button
                            size="small"
                            variant="secondary"
                            onClick={() => handleDelete(task)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          )}
        </div>
      </Container>

      {/* Create Task Modal */}
      <TaskFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />

      {/* Edit Task Modal */}
      <TaskFormModal
        isOpen={!!editingTask}
        onClose={() => setEditingTask(null)}
        task={editingTask || undefined}
      />
    </>
  );
};

export default TaskManagement;
