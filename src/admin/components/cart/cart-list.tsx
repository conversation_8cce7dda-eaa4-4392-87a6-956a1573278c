import { useState, useEffect } from "react";
import {
  Table,
  Button,
  Select,
  Input,
  DatePicker,
  Heading,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import Spinner from "../shared/spinner";
import { useRbac } from "../../hooks/use-rbac";

// Define types for our data
interface Hotel {
  id: string;
  name: string;
}

interface Cart {
  id: string;
  guest_name?: string;
  hotel_name?: string;
  hotel_id?: string;
  room_type?: string;
  check_in_date?: string;
  check_out_date?: string;
  number_of_rooms?: number;
  total_amount?: number;
  currency_code?: string;
  created_at?: string;
  line_items?: Array<{
    id: string;
    title: string;
    quantity: number;
    unit_price: number;
    metadata?: {
      hotel_id?: string;
      hotel_name?: string;
      room_config_name?: string;
      room_name?: string;
      room_type?: string;
      guest_name?: string;
      guest_email?: string;
      guest_phone?: string;
      check_in_date?: string;
      check_out_date?: string;
      check_in_time?: string;
      check_out_time?: string;
      number_of_guests?: number;
      number_of_rooms?: number;
      special_requests?: string;
    };
  }>;
}

const CartList = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [carts, setCarts] = useState<Cart[]>([]);
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  // Filters
  const [hotelFilter, setHotelFilter] = useState("all");
  const [guestNameFilter, setGuestNameFilter] = useState("");
  const [fromDateFilter, setFromDateFilter] = useState<Date | null>(null);
  const [toDateFilter, setToDateFilter] = useState<Date | null>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize] = useState(10);

  // Format date for display
  const formatDate = (
    dateString: string | undefined,
    withTime: boolean = false
  ): string => {
    if (!dateString) return "Not specified";
    try {
      if (withTime) return format(new Date(dateString), "dd/MM/yyyy HH:mm");
      else return format(new Date(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // The API already processes and extracts data from line items metadata
  // We can use the processed data directly, but add some debug logging
  const logCartData = (cart: Cart) => {
    console.log("🛒 Cart data received from API:", {
      cartId: cart.id,
      guest_name: cart.guest_name,
      hotel_name: cart.hotel_name,
      room_type: cart.room_type,
      number_of_rooms: cart.number_of_rooms,
      check_in_date: cart.check_in_date,
      check_out_date: cart.check_out_date,
      line_items_count: cart.line_items?.length || 0,
      line_items: cart.line_items
    });
  };

  // Fetch hotels for filter dropdown
  const fetchHotels = async () => {
    try {
      const response = await fetch("/admin/hotel-management/hotels");
      if (!response.ok) {
        throw new Error("Failed to fetch hotels");
      }
      const data = await response.json();
      setHotels(data.hotels || []);
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Failed to fetch hotels");
    }
  };

  // Fetch carts with filters
  const fetchCarts = async () => {
    try {
      setIsLoading(true);

      // Build query parameters
      const params = new URLSearchParams();

      // Add filters
      if (hotelFilter !== "all") {
        params.append("hotel_id", hotelFilter);
      }

      if (guestNameFilter) {
        params.append("guest_name", guestNameFilter);
      }

      if (fromDateFilter) {
        params.append("from_date", format(fromDateFilter, "yyyy-MM-dd"));
      }

      if (toDateFilter) {
        params.append("to_date", format(toDateFilter, "yyyy-MM-dd"));
      }

      // Pagination
      params.append("limit", pageSize.toString());
      params.append("offset", (currentPage * pageSize).toString());

      // Fetch data
      const response = await fetch(
        `/admin/hotel-management/carts?${params.toString()}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch carts");
      }

      const data = await response.json();
      setCarts(data.carts);
      setTotalCount(data.count);
    } catch (error) {
      console.error("Error fetching carts:", error);
      toast.error("Failed to fetch carts");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch hotels on component mount
  useEffect(() => {
    fetchHotels();
  }, []);

  // Initial fetch and when filters change
  useEffect(() => {
    fetchCarts();
  }, [hotelFilter, currentPage, pageSize]);

  // Apply filters
  const handleApplyFilters = () => {
    setCurrentPage(0); // Reset to first page
    fetchCarts();
  };

  // Reset filters
  const handleResetFilters = () => {
    setHotelFilter("all");
    setGuestNameFilter("");
    setFromDateFilter(null);
    setToDateFilter(null);
    setCurrentPage(0);
    fetchCarts();
  };

  // View cart details
  const handleViewCart = (cartId: string) => {
    navigate(`/hotel-management/carts/${cartId}`);
  };

  return (
    <div className="space-y-6">
      <Toaster />

      {/* Header */}
      <div className="flex justify-between items-center">
        <Heading>Pending Carts</Heading>
      </div>

      {/* Filters */}
      <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1 text-foreground">
              Hotel
            </label>
            <Select value={hotelFilter} onValueChange={setHotelFilter}>
              <Select.Trigger>
                <Select.Value placeholder="All Hotels" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="all">All Hotels</Select.Item>
                {hotels.map((hotel) => (
                  <Select.Item key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 text-foreground">
              Guest Name
            </label>
            <Input
              type="text"
              value={guestNameFilter}
              onChange={(e) => setGuestNameFilter(e.target.value)}
              placeholder="Search by guest name"
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 text-foreground">
              From Date
            </label>
            <DatePicker value={fromDateFilter} onChange={setFromDateFilter} />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1 text-foreground">
              To Date
            </label>
            <DatePicker value={toDateFilter} onChange={setToDateFilter} />
          </div>
        </div>

        <div className="flex justify-end mt-4 space-x-2">
          <Button variant="secondary" onClick={handleResetFilters}>
            Reset Filters
          </Button>
          <Button onClick={handleApplyFilters}>Apply Filters</Button>
        </div>
      </div>

      {/* Carts Table */}
      <div className="bg-card rounded-lg shadow-sm overflow-hidden border border-border">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Spinner size="medium" />
            <div className="ml-4 text-muted-foreground">Loading ...</div>
          </div>
        ) : (
          <>
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Cart ID</Table.HeaderCell>
                  <Table.HeaderCell>Guest Name</Table.HeaderCell>
                  <Table.HeaderCell>Hotel</Table.HeaderCell>
                  <Table.HeaderCell>Room Type</Table.HeaderCell>
                  <Table.HeaderCell>Check-in Date</Table.HeaderCell>
                  <Table.HeaderCell>Check-out Date</Table.HeaderCell>
                  <Table.HeaderCell>Rooms</Table.HeaderCell>
                  <Table.HeaderCell>Total Amount</Table.HeaderCell>
                  <Table.HeaderCell>Created At</Table.HeaderCell>
                  <Table.HeaderCell>Actions</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {carts.length === 0 ? (
                  <Table.Row>
                    <Table.Cell className="text-center py-4 text-muted-foreground">
                      No pending carts found
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  carts.map((cart) => {
                    // Log cart data for debugging
                    logCartData(cart);

                    return (
                      <Table.Row key={cart.id}>
                        <Table.Cell className="font-medium">
                          {cart.id.substring(0, 8)}...
                        </Table.Cell>
                        <Table.Cell>{cart.guest_name || "Guest"}</Table.Cell>
                        <Table.Cell>
                          {cart.hotel_name || "Unknown Hotel"}
                        </Table.Cell>
                        <Table.Cell>{cart.room_type || "Standard"}</Table.Cell>
                        <Table.Cell>
                          {formatDate(cart.check_in_date)}
                        </Table.Cell>
                        <Table.Cell>
                          {formatDate(cart.check_out_date)}
                        </Table.Cell>
                        <Table.Cell>{cart.number_of_rooms || 1}</Table.Cell>
                        <Table.Cell>
                          {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: cart.currency_code || "USD",
                          }).format(cart.total_amount || 0)}
                        </Table.Cell>
                        <Table.Cell>
                          {formatDate(cart.created_at, true)}
                        </Table.Cell>
                        <Table.Cell>
                          {hasPermission("bookings:view") && (
                            <Button
                              variant="secondary"
                              size="small"
                              onClick={() => handleViewCart(cart.id)}
                            >
                              View
                            </Button>
                          )}
                        </Table.Cell>
                      </Table.Row>
                    );
                  })
                )}
              </Table.Body>
            </Table>

            {/* Pagination */}
            {totalCount > 0 && (
              <div className="flex justify-between items-center p-4 border-t border-border">
                <div className="text-sm text-muted-foreground">
                  Showing {currentPage * pageSize + 1} to{" "}
                  {Math.min((currentPage + 1) * pageSize, totalCount)} of{" "}
                  {totalCount} carts
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    Previous
                  </Button>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={() =>
                      setCurrentPage(
                        Math.min(
                          Math.ceil(totalCount / pageSize) - 1,
                          currentPage + 1
                        )
                      )
                    }
                    disabled={
                      currentPage >= Math.ceil(totalCount / pageSize) - 1
                    }
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CartList;
