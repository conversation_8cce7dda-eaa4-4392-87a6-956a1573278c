import { useMutation, useQueryClient } from "@tanstack/react-query";
import { sdk } from "@camped-ai/framework/utils";
import { format, addDays } from "date-fns";
import {
  createSeasonalRuleForDate,
  checkPriceChangeConflicts,
  findBasePriceRule,
  type PricingContext,
  type BasePriceRule,
  type SeasonalPriceRule,
} from "../../utils/pricing-calendar-utils";

export type CreateSeasonalPriceRuleRequest = {
  base_price_rule_id: string;
  start_date: string;
  end_date: string;
  amount: number;
  currency_code: string;
  priority?: number;
  name?: string;
  description?: string;
};

export type UpdateSeasonalPriceRuleRequest = {
  id: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  name?: string;
  description?: string;
};

/**
 * Hook for managing calendar-based pricing updates
 */
export const usePricingCalendar = (hotelId: string) => {
  const queryClient = useQueryClient();

  // Create a new seasonal price rule
  const createSeasonalRule = useMutation({
    mutationFn: async (data: CreateSeasonalPriceRuleRequest) => {
      const response = await fetch(
        `/api/admin/hotel-management/hotels/${hotelId}/pricing/seasonal-rules`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create seasonal rule");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate pricing data to refresh the calendar
      queryClient.invalidateQueries({
        queryKey: ["hotel-comprehensive-pricing", hotelId],
      });
    },
  });

  // Update an existing seasonal price rule
  const updateSeasonalRule = useMutation({
    mutationFn: async (data: UpdateSeasonalPriceRuleRequest) => {
      const { id, ...updateData } = data;
      const response = await fetch(
        `/api/admin/hotel-management/hotels/${hotelId}/pricing/seasonal-rules/${id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updateData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update seasonal rule");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["hotel-comprehensive-pricing", hotelId],
      });
    },
  });

  // Delete a seasonal price rule
  const deleteSeasonalRule = useMutation({
    mutationFn: async (ruleId: string) => {
      const response = await fetch(
        `/api/admin/hotel-management/hotels/${hotelId}/pricing/seasonal-rules/${ruleId}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete seasonal rule");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["hotel-comprehensive-pricing", hotelId],
      });
    },
  });

  /**
   * Update price for a specific date by creating seasonal rules using the existing bulk API
   */
  const updateDatePrice = async (
    date: Date,
    newPrice: number,
    context: PricingContext,
    basePriceRules: BasePriceRule[],
    seasonalRules: SeasonalPriceRule[],
    currencyCode: string,
    costMarginData?: {
      cost: number;
      fixedMargin: number;
      marginPercentage: number;
    }
  ) => {
    // Find the base price rule for this context
    const basePriceRule = findBasePriceRule(basePriceRules, context);
    if (!basePriceRule) {
      throw new Error("No base price rule found for this configuration");
    }

    const dateStr = format(date, "yyyy-MM-dd");
    const nextDay = addDays(date, 1);
    const endDateStr = format(nextDay, "yyyy-MM-dd");

    // Use cost and margin data if provided, otherwise use price only
    const cost = costMarginData?.cost || 0;
    const fixedMargin = costMarginData?.fixedMargin || 0;
    const marginPercentage = costMarginData?.marginPercentage || 0;

    // Use the existing bulk seasonal pricing API
    const response = await fetch(
      `/admin/hotel-management/room-configs/${context.roomConfigId}/seasonal-pricing/bulk`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          currency_code: currencyCode,
          name: `Calendar Update ${format(date, "MMM dd, yyyy")}`,
          start_date: dateStr,
          end_date: endDateStr,
          weekday_rules: [{
            occupancy_type_id: context.occupancyTypeId,
            meal_plan_id: context.mealPlanId,
            default_values: {
              gross_cost: cost,
              fixed_margin: fixedMargin,
              margin_percentage: marginPercentage,
              total: newPrice,
            },
            weekday_values: {
              [format(date, "eee").toLowerCase()]: {
                gross_cost: cost,
                fixed_margin: fixedMargin,
                margin_percentage: marginPercentage,
              }
            },
            weekday_prices: {
              mon: newPrice , // Convert from cents to currency units for API
              tue: newPrice,
              wed: newPrice ,
              thu: newPrice ,
              fri: newPrice ,
              sat: newPrice ,
              sun: newPrice ,
            }
          }]
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Failed to update price");
    }

    return response.json();
  };

  /**
   * Bulk update prices for multiple dates
   */
  const updateMultipleDatePrices = async (
    datesPrices: Array<{ date: Date; price: number }>,
    context: PricingContext,
    basePriceRules: BasePriceRule[],
    seasonalRules: SeasonalPriceRule[],
    currencyCode: string
  ) => {
    // Process each date sequentially to avoid conflicts
    for (const { date, price } of datesPrices) {
      await updateDatePrice(
        date,
        price,
        context,
        basePriceRules,
        seasonalRules,
        currencyCode
      );
    }
  };

  /**
   * Create a seasonal rule for a date range with the same price
   */
  const createDateRangeRule = async (
    startDate: Date,
    endDate: Date,
    price: number,
    context: PricingContext,
    basePriceRules: BasePriceRule[],
    currencyCode: string,
    name?: string
  ) => {
    const basePriceRule = findBasePriceRule(basePriceRules, context);
    if (!basePriceRule) {
      throw new Error("No base price rule found for this configuration");
    }

    const rule: CreateSeasonalPriceRuleRequest = {
      base_price_rule_id: basePriceRule.id,
      start_date: format(startDate, "yyyy-MM-dd"),
      end_date: format(endDate, "yyyy-MM-dd"),
      amount: price,
      currency_code: currencyCode,
      priority: 100,
      name: name || `Custom pricing ${format(startDate, "MMM dd")} - ${format(endDate, "MMM dd")}`,
    };

    await createSeasonalRule.mutateAsync(rule);
  };

  return {
    createSeasonalRule,
    updateSeasonalRule,
    deleteSeasonalRule,
    updateDatePrice,
    updateMultipleDatePrices,
    createDateRangeRule,
    isLoading:
      createSeasonalRule.isPending ||
      updateSeasonalRule.isPending ||
      deleteSeasonalRule.isPending,
  };
};
