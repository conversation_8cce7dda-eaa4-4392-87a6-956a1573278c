import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  LazyLoadErrorBoundary,
  createSafeLazyComponent,
} from "../../../components/common/lazy-load-error-boundary";
import { Suspense } from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import AdminPageHelmet from "../../../components/AdminPageHelmet";
import { TableListSkeleton } from "../../../components/skeletons/table-list-skeleton";

// Dynamically import page client for better performance
const BookingsPageClient = createSafeLazyComponent(
  () => import("./page-client")
);

const ConciergeBookingsPage = () => {
  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="concierge_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view bookings.
              </Text>
            </div>
          </Container>
        }
      >
        <LazyLoadErrorBoundary fallbackMessage="Failed to load concierge bookings page.">
          <Suspense fallback={<TableListSkeleton />}>
            <BookingsPageClient />
          </Suspense>
        </LazyLoadErrorBoundary>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Concierge Bookings",
});

export default ConciergeBookingsPage;
