import { useState } from "react";
import { 
  Container, 
  Heading, 
  Text, 
  Button, 
  Input,
  Textarea,
  Badge,
  toast,
  Tabs
} from "@camped-ai/ui";
import { 
  Calendar,
  Clock,
  MapPin,
  Plus,
  FileText,
  Upload,
  MessageSquare,
  Phone,
  Mail,
  Edit,
  Trash2
} from "lucide-react";
import { format } from "date-fns";
import { EnhancedConciergeOrder } from "../types";

interface ItineraryManagementProps {
  booking: EnhancedConciergeOrder | null;
  onUpdate?: () => void;
}

interface ItineraryItem {
  id: string;
  date: string;
  time: string;
  title: string;
  description: string;
  location?: string;
  type: 'activity' | 'transport' | 'meal' | 'accommodation' | 'other';
}

interface CommunicationLog {
  id: string;
  date: string;
  type: 'email' | 'phone' | 'whatsapp' | 'note';
  subject: string;
  content: string;
  staff_member: string;
}

const ItineraryManagement = ({ booking, onUpdate }: ItineraryManagementProps) => {
  if (!booking) {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h3">Itinerary & Communications</Heading>
        </div>
        <div className="px-6 py-4">
          <Text className="text-ui-fg-muted">Loading itinerary information...</Text>
        </div>
      </Container>
    );
  }

  const [activeTab, setActiveTab] = useState("itinerary");
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [newItem, setNewItem] = useState<Partial<ItineraryItem>>({
    type: 'activity',
    date: '',
    time: '',
    title: '',
    description: '',
    location: ''
  });

  // Mock data - in real implementation, fetch from API
  const [itineraryItems, setItineraryItems] = useState<ItineraryItem[]>([
    {
      id: '1',
      date: '2024-01-15',
      time: '14:00',
      title: 'Hotel Check-in',
      description: 'Arrival and check-in at the hotel',
      location: 'Hotel Lobby',
      type: 'accommodation'
    },
    {
      id: '2',
      date: '2024-01-15',
      time: '19:00',
      title: 'Welcome Dinner',
      description: 'Dinner at the hotel restaurant',
      location: 'Hotel Restaurant',
      type: 'meal'
    }
  ]);

  const [communicationLogs, setCommunicationLogs] = useState<CommunicationLog[]>([
    {
      id: '1',
      date: '2024-01-10',
      type: 'email',
      subject: 'Booking Confirmation',
      content: 'Sent booking confirmation with itinerary details',
      staff_member: 'Sarah Johnson'
    },
    {
      id: '2',
      date: '2024-01-12',
      type: 'phone',
      subject: 'Special Requests Discussion',
      content: 'Discussed dietary requirements and room preferences',
      staff_member: 'Michael Chen'
    }
  ]);

  // Format date helper
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'activity': return <MapPin className="h-4 w-4" />;
      case 'transport': return <Calendar className="h-4 w-4" />;
      case 'meal': return <Clock className="h-4 w-4" />;
      case 'accommodation': return <FileText className="h-4 w-4" />;
      default: return <Calendar className="h-4 w-4" />;
    }
  };

  // Get type color
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'activity': return 'blue';
      case 'transport': return 'purple';
      case 'meal': return 'orange';
      case 'accommodation': return 'green';
      default: return 'grey';
    }
  };

  // Get communication type icon
  const getCommunicationIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="h-4 w-4" />;
      case 'phone': return <Phone className="h-4 w-4" />;
      case 'whatsapp': return <MessageSquare className="h-4 w-4" />;
      case 'note': return <FileText className="h-4 w-4" />;
      default: return <MessageSquare className="h-4 w-4" />;
    }
  };

  // Handle add itinerary item
  const handleAddItem = () => {
    if (!newItem.title || !newItem.date || !newItem.time) {
      toast.error("Error", {
        description: "Please fill in all required fields",
      });
      return;
    }

    const item: ItineraryItem = {
      id: Date.now().toString(),
      date: newItem.date!,
      time: newItem.time!,
      title: newItem.title!,
      description: newItem.description || '',
      location: newItem.location,
      type: newItem.type as ItineraryItem['type']
    };

    setItineraryItems([...itineraryItems, item]);
    setNewItem({
      type: 'activity',
      date: '',
      time: '',
      title: '',
      description: '',
      location: ''
    });
    setIsAddingItem(false);

    toast.success("Success", {
      description: "Itinerary item added successfully",
    });
  };

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h3">Itinerary & Communications</Heading>
        <Badge variant="secondary">
          {itineraryItems.length} Items
        </Badge>
      </div>

      <div className="px-6 py-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <Tabs.List className="grid w-full grid-cols-4">
            <Tabs.Trigger value="itinerary">Itinerary</Tabs.Trigger>
            <Tabs.Trigger value="timeline">Timeline</Tabs.Trigger>
            <Tabs.Trigger value="documents">Documents</Tabs.Trigger>
            <Tabs.Trigger value="communications">Communications</Tabs.Trigger>
          </Tabs.List>

          {/* Itinerary Builder Tab */}
          <Tabs.Content value="itinerary" className="space-y-4">
            <div className="flex items-center justify-between">
              <Text className="font-medium">Itinerary Items</Text>
              <Button
                size="small"
                onClick={() => setIsAddingItem(true)}
                disabled={isAddingItem}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>

            {/* Add Item Form */}
            {isAddingItem && (
              <div className="border border-ui-border-base rounded-lg p-4 space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Text className="text-sm font-medium mb-1">Date</Text>
                    <Input
                      type="date"
                      value={newItem.date}
                      onChange={(e) => setNewItem({...newItem, date: e.target.value})}
                    />
                  </div>
                  <div>
                    <Text className="text-sm font-medium mb-1">Time</Text>
                    <Input
                      type="time"
                      value={newItem.time}
                      onChange={(e) => setNewItem({...newItem, time: e.target.value})}
                    />
                  </div>
                </div>
                
                <div>
                  <Text className="text-sm font-medium mb-1">Title</Text>
                  <Input
                    value={newItem.title}
                    onChange={(e) => setNewItem({...newItem, title: e.target.value})}
                    placeholder="Activity or event title"
                  />
                </div>

                <div>
                  <Text className="text-sm font-medium mb-1">Description</Text>
                  <Textarea
                    value={newItem.description}
                    onChange={(e) => setNewItem({...newItem, description: e.target.value})}
                    placeholder="Additional details..."
                    rows={2}
                  />
                </div>

                <div className="flex gap-2">
                  <Button size="small" onClick={handleAddItem}>
                    Add Item
                  </Button>
                  <Button 
                    size="small" 
                    variant="secondary" 
                    onClick={() => setIsAddingItem(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {/* Itinerary Items List */}
            <div className="space-y-2">
              {itineraryItems.map((item) => (
                <div key={item.id} className="border border-ui-border-base rounded-lg p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <Badge variant={getTypeColor(item.type) as any} size="small">
                        {getTypeIcon(item.type)}
                        {item.type}
                      </Badge>
                      <div>
                        <Text className="font-medium">{item.title}</Text>
                        <Text className="text-sm text-ui-fg-muted">
                          {formatDate(item.date)} at {item.time}
                        </Text>
                        {item.location && (
                          <Text className="text-sm text-ui-fg-muted">
                            📍 {item.location}
                          </Text>
                        )}
                        {item.description && (
                          <Text className="text-sm mt-1">{item.description}</Text>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button size="small" variant="secondary">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button size="small" variant="secondary">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Tabs.Content>

          {/* Timeline Tab */}
          <Tabs.Content value="timeline">
            <Text>Timeline view coming soon...</Text>
          </Tabs.Content>

          {/* Documents Tab */}
          <Tabs.Content value="documents">
            <div className="text-center py-8">
              <Upload className="h-12 w-12 text-ui-fg-muted mx-auto mb-4" />
              <Text className="text-ui-fg-muted">Document management coming soon</Text>
            </div>
          </Tabs.Content>

          {/* Communications Tab */}
          <Tabs.Content value="communications" className="space-y-4">
            <div className="space-y-2">
              {communicationLogs.map((log) => (
                <div key={log.id} className="border border-ui-border-base rounded-lg p-3">
                  <div className="flex items-start gap-3">
                    <div className="mt-1">
                      {getCommunicationIcon(log.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <Text className="font-medium">{log.subject}</Text>
                        <Text className="text-sm text-ui-fg-muted">
                          {formatDate(log.date)}
                        </Text>
                      </div>
                      <Text className="text-sm text-ui-fg-muted mt-1">
                        {log.content}
                      </Text>
                      <Text className="text-xs text-ui-fg-muted mt-2">
                        by {log.staff_member}
                      </Text>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Tabs.Content>
        </Tabs>
      </div>
    </Container>
  );
};

export default ItineraryManagement;
