import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { createSafeLazyComponent } from "../../../components/common/lazy-load-error-boundary";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSearchParams } from "react-router-dom";
import { useMemo, Suspense } from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { itineraryScreenLoader, type ItineraryScreenFilters } from "./loader";
import AdminPageHelmet from "../../../components/AdminPageHelmet";
import { TableListSkeleton } from "../../../components/skeletons/table-list-skeleton";

// Dynamically import page client for better performance
const ItinerariesPageClient = createSafeLazyComponent(
  () => import("./page-client")
);

const ConciergeItinerariesPage = () => {
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();

  // Get current page and page size from URL params
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "20");
  const searchTerm = searchParams.get("q") || "";

  // Build filters for the loader
  const filters: ItineraryScreenFilters = useMemo(() => {
    const baseFilters: ItineraryScreenFilters = {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      sort_by: searchParams.get("order")?.replace("-", "") || "created_at",
      sort_order: searchParams.get("order")?.startsWith("-") ? "desc" : "asc",
    };

    // Add filters from URL params
    const statusFilter = searchParams.get("status");
    if (statusFilter) {
      baseFilters.status = statusFilter;
    }

    const bookingIdFilter = searchParams.get("booking_id");
    if (bookingIdFilter) {
      baseFilters.booking_id = bookingIdFilter;
    }

    const createdByFilter = searchParams.get("created_by");
    if (createdByFilter) {
      baseFilters.created_by = createdByFilter;
    }

    return baseFilters;
  }, [searchParams, currentPage, pageSize, searchTerm]);

  // Use the itinerary screen loader to fetch itineraries
  const {
    data: itineraryData,
    isLoading: itinerariesLoading,
    error: itinerariesError,
  } = useQuery({
    queryKey: ["itinerary-screen", filters],
    queryFn: () => itineraryScreenLoader(queryClient)(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });

  console.log({ itineraryData });

  // Extract data from queries
  const itineraries = itineraryData?.itineraries || [];
  const totalCount = itineraryData?.count || 0;
  const isLoading = itinerariesLoading;

  // Handle errors
  if (itinerariesError) {
    console.error("Error fetching itineraries:", itinerariesError);
  }

  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="concierge_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view itineraries.
              </Text>
            </div>
          </Container>
        }
      >
        <Suspense fallback={<TableListSkeleton />}>
          <ItinerariesPageClient
            itineraries={itineraries}
            isLoading={isLoading}
            totalCount={totalCount}
            pageSize={pageSize}
          />
        </Suspense>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Concierge Itineraries",
});

export default ConciergeItinerariesPage;
