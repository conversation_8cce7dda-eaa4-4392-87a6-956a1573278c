
import { Calendar, Copy } from "lucide-react";
import { Container, Heading, Text, Badge, Toaster, toast } from "@camped-ai/ui";

import { useMemo, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
 useReactTable,
 getCoreRowModel,
 createColumnHelper,
 ColumnDef,
} from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import type { Filter } from "../../../../components/table/data-table";
import { DataTableQuery } from "../../../../components/table/data-table/data-table-query";
import { DataTableRoot } from "../../../../components/table/data-table/data-table-root";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import ErrorBoundary from "../../../components/shared/error-boundary";

/**
* Hotel Management Bookings - standardized Core Datatable implementation
* Mirrors concierge-management/bookings patterns for consistency.
*/

type BookingRow = {
 order_id: string
 id?: string
 customer_first_name?: string
 customer_last_name?: string
 customer_email?: string
 hotel_id?: string
 hotel_name?: string
 check_in_date?: string
 check_out_date?: string
 status: string
 payment_status?: string
 total_amount?: number
 currency_code?: string
 created_at?: string
 metadata?: Record<string, any>
 order?: {
   id?: string
   display_id?: number
   email?: string
   currency_code?: string
   metadata?: Record<string, any>
   items?: Array<{ metadata?: Record<string, any> }>
   total?: number
   customer?: {
     first_name?: string
     last_name?: string
     email?: string
   }
 }
}

/**
* Fetch hotels for filter dropdown
*/
const fetchHotels = async (): Promise<Array<{ id: string; name: string }>> => {
 try {
   const response = await fetch("/admin/hotel-management/hotels?limit=100", {
     credentials: "include",
     headers: { "Content-Type": "application/json" },
   });
   if (!response.ok) throw new Error("Failed to fetch hotels");
   const data = await response.json();
   return data.hotels || [];
 } catch (e) {
   console.error("Error fetching hotels:", e);
   return [];
 }
};

const PageClient = () => {
 const navigate = useNavigate();
 const location = useLocation();
 const queryClient = useQueryClient();
 const { hasAnyPermission, loading: rbacLoading, hasPermission } = useRbac();

 // Permissions
 const hasBookingAccess = hasAnyPermission([
   "bookings:view",
   "hotel_management:view",
 ]);

 // RBAC loading gate
 if (rbacLoading) {
   return (
     <>
       <PermissionBasedSidebarHider />
       <Container>
         <div className="p-8 text-center">
           <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
           <Text>Loading permissions...</Text>
         </div>
       </Container>
     </>
   );
 }

 return (
   <>
     <PermissionBasedSidebarHider />
     {!hasBookingAccess ? (
       <Container>
         <div className="p-8 text-center">
           <Heading level="h1">Access Denied</Heading>
           <Text className="mt-2">
             You don't have permission to view bookings.
             <br />
             Required permissions: bookings:view or hotel_management:view
           </Text>
         </div>
       </Container>
     ) : (
       <ErrorBoundary>
         <BookingsTable />
       </ErrorBoundary>
     )}
   </>
 );
};

const BookingsTable: React.FC = () => {
 const navigate = useNavigate();
 const location = useLocation();

 // URL params
 const searchParams = new URLSearchParams(location.search);
 const currentPage = parseInt(searchParams.get("page") || "1");
 const pageSize = parseInt(searchParams.get("limit") || "20");
 const hotelId = searchParams.get("hotel_id") || undefined;

 // Local filter state (kept for chips/removal if needed later)
 const [filterState, setFilterState] = useState<{
   hotel_id: string[];
   status: string[];
   customer_name?: string;
 }>({
   hotel_id: hotelId ? [hotelId] : [],
   status: [],
 });

 // Build API filters from URL
 const apiFilters = useMemo(() => {
   const base: Record<string, string> = {
     limit: String(pageSize),
     offset: String((currentPage - 1) * pageSize),
     sort_by: searchParams.get("order")?.replace("-", "") || "created_at",
     sort_order: searchParams.get("order")?.startsWith("-") ? "desc" : "desc",
   };

   const q = searchParams.get("q");
   if (q) base.q = q;

   const status = searchParams.get("status");
   if (status) base.status = status;

   const hotel_id = searchParams.get("hotel_id");
   if (hotel_id) base.hotel_id = hotel_id;

   const customer_name = searchParams.get("customer_name");
   if (customer_name) base.customer_name = customer_name;

   const check_in_date_gte = searchParams.get("check_in_date_gte");
   const check_in_date_lte = searchParams.get("check_in_date_lte");
   const check_out_date_gte = searchParams.get("check_out_date_gte");
   const check_out_date_lte = searchParams.get("check_out_date_lte");
   if (check_in_date_gte) base.check_in_date_gte = check_in_date_gte;
   if (check_in_date_lte) base.check_in_date_lte = check_in_date_lte;
   if (check_out_date_gte) base.check_out_date_gte = check_out_date_gte;
   if (check_out_date_lte) base.check_out_date_lte = check_out_date_lte;

   return base;
 }, [searchParams, currentPage, pageSize]);

 // Fetch hotels for filters
 const { data: hotels = [] } = useQuery({
   queryKey: ["hotels", "list-basic", "for-bookings"],
   queryFn: fetchHotels,
   staleTime: 5 * 60 * 1000,
   gcTime: 10 * 60 * 1000,
 });

 // Fetch bookings list (hotel-management)
 const fetchBookings = async () => {
   const params = new URLSearchParams();
   Object.entries(apiFilters).forEach(([k, v]) => {
     if (v !== undefined && v !== null && v !== "") params.append(k, v);
   });
   const url = `/admin/hotel-management/bookings${
     params.toString() ? `?${params.toString()}` : ""
   }`;
   try {
     const res = await fetch(url, {
       credentials: "include",
       headers: { "Content-Type": "application/json" },
     });
     if (!res.ok) throw new Error(`Failed to fetch bookings`);
     const data = await res.json();
     // Expected shape: { bookings: BookingRow[], count, limit, offset }
     return {
       items: (data.bookings || []) as BookingRow[],
       count: data.count || 0,
       limit: data.limit ?? pageSize,
       offset: data.offset ?? (currentPage - 1) * pageSize,
     };
   } catch (e) {
     console.error(e);
     return { items: [], count: 0, limit: pageSize, offset: 0 };
   }
 };

 const { data, isLoading, error } = useQuery({
   queryKey: ["hotel-bookings", apiFilters, searchParams.toString()],
   queryFn: fetchBookings,
   staleTime: 0,
   gcTime: 5 * 60 * 1000,
   refetchOnWindowFocus: false,
   enabled: true,
 });

 const bookings = data?.items ?? [];
 const totalCount = data?.count ?? 0;

 if (error) {
   console.error("Error fetching hotel bookings:", error);
 }

 // Column helper
 const columnHelper = createColumnHelper<BookingRow>();

 const getCurrency = (row: BookingRow) =>
   row.currency_code || row.order?.currency_code || "CHF";

 // Columns mirrored to concierge patterns and hotel data fields
 const columns = useMemo<ColumnDef<BookingRow, any>[]>(
   () => [
     columnHelper.display({
       id: "booking_id",
       header: "Booking ID",
       cell: ({ row }) => {
         const b = row.original;
         return (
           <div className="flex items-center gap-x-3 w-[160px] truncate">
             <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle">
               <Calendar className="h-4 w-4 text-ui-fg-subtle" />
             </div>
             <div>
               <Text className="txt-compact-medium-plus" weight="plus">
                 {b.order_id}
               </Text>
               <div className="flex items-center gap-x-1">
                 <button
                   onClick={(e) => {
                     e.stopPropagation();
                     navigator.clipboard.writeText(String(b.order_id));
                     toast.success("Booking ID copied to clipboard");
                   }}
                   className="text-ui-fg-muted hover:text-ui-fg-subtle"
                 >
                   <Copy className="h-3 w-3" />
                 </button>
               </div>
             </div>
           </div>
         );
       },
     }),
     columnHelper.display({
       id: "customer",
       header: "Customer",
       cell: ({ row }) => {
         const b = row.original as any;

         // Prefer explicit guest_name from payload, then metadata.guest_name, then derived names
         const guestName =
           b.guest_name ||
           b.metadata?.guest_name ||
           ((b.customer_first_name || b.customer_last_name)
             ? `${b.customer_first_name || ""} ${b.customer_last_name || ""}`.trim()
             : "") ||
           ((b.order?.customer?.first_name || b.order?.customer?.last_name)
             ? `${b.order?.customer?.first_name || ""} ${b.order?.customer?.last_name || ""}`.trim()
             : "") ||
           "—";

         // Prefer explicit guest_email from payload, then metadata.guest_email, then other sources
         const guestEmail =
           b.guest_email ||
           b.metadata?.guest_email ||
           b.customer_email ||
           b.order?.customer?.email ||
           b.order?.email ||
           "";

         return (
           <div className="w-[220px] truncate">
             <Text className="txt-compact-medium">{guestName}</Text>
             <Text className="txt-compact-small text-ui-fg-subtle">
               {guestEmail || "—"}
             </Text>
           </div>
         );
       },
     }),
     columnHelper.display({
       id: "check_in_date",
       header: "Check-in Date",
       cell: ({ row }) => {
         const b = row.original;
         const checkIn =
           b.check_in_date ||
           b.metadata?.check_in_date ||
           b.order?.metadata?.check_in_date ||
           b.order?.items?.[0]?.metadata?.check_in_date;

         return (
           <Text className="txt-compact-medium w-[120px]">
             {checkIn
               ? new Date(checkIn).toLocaleDateString("en-US", {
                   month: "short",
                   day: "numeric",
                   year: "numeric",
                 })
               : "—"}
           </Text>
         );
       },
     }),
     columnHelper.display({
       id: "check_out_date",
       header: "Check-out Date",
       cell: ({ row }) => {
         const b = row.original;
         const checkOut =
           b.check_out_date ||
           b.metadata?.check_out_date ||
           b.order?.metadata?.check_out_date ||
           b.order?.items?.[0]?.metadata?.check_out_date;

         return (
           <Text className="txt-compact-medium w-[120px]">
             {checkOut
               ? new Date(checkOut).toLocaleDateString("en-US", {
                   month: "short",
                   day: "numeric",
                   year: "numeric",
                 })
               : "—"}
           </Text>
         );
       },
     }),
     columnHelper.display({
       id: "hotel_name",
       header: "Hotel Name",
       cell: ({ row }) => {
         const b = row.original;
         const hotel =
           b.hotel_name ||
           b.metadata?.hotel_name ||
           (b.metadata?.hotel_id ? b.metadata?.hotel_id : undefined) ||
           "—";
         return (
           <Text className="txt-compact-medium w-[140px] truncate">{hotel}</Text>
         );
       },
     }),
     columnHelper.display({
       id: "room_type",
       header: "Room Type",
       cell: ({ row }) => {
         const b = row.original as any;
         // Prefer top-level room_type, then room_config_name, then metadata, then order item metadata
         const roomType =
           b.room_type ||
           b.room_config_name ||
           b.metadata?.room_type ||
           b.metadata?.room_config_name ||
           b.order?.items?.[0]?.metadata?.room_type ||
           b.order?.items?.[0]?.metadata?.room_config_name ||
           "—";
         return (
           <Text className="txt-compact-medium w-[140px] truncate">{roomType}</Text>
         );
       },
     }),
     columnHelper.display({
       id: "total_price",
       header: "Total Price",
       cell: ({ row }) => {
         const b = row.original;
         const total =
           (b.total_amount ?? b.order?.total ?? 0);
         const currency = getCurrency(b);
         return (
           <Text className="txt-compact-medium font-medium w-[100px]">
             {new Intl.NumberFormat("en-US", {
               style: "currency",
               currency,
             }).format((total || 0) / 100)}
           </Text>
         );
       },
     }),
     columnHelper.display({
       id: "payment_status",
       header: "Payment Status",
       cell: ({ row }) => {
         const b = row.original;
         const payment =
           b.payment_status ||
           b.metadata?.payment_status ||
           "pending";
         const colorMap: Record<string, any> = {
           pending: "orange",
           paid: "green",
           failed: "red",
           refunded: "grey",
           partially_paid: "blue",
           cancelled: "grey",
         };

         return (
           <Badge color={colorMap[payment] || "grey"} size="xsmall">
             <span className="inter-small-semibold">
               {payment.replace(/_/g, " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
             </span>
           </Badge>
         );
       },
     }),
     columnHelper.display({
       id: "status",
       header: "Status",
       cell: ({ row }) => {
         const b = row.original;
         const s = b.status || "pending";
         const statusColors: Record<string, any> = {
           pending: "orange",
           confirmed: "green",
           checked_in: "blue",
           checked_out: "purple",
           canceled: "grey",
           no_show: "grey",
           reserved: "orange",
           booked: "green",
         };
         return (
           <Badge color={statusColors[s] || "grey"} size="xsmall">
             <span className="inter-small-semibold">
               {s.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
             </span>
           </Badge>
         );
       },
     }),
   ],
   []
 );

 // Table filters aligned with concierge patterns
 const tableFilters: Filter[] = useMemo(
   () => [
     {
       key: "status",
       label: "Booking Status",
       type: "select",
       options: [
         { label: "All", value: "" },
         { label: "Pending", value: "pending" },
         { label: "Confirmed", value: "confirmed" },
         { label: "Checked In", value: "checked_in" },
         { label: "Checked Out", value: "checked_out" },
         { label: "Canceled", value: "canceled" },
         { label: "No Show", value: "no_show" },
       ],
     },
     {
       key: "hotel_id",
       label: "Hotel",
       type: "select",
       options: [
         { label: "All Hotels", value: "" },
         ...hotels.map((h: any) => ({ label: h.name, value: h.id })),
       ],
     },
     {
       key: "customer_name",
       label: "Customer Name",
       type: "string",
     },
     {
       key: "check_in_date",
       label: "Check-in Date Range",
       type: "date",
     },
     {
       key: "check_out_date",
       label: "Check-out Date Range",
       type: "date",
     },
   ],
   [hotels]
 );

 // OrderBy keys for list view
 const orderBy = useMemo(
   () => [
     { key: "order_id" as keyof BookingRow, label: "Booking ID" },
     { key: "status" as keyof BookingRow, label: "Status" },
     { key: "hotel_name" as keyof BookingRow, label: "Hotel Name" },
     { key: "customer_first_name" as keyof BookingRow, label: "Customer Name" },
     { key: "check_in_date" as keyof BookingRow, label: "Check-in Date" },
     { key: "check_out_date" as keyof BookingRow, label: "Check-out Date" },
     { key: "total_amount" as keyof BookingRow, label: "Total Price" },
     { key: "created_at" as keyof BookingRow, label: "Created At" },
   ],
   []
 );

 // Create table
 const table = useReactTable({
   data: bookings,
   columns,
   getCoreRowModel: getCoreRowModel(),
   manualPagination: true,
   manualFiltering: true,
   manualSorting: true,
   pageCount: Math.ceil((totalCount || 0) / pageSize),
   state: {
     pagination: {
       pageIndex: currentPage - 1,
       pageSize,
     },
   },
 });

 // Override pagination to use URL navigation
 const handleNextPage = () => {
   const nextPage = currentPage + 1;
   const params = new URLSearchParams(location.search);
   params.set("page", String(nextPage));
   navigate(`${location.pathname}?${params.toString()}`);
 };
 const handlePreviousPage = () => {
   const prev = Math.max(currentPage - 1, 1);
   const params = new URLSearchParams(location.search);
   params.set("page", String(prev));
   navigate(`${location.pathname}?${params.toString()}`);
 };
 table.nextPage = handleNextPage;
 table.previousPage = handlePreviousPage;

 const isEmpty = !isLoading && ((totalCount || 0) === 0 || bookings.length === 0);

 // Header + DataTable layout consistent with concierge and destinations
 return (
   <Container className="divide-y p-0">
     {/* Header */}
     <div className="flex items-center justify-between px-6 py-3">
       <div>
         <Heading level="h2">Bookings</Heading>
       </div>
     </div>

     {/* DataTable Query Controls */}
     <DataTableQuery
       search="autofocus"
       filters={tableFilters}
       orderBy={orderBy as any}
     />

     {/* Empty state */}
     {isEmpty && (
       <div className="px-6 py-12 text-center text-sm text-ui-fg-subtle">
         No records found.
       </div>
     )}

     {/* List */}
     {!isEmpty && (
       <DataTableRoot
         table={table}
         columns={columns}
         count={totalCount}
         pagination
         navigateTo={(row) =>
           `/hotel-management/bookings/${row.original.order_id}`
         }
       />
     )}

     <Toaster />
   </Container>
 );
};

export default PageClient;
