import { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  But<PERSON>,
  Container,
  Heading,
  Text,
  Toaster,
  toast,
} from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";

import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import { TwoColumnPage } from "../../../../../components/layout/pages";
import Spinner from "../../../../components/shared/spinner";
import PaymentLinkButton from "../../../../components/booking/payment-link-button";

// Reuse concierge detail sections where applicable to achieve parity
import ConciergeBookingCustomerSection from "../../../concierge-management/bookings/[id]/components/concierge-booking-customer-section";
import HotelBookingAddOnsTable from "./components/hotel-booking-addons-table";

// Lightweight booking details summary similar to concierge BookingDetailsSection
// We don't have a dedicated hotel equivalent; render a concise info block here.
const HotelBookingDetailsBlock = ({ booking }: { booking: any }) => {
  if (!booking) return null;

  const dateStr = (d?: string) => {
    try {
      return d ? new Date(d).toLocaleDateString() : "—";
    } catch {
      return "—";
    }
  };

  const status =
    booking.status ||
    booking.metadata?.status ||
    booking.payment_status ||
    "pending";

  const statusVariant = (() => {
    const s = String(status).toLowerCase();
    if (["confirmed", "paid", "completed", "checked_in"].includes(s))
      return "green";
    if (["pending", "awaiting_payment", "in_progress"].includes(s))
      return "orange";
    if (["canceled", "cancelled", "no_show"].includes(s)) return "red";
    if (["checked_out"].includes(s)) return "purple";
    return "grey";
  })();

  const hotelName =
    booking.hotel_name || booking.metadata?.hotel_name || "Hotel";

  const roomType =
    booking.room_type ||
    booking.metadata?.room_type ||
    booking.metadata?.room_config_name ||
    "Room";

  const guests =
    booking.number_of_guests || booking.metadata?.number_of_guests || 1;

  const rooms =
    booking.number_of_rooms || booking.metadata?.number_of_rooms || 1;

  const currency =
    booking.currency_code || booking.metadata?.currency_code || "USD";
  const totalAmount =
    booking.total_amount || booking.total || booking.metadata?.total_amount;

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>Booking Details</Heading>
        <Badge color={statusVariant as any}>
          {String(status).replace(/_/g, " ")}
        </Badge>
      </div>

      <div className="px-6 py-4 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="text-sm text-ui-fg-subtle">Hotel</div>
          <div className="font-medium">{hotelName}</div>
        </div>
        <div>
          <div className="text-sm text-ui-fg-subtle">Room Type</div>
          <div className="font-medium">{roomType}</div>
        </div>
        <div>
          <div className="text-sm text-ui-fg-subtle">Check-in</div>
          <div className="font-medium">
            {dateStr(booking.check_in_date || booking.metadata?.check_in_date)}{" "}
            {booking.check_in_time || booking.metadata?.check_in_time || ""}
          </div>
        </div>
        <div>
          <div className="text-sm text-ui-fg-subtle">Check-out</div>
          <div className="font-medium">
            {dateStr(
              booking.check_out_date || booking.metadata?.check_out_date
            )}{" "}
            {booking.check_out_time || booking.metadata?.check_out_time || ""}
          </div>
        </div>
        <div>
          <div className="text-sm text-ui-fg-subtle">Guests</div>
          <div className="font-medium">{guests}</div>
        </div>
        <div>
          <div className="text-sm text-ui-fg-subtle">Rooms</div>
          <div className="font-medium">{rooms}</div>
        </div>
        {totalAmount ? (
          <div className="md:col-span-2">
            <div className="text-sm text-ui-fg-subtle">Total Amount</div>
            <div className="font-semibold text-lg">
              {new Intl.NumberFormat("en-US", {
                style: "currency",
                currency,
              }).format(Number(totalAmount) || 0)}
            </div>
          </div>
        ) : null}
      </div>
    </Container>
  );
};

const HotelBookingDetailPageClient = () => {
  const { id: bookingId } = useParams();
  const navigate = useNavigate();

  const [booking, setBooking] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [addOns, setAddOns] = useState<any[]>([]);
  const [currentAssignedTo] = useState<string>(""); // no hotel-specific assignment controls yet
  const [xeroDetails, setXeroDetails] = useState<any | null>(null);
  const [isSyncingXero, setIsSyncingXero] = useState(false);
  const [isLoadingXero, setIsLoadingXero] = useState(false);

  // Payment Link success handler
  const handlePaymentLinkSuccess = (paymentLink: any) => {
    setBooking((prev: any) => {
      const prevMeta = (prev && prev.metadata) || {};
      return {
        ...prev,
        metadata: {
          ...prevMeta,
          payment_link: paymentLink?.url || prevMeta?.payment_link,
          payment_link_id: paymentLink?.id || prevMeta?.payment_link_id,
          payment_link_expires_at: paymentLink?.expires_at || prevMeta?.payment_link_expires_at,
        },
      };
    });
    toast.success("Payment Link", { description: "Payment link generated successfully" });
  };

  // Fetch hotel booking details
  const fetchBookingDetails = async () => {
    if (!bookingId) {
      setIsLoading(false);
      return;
    }
    try {
      setIsLoading(true);
      const res = await fetch(`/admin/hotel-management/bookings/${bookingId}`);
      if (!res.ok) {
        throw new Error(
          `Failed to fetch booking: ${res.status} ${res.statusText}`
        );
      }
      const data = await res.json();
      if (!data.booking) {
        throw new Error("No booking data in response");
      }

      const processed = { ...data.booking };

      // Ensure metadata parsed
      if (typeof processed.metadata === "string") {
        try {
          processed.metadata = JSON.parse(processed.metadata);
        } catch {
          // ignore
        }
      }

      setBooking(processed);
      setAddOns(Array.isArray(data.add_ons) ? data.add_ons : []);

      const hotelId = processed.hotel_id || processed.metadata?.hotel_id;
      if (hotelId) {
        try {
          const hRes = await fetch(`/admin/hotel-management/hotels/${hotelId}`);
          if (hRes.ok) {
            const h = await hRes.json();
            setHotelDetails(h.hotel || h.hotels || null);
          }
        } catch {
          // ignore
        }
      }
    } catch (err: any) {
      console.error(err);
      toast.error("Error", {
        description: err.message || "Failed to load booking",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBookingDetails();
  }, [bookingId]);

  // If API requires order_id from booking, attempt a fallback from metadata as well
  const effectiveOrderId = useMemo(() => {
    return booking?.id || booking?.order_id || booking?.metadata?.order_id || booking?.order?.id || booking?.order?.display_id;
  }, [booking]);

  // Xero helpers
  const fetchXeroDetails = async () => {
    console.log("Fetching Xero details for order ID:", effectiveOrderId);
    if (!effectiveOrderId) return;
    setIsLoadingXero(true);
    try {
      const res = await fetch(`https://dev.one.flinkk.io/api/module/orders/${encodeURIComponent(effectiveOrderId as string)}/get-xero-details`, {
        headers: {
          "X-Tenant-ID": "687600adbf0038b51be30359",
        },
      });
      if (!res.ok) throw new Error("Failed to fetch Xero details");
      const data = await res.json();
      setXeroDetails(data);
    } catch (e: any) {
      console.error(e);
      toast.error("Xero", { description: e.message || "Could not load Xero details" });
      setXeroDetails(null);
    } finally {
      setIsLoadingXero(false);
    }
  };

  const handleCreateInvoiceInXero = async () => {
    if (!effectiveOrderId) {
      toast.error("Xero", { description: "Order ID missing for this booking" });
      return;
    }
    setIsSyncingXero(true);
    try {
      const res = await fetch(`https://dev.one.flinkk.io/api/module/orders/${encodeURIComponent(effectiveOrderId as string)}/sync-to-xero`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Tenant-ID": "687600adbf0038b51be30359",
        },
      });
      if (!res.ok) throw new Error("Failed to sync to Xero");
      toast.success("Xero", { description: "Draft invoice created in Xero" });
      await fetchXeroDetails();
    } catch (e: any) {
      console.error(e);
      toast.error("Xero", { description: e.message || "Sync to Xero failed" });
    } finally {
      setIsSyncingXero(false);
    }
  };

  const handleViewInXero = () => {
    const ref = xeroDetails?.mapping?.xeroEntityId;
    if (!ref) {
      toast.error("No Xero mapping reference available.");
      return;
    }
    const xeroUrl = `https://go.xero.com/app/invoices/edit/${encodeURIComponent(ref)}`;
    window.open(xeroUrl, "_blank", "noopener,noreferrer");
  };

  console.log({effectiveOrderId})

  useEffect(() => {
    // Refetch Xero details whenever we have an effective order id
    if (effectiveOrderId) {
      fetchXeroDetails();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [effectiveOrderId]);



  const headerGuestName = useMemo(() => {
    return booking?.guest_name ||
      booking?.metadata?.guest_name ||
      (booking?.order?.customer?.first_name &&
        booking?.order?.customer?.last_name)
      ? `${booking?.order?.customer?.first_name} ${booking?.order?.customer?.last_name}`
      : "Guest Booking";
  }, [booking]);

  const handleGoBack = () => navigate("/hotel-management/bookings");

  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="flex items-center justify-center py-8">
            <Spinner size="medium" />
            <div className="ml-4 text-muted-foreground">
              Loading booking details...
            </div>
          </div>
        </Container>
      </>
    );
  }

  if (!booking) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container>
          <div className="text-center py-8">
            <Heading>Booking Not Found</Heading>
            <Text className="mt-2">
              The booking you're looking for doesn't exist or has been removed.
            </Text>
            <Button className="mt-4" onClick={handleGoBack}>
              Back to Bookings
            </Button>
          </div>
        </Container>
      </>
    );
  }

  // Build a "booking-like" object compatible with concierge sections
  const compatBooking = {
    ...booking,
    guest_name: booking.guest_name || booking.metadata?.guest_name,
    guest_email:
      booking.guest_email || booking.metadata?.guest_email || booking.email,
    guest_phone: booking.guest_phone || booking.metadata?.guest_phone,
    hotel_id: booking.hotel_id || booking.metadata?.hotel_id,
    hotel_name: booking.hotel_name || booking.metadata?.hotel_name,
    room_type:
      booking.room_type ||
      booking.metadata?.room_type ||
      booking.metadata?.room_config_name,
    check_in_date: booking.check_in_date || booking.metadata?.check_in_date,
    check_out_date: booking.check_out_date || booking.metadata?.check_out_date,
    check_in_time: booking.check_in_time || booking.metadata?.check_in_time,
    check_out_time: booking.check_out_time || booking.metadata?.check_out_time,
    number_of_guests:
      booking.number_of_guests || booking.metadata?.number_of_guests,
    number_of_rooms:
      booking.number_of_rooms || booking.metadata?.number_of_rooms,
    currency_code:
      booking.currency_code || booking.metadata?.currency_code || "USD",
    total_amount:
      booking.total_amount || booking.total || booking.metadata?.total_amount,
    concierge_order_items: addOns || [],
    order: booking.order, // if present
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <RoleGuard
        requirePermission="bookings:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view booking details.
              </Text>
            </div>
          </Container>
        }
      >
        <TwoColumnPage
          widgets={{ before: [], after: [], sideBefore: [], sideAfter: [] }}
          showJSON={false}
          showMetadata={false}
          data={compatBooking}
          hasOutlet={false}
        >
          <TwoColumnPage.Main>
            <ConciergeBookingCustomerSection booking={compatBooking} />

            <HotelBookingDetailsBlock booking={compatBooking} />

            <HotelBookingAddOnsTable
              booking={compatBooking}
              addOns={compatBooking.concierge_order_items || []}
            />
          </TwoColumnPage.Main>

          <TwoColumnPage.Sidebar>
            {/* Payment Link panel */}
            <Container className="divide-y p-0">
              <div className="flex items-center justify-between px-6 py-4">
                <Heading level="h3">Payment Link</Heading>
              </div>
              <div className="px-6 py-4 space-y-3">
                <Text>
                  Generate a payment link for this booking and share it with the guest to complete payment for this order.
                </Text>
                {/* Generate or Copy via shared component */}
                <PaymentLinkButton
                  bookingId={bookingId || ""}
                  onSuccess={handlePaymentLinkSuccess}
                />

                {/* Update Traveler Info - removed as per requirements */}

                {/* Download Invoice - removed as per requirements */}

                {/* Cancel Booking - removed as per requirements */}
              </div>
            </Container>

            {/* Xero Invoice Panel */}
            <Container className="divide-y p-0">
              <div className="flex items-center justify-between px-6 py-4">
                <Heading level="h3">Xero Invoice</Heading>
              </div>
              <div className="px-6 py-4 space-y-3">
                {xeroDetails?.hasMapping ? (
                  <>
                    <Text>
                      A Xero invoice has been created for this order. You can open it directly in Xero to review, edit, or send it to your customer.
                    </Text>
                    <Button
                      variant="secondary"
                      onClick={handleViewInXero}
                      disabled={!xeroDetails?.mapping?.xeroEntityId || isLoadingXero}
                    >
                      View in Xero
                    </Button>
                  </>
                ) : (
                  <>
                    <Text>
                      Sync this booking to your connected Xero organization. Click “Create Invoice in Xero” to create a draft invoice using the items and details from this order.
                    </Text>
                    <Button
                      variant="primary"
                      onClick={handleCreateInvoiceInXero}
                      disabled={!effectiveOrderId || isSyncingXero}
                    >
                      {isSyncingXero ? "Creating..." : "Create Invoice in Xero"}
                    </Button>
                  </>
                )}
              </div>
            </Container>
          </TwoColumnPage.Sidebar>
        </TwoColumnPage>
      </RoleGuard>
    </>
  );
};

export default HotelBookingDetailPageClient;
