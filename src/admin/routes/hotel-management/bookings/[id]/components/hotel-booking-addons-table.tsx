import { useMemo } from "react";
import {
  Container,
  <PERSON>ing,
  Badge,
  Text,
  createDataTableColumnHelper,
} from "@camped-ai/ui";
import { Package } from "lucide-react";
import { DataTable } from "../../../../../../components/table/data-table";
import { useDataTable } from "../../../../../../hooks/use-data-table";
import { formatCurrencyDisplay } from "../../../../../../utils/currency-helpers";

type HotelAddOnItem = {
  id: string;
  title?: string;
  add_on_name?: string;
  quantity?: number;
  unit_price?: number; // expected to be smallest unit if consistent with backend
  total_price?: number; // optional precomputed total
  currency_code?: string;
  metadata?: Record<string, any>;
  add_on_metadata?: Record<string, any>;
  status?: string;
  notes?: string | null;

  // Optional contextual fields sometimes present on concierge data
  order_line_item?: { metadata?: Record<string, any> } | null;
  order_item?: { metadata?: Record<string, any> } | null;
};

interface HotelBookingAddOnsTableProps {
  booking: any;
  addOns: HotelAddOnItem[];
}

const PAGE_SIZE = 10;

const columnHelper = createDataTableColumnHelper<HotelAddOnItem>();

const useColumns = () => {
  return [
    columnHelper.display({
      id: "service_name",
      header: "Service",
      cell: ({ row }) => {
        const addon = row.original;

        // Base name
        let serviceName = addon.title || addon.add_on_name || "Unknown Service";

        // If the title is generic "Product Item", try to get a better label from metadata commonly used for hotel add-ons
        if (serviceName === "Product Item") {
          const tryExtract = (metadata?: Record<string, any> | null) => {
            if (!metadata) return null;
            return (
              metadata.room_config_name ||
              metadata.room_name ||
              metadata.product_name ||
              metadata.service_name ||
              metadata.variant_title ||
              metadata.title ||
              null
            );
          };

          const betterFromLine = tryExtract(addon.order_line_item?.metadata);
          const betterFromItem = tryExtract(addon.order_item?.metadata);
          const betterFromAddon = tryExtract(addon.add_on_metadata);

          serviceName =
            betterFromLine ||
            betterFromItem ||
            betterFromAddon ||
            serviceName;
        }

        return (
          <div className="w-[200px]">
            <div className="font-medium text-ui-fg-base leading-tight">
              {serviceName}
            </div>
          </div>
        );
      },
    }),
    columnHelper.accessor("quantity", {
      header: "Qty",
      cell: ({ row }) => {
        const addon = row.original;
        return <div className="text-ui-fg-base">{addon.quantity ?? 1}</div>;
      },
    }),
    columnHelper.accessor("unit_price", {
      header: "Unit Price",
      cell: ({ row }) => {
        const addon = row.original;
        const currencyCode =
          addon.metadata?.currency_code ||
          addon.currency_code ||
          "GBP";
        return (
          <div className="text-ui-fg-base">
            {formatCurrencyDisplay(addon.unit_price || 0, currencyCode)}
          </div>
        );
      },
    }),
    columnHelper.accessor("status", {
      header: "Status",
      cell: ({ row }) => {
        const addon = row.original;
        const color = getStatusColor(addon.status);
        return (
          <Badge color={color as any}>
            {formatStatus(addon.status)}
          </Badge>
        );
      },
    }),
    columnHelper.accessor("notes", {
      header: "Notes",
      cell: ({ row }) => {
        const addon = row.original;
        return (
          <div className="text-ui-fg-base text-sm">
            {addon.notes || "-"}
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "total_price",
      header: "Total",
      cell: ({ row }) => {
        const addon = row.original;
        const totalPrice =
          addon.total_price || (addon.unit_price || 0) * (addon.quantity || 1);
        const currency =
          addon.metadata?.currency_code || addon.currency_code || "GBP";
        return (
          <div className="font-semibold text-ui-fg-base">
            {formatCurrencyDisplay(totalPrice, currency)}
          </div>
        );
      },
    }),
  ];
};

const getStatusColor = (status?: string) => {
  switch ((status || "").toLowerCase()) {
    case "confirmed":
    case "paid":
    case "completed":
    case "client_confirmed":
    case "active":
      return "green";
    case "pending":
    case "under_review":
    case "awaiting_payment":
    case "in_progress":
      return "orange";
    case "cancelled":
    case "canceled":
    case "inactive":
    case "no_show":
      return "red";
    case "order_placed":
    case "checked_out":
      return "purple";
    case "checked_in":
      return "blue";
    default:
      return "grey";
  }
};

const formatStatus = (status?: string) =>
  (status || "unknown").replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());

const HotelBookingAddOnsTable = ({ booking, addOns }: HotelBookingAddOnsTableProps) => {
  const columns = useColumns();

  const { table } = useDataTable({
    data: addOns || [],
    columns,
    count: addOns?.length || 0,
    pageSize: PAGE_SIZE,
    getRowId: (row) => row.id,
  });

  // Calculate total amount
  const totalAmount = useMemo(() => {
    return (
      addOns?.reduce((sum, addon) => {
        const itemTotal =
          addon.total_price || (addon.unit_price || 0) * (addon.quantity || 1);
        return sum + itemTotal;
      }, 0) || 0
    );
  }, [addOns]);

  // Get currency from first addon, booking, or default
  const currency = useMemo(() => {
    return (
      addOns?.[0]?.metadata?.currency_code ||
      addOns?.[0]?.currency_code ||
      booking?.currency_code ||
      "GBP"
    );
  }, [addOns, booking]);

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center gap-3">
          <Heading>Add-ons & Services</Heading>
          <Badge color="grey" size="small">
            <Package className="h-3 w-3 mr-1" />
            {addOns?.length || 0} Item{(addOns?.length || 0) !== 1 ? "s" : ""}
          </Badge>
        </div>
        {/* Read-only component: no actions */}
      </div>

      <DataTable
        table={table}
        columns={columns}
        pageSize={PAGE_SIZE}
        count={addOns?.length || 0}
        isLoading={false}
        pagination={false}
        queryObject={{}}
        noRecords={{
          title: "No add-ons",
          message: "No add-ons have been booked for this reservation.",
        }}
      />

      {/* Total Summary - Only show if there are add-ons */}
      {addOns && addOns.length > 0 && (
        <div className="px-6 py-4">
          <div className="flex justify-end">
            <div className="bg-ui-bg-subtle p-4 rounded-lg">
              <div className="flex items-center justify-between gap-8">
                <Text className="font-medium">Total Add-ons & Services:</Text>
                <Text className="font-bold text-lg">
                  {formatCurrencyDisplay(totalAmount, currency)}
                </Text>
              </div>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

export default HotelBookingAddOnsTable;