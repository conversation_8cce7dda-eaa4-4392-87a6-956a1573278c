import { z } from "zod";

// Test the margin validation schema
const marginSchema = z
  .union([z.number(), z.string()])
  .optional()
  .transform((val) => {
    if (val === undefined || val === null || val === "") return undefined;
    const num = typeof val === "string" ? parseFloat(val) : val;
    return isNaN(num) ? undefined : num;
  })
  .refine((val) => val === undefined || (val >= 0 && val <= 100), {
    message: "Margin must be between 0 and 100",
  });

describe('Hotel Creation Margin Field Validation', () => {
  it('should accept valid number values', () => {
    expect(marginSchema.parse(25)).toBe(25);
    expect(marginSchema.parse(0)).toBe(0);
    expect(marginSchema.parse(100)).toBe(100);
    expect(marginSchema.parse(50.5)).toBe(50.5);
  });

  it('should accept valid string values and convert to numbers', () => {
    expect(marginSchema.parse("25")).toBe(25);
    expect(marginSchema.parse("0")).toBe(0);
    expect(marginSchema.parse("100")).toBe(100);
    expect(marginSchema.parse("50.5")).toBe(50.5);
  });

  it('should accept undefined and empty values', () => {
    expect(marginSchema.parse(undefined)).toBe(undefined);
    expect(marginSchema.parse("")).toBe(undefined);
    expect(marginSchema.parse(null)).toBe(undefined);
  });

  it('should reject values outside valid range', () => {
    expect(() => marginSchema.parse(-1)).toThrow("Margin must be between 0 and 100");
    expect(() => marginSchema.parse(101)).toThrow("Margin must be between 0 and 100");
    expect(() => marginSchema.parse("-1")).toThrow("Margin must be between 0 and 100");
    expect(() => marginSchema.parse("101")).toThrow("Margin must be between 0 and 100");
  });

  it('should handle invalid string values', () => {
    expect(marginSchema.parse("invalid")).toBe(undefined);
    expect(marginSchema.parse("abc")).toBe(undefined);
  });

  it('should handle auto-populated values from destination', () => {
    // Simulate auto-populated values that might come from API
    const destinationMargin = 15; // Number from API
    expect(marginSchema.parse(destinationMargin)).toBe(15);
    
    // Simulate string values from API
    const destinationMarginString = "15";
    expect(marginSchema.parse(destinationMarginString)).toBe(15);
  });
});
