import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Buildings } from "@camped-ai/icons";
import { Toaster, toast, Container } from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../../components/rbac/RoleGuard";
import { useRbac } from "../../../../../hooks/use-rbac";
import PackageForm from "../../components/PackageForm";

interface Package {
  id: string;
  package_lookup_id: string;
  description: string;
  valid_from: string;
  valid_to: string;
  destinations: string[];
}

interface PackageFormData {
  package_lookup_id: string;
  description: string;
  valid_from: Date | null;
  valid_to: Date | null;
  destination_ids: string[];
}

const EditPackagePage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [packageData, setPackageData] = useState<Package | null>(null);

  // Check permission and redirect if not authorized
  if (!hasPermission("package_management:update")) {
    navigate("/package-management/packages");
    return null;
  }

  if (!id) {
    navigate("/package-management/packages");
    return null;
  }

  // Fetch package data
  useEffect(() => {
    const fetchPackage = async () => {
      try {
        setLoading(true);
        console.log("Fetching package with ID:", id);

        const response = await fetch(`/admin/packages/${id}`, {
          credentials: "include",
        });

        if (!response.ok) {
          if (response.status === 404) {
            toast.error("Package not found");
            navigate("/package-management/packages");
            return;
          }
          throw new Error("Failed to fetch package");
        }

        const data = await response.json();
        console.log("Fetched package data:", data);
        
        setPackageData(data.package);
      } catch (error) {
        console.error("Error fetching package:", error);
        toast.error("Failed to load package data");
        navigate("/package-management/packages");
      } finally {
        setLoading(false);
      }
    };

    fetchPackage();
  }, [id, navigate]);

  const handleSubmit = async (formData: PackageFormData) => {
    try {
      setIsSubmitting(true);

      // Prepare data for API
      const updateData = {
        package_lookup_id: formData.package_lookup_id,
        description: formData.description,
        valid_from: formData.valid_from!.toISOString(),
        valid_to: formData.valid_to!.toISOString(),
        destination_ids: formData.destination_ids,
      };

      console.log("Updating package with data:", updateData);

      // Call API to update package
      const response = await fetch(`/admin/packages/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update package");
      }

      const result = await response.json();
      console.log("Package updated successfully:", result);

      toast.success("Package updated successfully");
      
      // Navigate back to packages list
      navigate("/package-management/packages");
    } catch (error) {
      console.error("Error updating package:", error);
      throw error; // Re-throw to let PackageForm handle the error display
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/package-management/packages");
  };

  // Show loading state
  if (loading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-sm text-gray-600">Loading package data...</span>
          </div>
        </Container>
      </>
    );
  }

  // Show error state if package not found
  if (!packageData) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="p-6">
          <div className="text-center py-12">
            <p className="text-gray-500">Package not found</p>
          </div>
        </Container>
      </>
    );
  }

  // Prepare initial data for the form
  const initialData = {
    package_lookup_id: packageData.package_lookup_id,
    description: packageData.description,
    valid_from: new Date(packageData.valid_from),
    valid_to: new Date(packageData.valid_to),
    destination_ids: packageData.destinations,
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard requiredPermissions={["package_management:update"]}>
        <PackageForm
          title="Edit Package"
          description="Update package details, validity period, and destinations."
          submitButtonText="Update Package"
          initialData={initialData}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Package",
  icon: Buildings,
});

export default EditPackagePage;
