import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Buildings } from "@camped-ai/icons";
import { Toaster, toast } from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import { useRbac } from "../../../../hooks/use-rbac";
import PackageForm from "../components/PackageForm";

interface PackageFormData {
  package_lookup_id: string;
  description: string;
  valid_from: Date | null;
  valid_to: Date | null;
  destination_ids: string[];
}

const CreatePackagePage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check permission and redirect if not authorized
  if (!hasPermission("package_management:create")) {
    navigate("/package-management/packages");
    return null;
  }

  const handleSubmit = async (formData: PackageFormData) => {
    try {
      setIsSubmitting(true);

      // Prepare data for API
      const packageData = {
        package_lookup_id: formData.package_lookup_id,
        description: formData.description,
        valid_from_date: formData.valid_from!.toISOString(),
        valid_to_date: formData.valid_to!.toISOString(),
        destination_ids: formData.destination_ids,
      };

      console.log("Creating package with data:", packageData);

      // Call API to create package
      const response = await fetch("/admin/packages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(packageData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create package");
      }

      const result = await response.json();
      console.log("Package created successfully:", result);

      toast.success("Package created successfully");
      
      // Navigate back to packages list
      navigate("/package-management/packages");
    } catch (error) {
      console.error("Error creating package:", error);
      throw error; // Re-throw to let PackageForm handle the error display
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/package-management/packages");
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard requiredPermissions={["package_management:create"]}>
        <PackageForm
          title="Create Package"
          description="Create a new package instance with validity period and destinations."
          submitButtonText="Create Package"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Create Package",
  icon: Buildings,
});

export default CreatePackagePage;
