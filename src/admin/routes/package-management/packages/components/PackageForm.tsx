import React, { useState, useEffect } from "react";
import {
  Heading,
  Text,
  Button,
  Label,
  Textarea,
  Select,
  Container,
  toast,
  DatePicker,
} from "@camped-ai/ui";
import { ArrowLeft } from "@camped-ai/icons";
import { useNavigate } from "react-router-dom";
import { MultiSelect } from "../../../../components/common/MultiSelect";

interface PackageLookup {
  id: string;
  package_name: string;
  description: string;
  is_active: boolean;
}

interface Destination {
  id: string;
  name: string;
  handle: string;
}

interface PackageFormData {
  package_lookup_id: string;
  description: string;
  valid_from: Date | null;
  valid_to: Date | null;
  destination_ids: string[];
}

interface PackageFormProps {
  initialData?: Partial<PackageFormData>;
  onSubmit: (data: PackageFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  title: string;
  description: string;
  submitButtonText: string;
}

const PackageForm: React.FC<PackageFormProps> = ({
  initialData = {},
  onSubmit,
  onCancel,
  isSubmitting = false,
  title,
  description,
  submitButtonText,
}) => {
  const navigate = useNavigate();
  
  // Form state
  const [formData, setFormData] = useState<PackageFormData>({
    package_lookup_id: initialData.package_lookup_id || "",
    description: initialData.description || "",
    valid_from: initialData.valid_from || null,
    valid_to: initialData.valid_to || null,
    destination_ids: initialData.destination_ids || [],
  });

  // Data loading states
  const [packageLookups, setPackageLookups] = useState<PackageLookup[]>([]);
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [loadingLookups, setLoadingLookups] = useState(false);
  const [loadingDestinations, setLoadingDestinations] = useState(false);

  // Fetch package lookups (active only)
  const fetchPackageLookups = async () => {
    try {
      setLoadingLookups(true);
      const response = await fetch("/admin/package-lookups?is_active=true");
      if (!response.ok) {
        throw new Error("Failed to fetch package lookups");
      }
      const data = await response.json();
      setPackageLookups(data.package_lookups || []);
    } catch (error) {
      console.error("Error fetching package lookups:", error);
      toast.error("Failed to load package lookups");
    } finally {
      setLoadingLookups(false);
    }
  };

  // Fetch destinations
  const fetchDestinations = async () => {
    try {
      setLoadingDestinations(true);
      const response = await fetch("/admin/hotel-management/destinations");
      if (!response.ok) {
        throw new Error("Failed to fetch destinations");
      }
      const data = await response.json();
      setDestinations(data.destinations || []);
    } catch (error) {
      console.error("Error fetching destinations:", error);
      toast.error("Failed to load destinations");
    } finally {
      setLoadingDestinations(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchPackageLookups();
    fetchDestinations();
  }, []);

  // Update form data when initialData changes
  useEffect(() => {
    // Only update if initialData has actual content (not just an empty object)
    if (initialData && Object.keys(initialData).length > 0) {
      setFormData({
        package_lookup_id: initialData.package_lookup_id || "",
        description: initialData.description || "",
        valid_from: initialData.valid_from || null,
        valid_to: initialData.valid_to || null,
        destination_ids: initialData.destination_ids || [],
      });
    }
  }, [initialData]);

  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (!formData.package_lookup_id || !formData.description || !formData.valid_from || !formData.valid_to || formData.destination_ids.length === 0) {
        toast.error("Please fill in all required fields");
        return;
      }

      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(error instanceof Error ? error.message : "Failed to submit form");
    }
  };

  const destinationOptions = destinations.map(dest => ({
    value: dest.id,
    label: dest.name,
  }));

  return (
    <div className="min-h-screen bg-ui-bg-subtle">
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center gap-4 px-6 py-4">
          <Button
            variant="secondary"
            size="small"
            onClick={onCancel}
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <Heading level="h1">{title}</Heading>
            <Text className="text-ui-fg-subtle mt-1">{description}</Text>
          </div>
        </div>

        {/* Basic Information Section */}
        <div className="px-6 py-4">
          <div className="flex items-center justify-between mb-6">
            <Heading level="h2">Basic Information</Heading>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Package Lookup */}
            <div className="space-y-2">
              <Label htmlFor="package_lookup">Package Lookup *</Label>
              <Select
                value={formData.package_lookup_id}
                onValueChange={(value) => setFormData(prev => ({ ...prev, package_lookup_id: value }))}
                disabled={loadingLookups}
              >
                <Select.Trigger>
                  <Select.Value placeholder={loadingLookups ? "Loading..." : "Select package lookup"} />
                </Select.Trigger>
                <Select.Content>
                  {packageLookups.map((lookup) => (
                    <Select.Item key={lookup.id} value={lookup.id}>
                      {lookup.package_name}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            </div>
          </div>

          {/* Description - Full Width */}
          <div className="mt-6 space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              placeholder="Enter package description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
            />
          </div>
        </div>

        {/* Validity Period Section */}
        <div className="px-6 py-4">
          <div className="flex items-center justify-between mb-6">
            <Heading level="h2">Validity Period</Heading>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="valid_from">Valid From *</Label>
              <DatePicker
                value={formData.valid_from || undefined}
                onChange={(date) => setFormData(prev => ({ ...prev, valid_from: date }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="valid_to">Valid To *</Label>
              <DatePicker
                value={formData.valid_to || undefined}
                onChange={(date) => setFormData(prev => ({ ...prev, valid_to: date }))}
              />
            </div>
          </div>
        </div>

        {/* Destinations Section */}
        <div className="px-6 py-4">
          <div className="flex items-center justify-between mb-6">
            <Heading level="h2">Destinations</Heading>
          </div>
          <div className="space-y-2">
            <Label htmlFor="destinations">Select Destinations *</Label>
            <MultiSelect
              options={destinationOptions}
              selectedValues={formData.destination_ids}
              onChange={(values) => setFormData(prev => ({ ...prev, destination_ids: values }))}
              placeholder={loadingDestinations ? "Loading destinations..." : "Select destinations"}
              disabled={loadingDestinations}
              showSelectAll={true}
              showSelectedTags={true}
            />
            <Text size="small" className="text-ui-fg-subtle mt-2">
              Select the destinations where this package will be available.
            </Text>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-ui-border-base">
            <Button
              variant="secondary"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={
                isSubmitting ||
                !formData.package_lookup_id ||
                !formData.description ||
                !formData.valid_from ||
                !formData.valid_to ||
                formData.destination_ids.length === 0
              }
            >
              {isSubmitting ? "Submitting..." : submitButtonText}
            </Button>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default PackageForm;
