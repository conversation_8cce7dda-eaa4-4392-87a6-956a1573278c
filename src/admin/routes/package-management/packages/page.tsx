import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Buildings,
  PlusMini,
} from "@camped-ai/icons";
import {
  Edit,
  Trash2,
  Calendar,
  MapPin,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  IconButton,
  Prompt,
} from "@camped-ai/ui";
import { useState, useEffect, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useReactTable, getCoreRowModel, ColumnDef } from "@tanstack/react-table";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";


// Add custom scrollbar styles
const scrollbarStyles = `
  .scrollbar-thin::-webkit-scrollbar {
    height: 6px;
  }
  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
`;

interface Package {
  id: string;
  package_lookup_id: string;
  package_lookup_name?: string; // Add this to store the package lookup name
  description: string;
  valid_from: string;
  valid_to: string;
  destinations: string[];
  created_at: string;
  updated_at: string;
}

const PackagesPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [searchParams] = useSearchParams();
  const [packages, setPackages] = useState<Package[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletePromptOpen, setDeletePromptOpen] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);

  // State for destination mapping (used for display purposes)
  const [destinationMap, setDestinationMap] = useState<Map<string, string>>(new Map());

  // State for package lookup mapping (used for display purposes)
  const [packageLookupMap, setPackageLookupMap] = useState<Map<string, string>>(new Map());

  // Get search and filter parameters from URL
  const searchQuery = searchParams.get("q") || "";
  const destinationFilter = searchParams.get("destinations"); // This will be a destination ID
  const orderParam = searchParams.get("order") || "";



  // Fetch packages from API
  const fetchPackages = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (searchQuery) {
        queryParams.append("q", searchQuery);
      }
      if (destinationFilter) {
        queryParams.append("destinations", destinationFilter);
      }
      if (orderParam) {
        queryParams.append("order", orderParam);
      }

      const url = `/admin/packages${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;
      const response = await fetch(url);

      if (!response.ok) {
        // If API endpoint doesn't exist or returns error, just show empty state
        if (response.status === 404) {
          console.log("Packages API endpoint not found, showing empty state");
          setPackages([]);
          return;
        }
        throw new Error("Failed to fetch packages");
      }
      const data = await response.json();
      console.log("API Response:", data); // Debug log
      setPackages(data.packages || []);
    } catch (error) {
      console.error("Error fetching packages:", error);
      // For now, just show empty state instead of error toast
      // This allows the page to work even when API is not implemented
      setPackages([]);
      // Uncomment the line below when API is ready
      // toast.error("Failed to load packages");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
    fetchDestinations(); // Fetch destinations to populate the destination map
    fetchPackageLookups(); // Fetch package lookups to populate the package lookup map
  }, [destinationFilter, searchQuery, orderParam]); // Re-fetch when filters change

  // Apply client-side filtering and sorting
  const filteredPackages = useMemo(() => {
    let filtered = [...packages];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(pkg =>
        pkg.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pkg.package_lookup_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        packageLookupMap.get(pkg.package_lookup_id)?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pkg.destinations?.some(dest =>
          destinationMap.get(dest)?.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
    }

    // Apply destination filter
    if (destinationFilter) {
      filtered = filtered.filter(pkg =>
        pkg.destinations && pkg.destinations.includes(destinationFilter)
      );
    }

    // Apply sorting
    if (orderParam) {
      const isDescending = orderParam.startsWith("-");
      const sortKey = isDescending ? orderParam.slice(1) : orderParam;

      filtered.sort((a, b) => {
        let aValue: any = a[sortKey as keyof Package];
        let bValue: any = b[sortKey as keyof Package];

        // Handle package lookup name sorting
        if (sortKey === "package_lookup_id") {
          aValue = a.package_lookup_name || packageLookupMap.get(aValue) || "";
          bValue = b.package_lookup_name || packageLookupMap.get(bValue) || "";
        }

        // Handle date sorting
        if (sortKey === "valid_from" || sortKey === "valid_to") {
          aValue = new Date(aValue as string).getTime();
          bValue = new Date(bValue as string).getTime();
        } else if (typeof aValue === "string" && typeof bValue === "string") {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return isDescending ? 1 : -1;
        if (aValue > bValue) return isDescending ? -1 : 1;
        return 0;
      });
    }

    return filtered;
  }, [packages, searchQuery, destinationFilter, orderParam, destinationMap, packageLookupMap]);



  // Fetch destinations for creating destination map
  const fetchDestinations = async () => {
    try {
      const response = await fetch("/admin/hotel-management/destinations/list-basic");
      if (!response.ok) {
        throw new Error("Failed to fetch destinations");
      }
      const data = await response.json();
      const destinationsList = data.destinations || [];

      // Create a map for quick destination ID to name lookup
      const map = new Map<string, string>();
      destinationsList.forEach((dest: any) => {
        map.set(dest.id, dest.name);
      });
      setDestinationMap(map);
    } catch (error) {
      console.error("Error fetching destinations:", error);
      toast.error("Failed to load destinations");
    }
  };

  // Fetch package lookups for creating package lookup map
  const fetchPackageLookups = async () => {
    try {
      const response = await fetch("/admin/package-lookups");
      if (!response.ok) {
        throw new Error("Failed to fetch package lookups");
      }
      const data = await response.json();
      const packageLookupsList = data.package_lookups || [];

      // Create a map for quick package lookup ID to name lookup
      const map = new Map<string, string>();
      packageLookupsList.forEach((lookup: any) => {
        map.set(lookup.id, lookup.package_name);
      });
      setPackageLookupMap(map);
    } catch (error) {
      console.error("Error fetching package lookups:", error);
      toast.error("Failed to load package lookups");
    }
  };



  const handleDelete = async (pkg: Package) => {
    try {
      const response = await fetch(`/admin/packages/${pkg.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete package");
      }

      // Refresh the packages list
      await fetchPackages();

      toast.success("Package deleted successfully");
      setDeletePromptOpen(false);
      setSelectedPackage(null);
    } catch (error) {
      console.error("Error deleting package:", error);
      toast.error("Failed to delete package");
    }
  };

  const handleEdit = (pkg: Package) => {
    navigate(`/package-management/packages/${pkg.id}/edit`);
  };



  // Handle opening create page
  const handleCreatePackage = () => {
    navigate("/package-management/packages/create");
  };



  const columns: ColumnDef<Package>[] = [
    {
      header: "Package Name",
      accessorKey: "package_lookup_id",
      cell: ({ row }) => (
        <div>
          <Text weight="plus" size="small">
            {row.original.package_lookup_name ||
             packageLookupMap.get(row.original.package_lookup_id) ||
             "Unknown Package"}
          </Text>
        </div>
      ),
    },
    {
      header: "Description",
      accessorKey: "description",
      cell: ({ row }) => (
        <Text size="small" className="text-gray-600">
          {row.original.description}
        </Text>
      ),
    },
    {
      header: "Validity Period",
      accessorKey: "valid_from",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="h-3 w-3" />
            <Text size="small">
              {new Date(row.original.valid_from).toLocaleDateString()} - {new Date(row.original.valid_to).toLocaleDateString()}
            </Text>
          </div>
        );
      },
    },
    {
      header: "Destinations",
      accessorKey: "destinations",
      cell: ({ row }) => (
        <div className="flex items-start gap-1 min-w-0 max-w-sm">
          <MapPin className="h-3 w-3 mt-1 text-gray-500 flex-shrink-0" />
          <div className="flex gap-1 overflow-x-auto scrollbar-thin pb-1" style={{ scrollbarWidth: 'thin' }}>
            {(row.original.destinations || []).map((destId: string, index: number) => {
              const destinationName = destinationMap.get(destId) || destId;
              return (
                <Badge key={index} color="grey" size="small" className="flex-shrink-0 whitespace-nowrap">
                  {destinationName}
                </Badge>
              );
            })}
            {(!row.original.destinations || row.original.destinations.length === 0) && (
              <Text size="small" className="text-gray-500">No destinations</Text>
            )}
          </div>
        </div>
      ),
    },
    {
      header: "Created",
      accessorKey: "created_at",
      cell: ({ row }) => (
        <Text size="small" className="text-gray-600">
          {new Date(row.original.created_at).toLocaleDateString()}
        </Text>
      ),
    },
    {
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {hasPermission("package_management:update") && (
            <IconButton
              size="small"
              variant="transparent"
              onClick={() => handleEdit(row.original)}
            >
              <Edit className="h-4 w-4" />
            </IconButton>
          )}
          {hasPermission("package_management:delete") && (
            <IconButton
              size="small"
              variant="transparent"
              onClick={() => {
                setSelectedPackage(row.original);
                setDeletePromptOpen(true);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </IconButton>
          )}
        </div>
      ),
    },
  ];

  // Define filters
  const filters: Filter[] = useMemo(() => [
    {
      key: "destinations",
      label: "Destinations",
      type: "select",
      options: Array.from(destinationMap.entries()).map(([id, name]) => ({
        label: name,
        value: id,
      })),
    }
  ], [destinationMap]);

  // Define sortable columns
  const orderBy = useMemo(() => [
    { key: "package_lookup_id" as keyof Package, label: "Package Name" },
    { key: "description" as keyof Package, label: "Description" },
    { key: "valid_from" as keyof Package, label: "Valid From" },
    { key: "valid_to" as keyof Package, label: "Valid To" },
  ], []);

  // Create table instance
  const table = useReactTable({
    data: filteredPackages,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(filteredPackages.length / 20),
    state: {
      pagination: {
        pageIndex: 0,
        pageSize: 20,
      },
    },
  });

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: scrollbarStyles }} />
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="package_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view packages.
              </Text>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">

          {/* Header */}
          <div className="flex items-center justify-between px-6 py-4">
            <div>
              <Heading level="h2">Packages</Heading>
            </div>
            <div className="flex items-center gap-x-2">
              {hasPermission("package_management:create") && (
                <Button
                  size="small"
                  onClick={handleCreatePackage}
                >
                  <PlusMini />
                  Add Package
                </Button>
              )}
            </div>
          </div>

          {/* DataTable */}
          <div className="overflow-x-auto">
            <DataTable
              table={table}
              columns={columns}
              pageSize={20}
              count={filteredPackages.length}
              isLoading={loading}
              filters={filters}
              orderBy={orderBy}
              search="autofocus"
              pagination
              queryObject={Object.fromEntries(searchParams)}
              noRecords={{
                title: "No packages found",
                message: "No packages found. Create your first package to get started.",
              }}
            />
          </div>
        </Container>

        {/* Delete Confirmation Prompt */}
        <Prompt open={deletePromptOpen} onOpenChange={setDeletePromptOpen}>
          <Prompt.Content>
            <Prompt.Header>
              <Prompt.Title>Delete Package</Prompt.Title>
              <Prompt.Description>
                Are you sure you want to delete this package? This action cannot be undone.
              </Prompt.Description>
            </Prompt.Header>
            <Prompt.Footer>
              <Prompt.Cancel onClick={() => setDeletePromptOpen(false)}>
                Cancel
              </Prompt.Cancel>
              <Prompt.Action onClick={() => selectedPackage && handleDelete(selectedPackage)}>
                Delete
              </Prompt.Action>
            </Prompt.Footer>
          </Prompt.Content>
        </Prompt>




        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Packages",
  icon: Buildings,
});

export default PackagesPage;
