import React, { useState } from "react";
import {
  Heading,
  Text,
  Button,
  Label,
  Input,
  Textarea,
  Switch,
  Container,
  toast,
} from "@camped-ai/ui";
import { ArrowLeft } from "@camped-ai/icons";
import { useNavigate } from "react-router-dom";

interface PackageLookupFormData {
  package_name: string;
  description: string;
  is_active: boolean;
}

interface PackageLookupFormProps {
  initialData?: Partial<PackageLookupFormData>;
  onSubmit: (data: PackageLookupFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
  title: string;
  description: string;
  submitButtonText: string;
}

const PackageLookupForm: React.FC<PackageLookupFormProps> = ({
  initialData = {},
  onSubmit,
  onCancel,
  isSubmitting = false,
  title,
  description,
  submitButtonText,
}) => {
  const navigate = useNavigate();
  
  // Form state
  const [formData, setFormData] = useState<PackageLookupFormData>({
    package_name: initialData.package_name || "",
    description: initialData.description || "",
    is_active: initialData.is_active ?? true,
  });

  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (!formData.package_name.trim()) {
        toast.error("Package name is required");
        return;
      }

      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(error instanceof Error ? error.message : "Failed to submit form");
    }
  };

  return (
    <div className="min-h-screen bg-ui-bg-subtle">
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center gap-4 px-6 py-4">
          <Button
            variant="secondary"
            size="small"
            onClick={onCancel}
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <Heading level="h1">{title}</Heading>
            <Text className="text-ui-fg-subtle mt-1">{description}</Text>
          </div>
        </div>

        {/* Section Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Basic Information</Heading>
        </div>

        {/* Form Content */}
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Package Name */}
            <div className="space-y-2">
              <Label htmlFor="package_name">Package Name *</Label>
              <Input
                id="package_name"
                placeholder="Enter package name"
                value={formData.package_name}
                onChange={(e) => setFormData(prev => ({ ...prev, package_name: e.target.value }))}
              />
            </div>

            {/* Activity Status */}
            <div className="space-y-2">
              <Label htmlFor="is_active">Status</Label>
              <div className="flex items-center gap-2">
                <Switch
                  id="is_active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
                <Text size="small">Active</Text>
              </div>
            </div>
          </div>

          {/* Description - Full Width */}
          <div className="mt-6 space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter package description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            />
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-ui-border-base">
            <Button
              variant="secondary"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !formData.package_name.trim()}
            >
              {isSubmitting ? "Submitting..." : submitButtonText}
            </Button>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default PackageLookupForm;
