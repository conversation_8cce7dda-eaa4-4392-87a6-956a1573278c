import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Tag } from "@camped-ai/icons";
import { Toaster, toast } from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../components/rbac/RoleGuard";
import { useRbac } from "../../../../hooks/use-rbac";
import PackageLookupForm from "../components/PackageLookupForm";

interface PackageLookupFormData {
  package_name: string;
  description: string;
  is_active: boolean;
}

const CreatePackageLookupPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check permission and redirect if not authorized
  if (!hasPermission("package_management:create")) {
    navigate("/package-management/package-lookups");
    return null;
  }

  const handleSubmit = async (data: PackageLookupFormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/admin/package-lookups", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create package lookup");
      }

      const result = await response.json();
      toast.success("Package lookup created successfully");
      navigate("/package-management/package-lookups");
    } catch (error) {
      console.error("Error creating package lookup:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create package lookup");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/package-management/package-lookups");
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard requiredPermissions={["package_management:create"]}>
        <PackageLookupForm
          title="Create Package Lookup"
          description="Add a new package lookup to organize your packages."
          submitButtonText="Create Package Lookup"
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Create Package Lookup",
  icon: Tag,
});

export default CreatePackageLookupPage;
