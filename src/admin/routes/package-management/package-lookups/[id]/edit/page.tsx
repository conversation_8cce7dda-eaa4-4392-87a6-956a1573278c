import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Tag } from "@camped-ai/icons";
import { Toaster, toast } from "@camped-ai/ui";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../../../components/rbac/RoleGuard";
import { useRbac } from "../../../../../hooks/use-rbac";
import PackageLookupForm from "../../components/PackageLookupForm";

interface PackageLookup {
  id: string;
  package_name: string;
  description: string;
  is_active: boolean;
}

interface PackageLookupFormData {
  package_name: string;
  description: string;
  is_active: boolean;
}

const EditPackageLookupPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [packageLookupData, setPackageLookupData] = useState<PackageLookup | null>(null);

  // Check permission and redirect if not authorized
  if (!hasPermission("package_management:update")) {
    navigate("/package-management/package-lookups");
    return null;
  }

  if (!id) {
    navigate("/package-management/package-lookups");
    return null;
  }

  // Fetch package lookup data
  useEffect(() => {
    const fetchPackageLookup = async () => {
      try {
        const response = await fetch(`/admin/package-lookups/${id}`);
        if (!response.ok) {
          throw new Error("Failed to fetch package lookup");
        }
        const data = await response.json();
        setPackageLookupData(data.package_lookup);
      } catch (error) {
        console.error("Error fetching package lookup:", error);
        toast.error("Failed to load package lookup data");
        navigate("/package-management/package-lookups");
      } finally {
        setLoading(false);
      }
    };

    fetchPackageLookup();
  }, [id, navigate]);

  const handleSubmit = async (data: PackageLookupFormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/admin/package-lookups", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id,
          ...data,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update package lookup");
      }

      const result = await response.json();
      toast.success("Package lookup updated successfully");
      navigate("/package-management/package-lookups");
    } catch (error) {
      console.error("Error updating package lookup:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update package lookup");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate("/package-management/package-lookups");
  };

  if (loading) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-ui-fg-subtle">Loading...</div>
        </div>
      </>
    );
  }

  if (!packageLookupData) {
    return null;
  }

  // Prepare initial data for the form
  const initialData = {
    package_name: packageLookupData.package_name,
    description: packageLookupData.description || "",
    is_active: packageLookupData.is_active,
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard requiredPermissions={["package_management:update"]}>
        <PackageLookupForm
          title="Edit Package Lookup"
          description="Update the package lookup information."
          submitButtonText="Update Package Lookup"
          initialData={initialData}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
        />
        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Edit Package Lookup",
  icon: Tag,
});

export default EditPackageLookupPage;
