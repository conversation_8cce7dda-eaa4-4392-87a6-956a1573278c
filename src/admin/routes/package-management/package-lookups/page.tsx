import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Tag,
  PlusMini,
} from "@camped-ai/icons";
import {
  Edit,
  Trash2,
} from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  IconButton,
  Prompt,
} from "@camped-ai/ui";
import { useState, useEffect, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useReactTable, getCoreRowModel, ColumnDef } from "@tanstack/react-table";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";

interface PackageLookup {
  id: string;
  package_name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const PackageLookupsPage = () => {
  const { hasPermission } = useRbac();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [packageLookups, setPackageLookups] = useState<PackageLookup[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletePromptOpen, setDeletePromptOpen] = useState(false);
  const [selectedLookup, setSelectedLookup] = useState<PackageLookup | null>(null);

  // Get search and filter parameters from URL
  const searchQuery = searchParams.get("q") || "";
  const statusFilter = searchParams.get("is_active");
  const orderParam = searchParams.get("order") || "";

  // Fetch package lookups from API
  const fetchPackageLookups = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (statusFilter) {
        queryParams.append("is_active", statusFilter);
      }
      if (searchQuery) {
        queryParams.append("q", searchQuery);
      }
      if (orderParam) {
        queryParams.append("order", orderParam);
      }

      const url = `/admin/package-lookups${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error("Failed to fetch package lookups");
      }
      const data = await response.json();
      console.log("API Response:", data); // Debug log
      setPackageLookups(data.package_lookups || []);
    } catch (error) {
      console.error("Error fetching package lookups:", error);
      toast.error("Failed to load package lookups");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackageLookups();
  }, [statusFilter, searchQuery, orderParam]); // Re-fetch when filters change

  // Apply client-side filtering and sorting
  const filteredLookups = useMemo(() => {
    let filtered = [...packageLookups];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(lookup =>
        lookup.package_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lookup.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply sorting
    if (orderParam) {
      const isDescending = orderParam.startsWith("-");
      const sortKey = isDescending ? orderParam.slice(1) : orderParam;

      filtered.sort((a, b) => {
        let aValue = a[sortKey as keyof PackageLookup];
        let bValue = b[sortKey as keyof PackageLookup];

        // Handle different data types
        if (typeof aValue === "string" && typeof bValue === "string") {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return isDescending ? 1 : -1;
        if (aValue > bValue) return isDescending ? -1 : 1;
        return 0;
      });
    }

    return filtered;
  }, [packageLookups, searchQuery, orderParam]);

  const handleDelete = async (lookup: PackageLookup) => {
    try {
      const response = await fetch("/admin/package-lookups", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: [lookup.id] }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete package lookup");
      }

      // Refresh the data from server
      await fetchPackageLookups();

      toast.success("Package lookup deleted successfully");
      setDeletePromptOpen(false);
      setSelectedLookup(null);
    } catch (error) {
      console.error("Error deleting package lookup:", error);
      toast.error("Failed to delete package lookup");
    }
  };

  const handleEdit = (lookup: PackageLookup) => {
    navigate(`/package-management/package-lookups/${lookup.id}/edit`);
  };

  const handleCreate = () => {
    navigate("/package-management/package-lookups/create");
  };

  const columns: ColumnDef<PackageLookup>[] = [
    {
      header: "Package Name",
      accessorKey: "package_name",
      cell: ({ row }) => (
        <div>
          <Text weight="plus" size="small">
            {row.original.package_name}
          </Text>
        </div>
      ),
    },
    {
      header: "Description",
      accessorKey: "description",
      cell: ({ row }) => (
        <Text size="small" className="text-gray-600">
          {row.original.description}
        </Text>
      ),
    },
    {
      header: "Status",
      accessorKey: "is_active",
      cell: ({ row }) => (
        <Badge
          color={row.original.is_active ? "green" : "red"}
          size="small"
        >
          {row.original.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    },
    {
      header: "Created",
      accessorKey: "created_at",
      cell: ({ row }) => (
        <Text size="small" className="text-gray-600">
          {new Date(row.original.created_at).toLocaleDateString()}
        </Text>
      ),
    },
    {
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          {hasPermission("package_management:update") && (
            <IconButton
              size="small"
              variant="transparent"
              onClick={() => handleEdit(row.original)}
            >
              <Edit className="h-4 w-4" />
            </IconButton>
          )}
          {hasPermission("package_management:delete") && (
            <IconButton
              size="small"
              variant="transparent"
              onClick={() => {
                setSelectedLookup(row.original);
                setDeletePromptOpen(true);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </IconButton>
          )}
        </div>
      ),
    },
  ];

  // Define filters
  const filters: Filter[] = useMemo(() => [
    {
      key: "is_active",
      label: "Status",
      type: "select",
      options: [
        { label: "Active", value: "true" },
        { label: "Inactive", value: "false" },
      ],
    }
  ], []);

  // Define sortable columns
  const orderBy = useMemo(() => [
    { key: "package_name" as keyof PackageLookup, label: "Package Name" },
    { key: "is_active" as keyof PackageLookup, label: "Status" },
    { key: "created_at" as keyof PackageLookup, label: "Date Added" },
  ], []);

  // Create table instance
  const table = useReactTable({
    data: filteredLookups,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(filteredLookups.length / 20),
    state: {
      pagination: {
        pageIndex: 0,
        pageSize: 20,
      },
    },
  });

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="package_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view package lookups.
              </Text>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">


          {/* Header */}
          <div className="flex items-center justify-between px-6 py-4">
            <div>
              <Heading level="h2">Package Lookups</Heading>
            </div>
            <div className="flex items-center gap-x-2">
              {hasPermission("package_management:create") && (
                <Button
                  size="small"
                  onClick={handleCreate}
                >
                  <PlusMini />
                  Add Package Lookup
                </Button>
              )}
            </div>
          </div>

          {/* DataTable */}
          <DataTable
            table={table}
            columns={columns}
            pageSize={20}
            count={filteredLookups.length}
            isLoading={loading}
            filters={filters}
            orderBy={orderBy}
            search="autofocus"
            pagination
            queryObject={Object.fromEntries(searchParams)}
            noRecords={{
              title: "No package lookups found",
              message: "No package lookups found. Create your first package lookup to get started.",
            }}
          />
        </Container>

        {/* Delete Confirmation Prompt */}
        <Prompt open={deletePromptOpen} onOpenChange={setDeletePromptOpen}>
          <Prompt.Content>
            <Prompt.Header>
              <Prompt.Title>Delete Package Lookup</Prompt.Title>
              <Prompt.Description>
                Are you sure you want to delete "{selectedLookup?.package_name}"? This action cannot be undone.
              </Prompt.Description>
            </Prompt.Header>
            <Prompt.Footer>
              <Prompt.Cancel onClick={() => setDeletePromptOpen(false)}>
                Cancel
              </Prompt.Cancel>
              <Prompt.Action onClick={() => selectedLookup && handleDelete(selectedLookup)}>
                Delete
              </Prompt.Action>
            </Prompt.Footer>
          </Prompt.Content>
        </Prompt>



        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Package Lookups",
  icon: Tag,
});

export default PackageLookupsPage;
