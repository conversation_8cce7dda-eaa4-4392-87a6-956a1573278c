import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Buildings } from "@camped-ai/icons";
import {
  Container,
  Heading,
  Text,
  Toaster,
  toast,
} from "@camped-ai/ui";

import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../components/rbac/RoleGuard";
import { useRbac } from "../../hooks/use-rbac";

interface Metrics {
  total_package_lookups_count: number;
  active_package_lookups_count: number;
  inactive_package_lookups_count: number;
  total_packages_count: number;
  active_packages_count: number;
  expired_packages_count: number;
  upcoming_packages_count: number;
}

const PackageManagementPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [metrics, setMetrics] = useState<Metrics>({
    total_package_lookups_count: 0,
    active_package_lookups_count: 0,
    inactive_package_lookups_count: 0,
    total_packages_count: 0,
    active_packages_count: 0,
    expired_packages_count: 0,
    upcoming_packages_count: 0,
  });
  const [loading, setLoading] = useState(true);

  // Fetch metrics from API
  const fetchMetrics = async () => {
    try {
      setLoading(true);
      const response = await fetch("/admin/package-management/metrics");
      if (!response.ok) {
        throw new Error("Failed to fetch metrics");
      }
      const data = await response.json();
      setMetrics(data.metrics);
    } catch (error) {
      console.error("Error fetching metrics:", error);
      toast.error("Failed to load package management metrics");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, []);

  // Quick navigation cards for the dashboard
  const allNavigationCards = [
    {
      title: "Package Lookups",
      description: "Manage package lookup definitions and categories",
      href: "/package-management/package-lookups",
      icon: "📋",
      count: metrics?.total_package_lookups_count || 0,
      permission: "package_management:view",
    },
    {
      title: "Packages",
      description: "Manage package instances with validity periods",
      href: "/package-management/packages",
      icon: "📦",
      count: metrics?.total_packages_count || 0,
      permission: "package_management:view",
    },
  ];

  // Filter navigation cards based on permissions
  const navigationCards = allNavigationCards.filter((card) =>
    hasPermission(card.permission)
  );

  // Metrics cards for the top section
  const allMetricsCards = [
    {
      title: "Active Packages",
      count: metrics?.active_packages_count || 0,
      icon: "✅",
      description: "Currently active packages",
      permission: "package_management:view",
      color: "green",
    },
    {
      title: "Upcoming Packages",
      count: metrics?.upcoming_packages_count || 0,
      icon: "🔮",
      description: "Packages not yet active",
      permission: "package_management:view",
      color: "yellow",
    },
    {
      title: "Active Lookups",
      count: metrics?.active_package_lookups_count || 0,
      icon: "🟢",
      description: "Active package lookup definitions",
      permission: "package_management:view",
      color: "emerald",
    },
  ];

  // Filter metrics cards based on permissions
  const metricsCards = allMetricsCards.filter((card) =>
    hasPermission(card.permission)
  );

  // Core Functions Cards
  const coreFunctionCards = [
    {
      title: "Package Lookups",
      description: "Navigate to package lookup definitions",
      icon: "📋",
      count: metrics?.total_package_lookups_count || 0,
      href: "/package-management/package-lookups",
      isPlaceholder: false,
    },
    {
      title: "Packages",
      description: "Navigate to package instances management",
      icon: "📦",
      count: metrics?.total_packages_count || 0,
      href: "/package-management/packages",
      isPlaceholder: false,
    },
  ];

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="package_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view package management.
              </Text>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <div>
              <Heading level="h2">Package Management</Heading>
            </div>
          </div>

          {/* Metrics Section */}
          {metricsCards.length > 0 && (
            <div className="px-6 py-6">
              <div className="mb-4">
                <Heading level="h3" className="text-lg font-semibold text-gray-900">
                  Package Management Overview
                </Heading>
                <Text className="text-sm text-gray-600">
                  Real-time metrics and statistics for package management
                </Text>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {metricsCards.map((card, index) => (
                  <div
                    key={index}
                    className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-2xl font-bold text-gray-900">
                          {loading ? (
                            <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
                          ) : (
                            card.count
                          )}
                        </div>
                        <div className="text-sm font-medium text-gray-600">
                          {card.title}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {card.description}
                        </div>
                      </div>
                      <div className="text-2xl">{card.icon}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Core Functions Section */}
          <div className="px-6 py-6">
            <div className="mb-4">
              <Heading level="h3" className="text-lg font-semibold text-gray-900">
                Core Functions
              </Heading>
              <Text className="text-sm text-gray-600">
                Primary package management operations and data
              </Text>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {coreFunctionCards.map((card, index) => (
                <div
                  key={index}
                  onClick={() => navigate(card.href)}
                  className="group bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg hover:border-blue-300 transition-all cursor-pointer"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="text-2xl">{card.icon}</div>
                        <div>
                          <div className="font-semibold text-gray-900 group-hover:text-blue-600">
                            {card.title}
                          </div>
                          <div className="text-sm text-gray-600">
                            {card.description}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold text-blue-600">
                          {loading ? (
                            <div className="animate-pulse bg-gray-200 h-8 w-12 rounded"></div>
                          ) : (
                            card.count
                          )}
                        </div>
                        <span className="text-xs text-blue-600 font-medium group-hover:text-blue-700">
                          Manage {card.title.toLowerCase()} →
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Container>
        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Package Management",
  icon: Buildings,
});

export default PackageManagementPage;
