import React from "react";
import {
  Text,
  Badge,
  Table,
  Container,
  <PERSON><PERSON>,
  Button,
} from "@camped-ai/ui";
import {
  Calendar,
  Package,
  DollarSign,
  User,
  MapPin,
  FileText,
  ArrowLeft,
  Edit,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useSupplierOrder } from "../../../../hooks/vendor-management/use-supplier-orders";
import { useRbac } from "../../../../hooks/use-rbac";

// Enhanced order interface that includes supplier information from the API
interface EnhancedSupplierOrder {
  id: string;
  order_number: string;
  status: string;
  order_type: string;
  supplier_id: string;
  supplier_name?: string;
  supplier_email?: string;
  supplier_phone?: string;
  supplier_address?: string;
  subtotal: number;
  tax_amount?: number;
  total_amount: number;
  currency_code: string;
  requested_delivery_date?: string;
  actual_delivery_date?: string;
  delivery_address?: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  notes?: string;
  internal_notes?: string;
  items?: any[];
  items_count?: number;
  created_at: string;
  updated_at: string;
}

interface ViewSupplierOrderPageClientProps {
  orderId: string;
}

const ViewSupplierOrderPageClient: React.FC<ViewSupplierOrderPageClientProps> = ({
  orderId,
}) => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const { data, isLoading, error } = useSupplierOrder(orderId);
  const order = data?.order as EnhancedSupplierOrder;

  const getStatusBadgeColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "green";
      case "pending":
        return "orange";
      case "in_progress":
        return "blue";
      case "completed":
        return "green";
      case "cancelled":
        return "red";
      default:
        return "grey";
    }
  };

  const getOrderTypeBadgeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "product":
        return "blue";
      case "service":
        return "green";
      case "mixed":
        return "purple";
      default:
        return "grey";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  if (error) {
    return (
      <Container className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate("/supplier-management/orders")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
        </div>
        <div className="text-center py-8">
          <Text className="text-red-600">
            Failed to load order details: {error.message}
          </Text>
        </div>
      </Container>
    );
  }

  if (isLoading) {
    return (
      <Container className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate("/supplier-management/orders")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
        </div>
        <div className="flex items-center justify-center py-8">
          <Text>Loading order details...</Text>
        </div>
      </Container>
    );
  }

  if (!order) {
    return (
      <Container className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate("/supplier-management/orders")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
        </div>
        <div className="text-center py-8">
          <Text>Order not found</Text>
        </div>
      </Container>
    );
  }

  return (
    <Container className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate("/supplier-management/orders")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
          <div>
            <Heading level="h1">Order {order.order_number}</Heading>
            <Text className="text-ui-fg-muted">
              View comprehensive supplier order details
            </Text>
          </div>
        </div>
        {hasPermission("supplier_orders:edit") && (
          <Button
            variant="secondary"
            size="small"
            onClick={() => navigate(`/supplier-management/orders/${orderId}/edit`)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Order
          </Button>
        )}
      </div>

      {/* Order Overview */}
      <Container className="p-4 bg-ui-bg-subtle rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center gap-2">
            <Package className="h-4 w-4 text-ui-fg-muted" />
            <div>
              <Text className="text-sm text-ui-fg-muted">Order Number</Text>
              <Text className="font-medium">{order.order_number}</Text>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div>
              <Text className="text-sm text-ui-fg-muted">Status</Text>
              <Badge color={getStatusBadgeColor(order.status)}>
                {order.status}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div>
              <Text className="text-sm text-ui-fg-muted">Type</Text>
              <Badge color={getOrderTypeBadgeColor(order.order_type)}>
                {order.order_type}
              </Badge>
            </div>
          </div>
        </div>
      </Container>

      {/* Supplier Information */}
      <div>
        <Heading level="h3" className="mb-3">
          Supplier Information
        </Heading>
        <Container className="p-4 space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Text className="text-sm text-ui-fg-muted">Supplier Name</Text>
              <Text className="font-medium">{order.supplier_name}</Text>
            </div>
            {order.supplier_email && (
              <div>
                <Text className="text-sm text-ui-fg-muted">Email</Text>
                <Text>{order.supplier_email}</Text>
              </div>
            )}
            {order.supplier_phone && (
              <div>
                <Text className="text-sm text-ui-fg-muted">Phone</Text>
                <Text>{order.supplier_phone}</Text>
              </div>
            )}
            {order.supplier_address && (
              <div>
                <Text className="text-sm text-ui-fg-muted">Address</Text>
                <Text>{order.supplier_address}</Text>
              </div>
            )}
          </div>
        </Container>
      </div>

      {/* Financial Information */}
      <div>
        <Heading level="h3" className="mb-3">
          Financial Details
        </Heading>
        <Container className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-ui-fg-muted" />
              <div>
                <Text className="text-sm text-ui-fg-muted">Subtotal</Text>
                <Text className="font-medium">
                  {formatCurrency(order.subtotal, order.currency_code)}
                </Text>
              </div>
            </div>
            <div>
              <Text className="text-sm text-ui-fg-muted">Tax Amount</Text>
              <Text className="font-medium">
                {formatCurrency(order.tax_amount || 0, order.currency_code)}
              </Text>
            </div>
            <div>
              <Text className="text-sm text-ui-fg-muted">Total Amount</Text>
              <Text className="font-medium text-lg">
                {formatCurrency(order.total_amount, order.currency_code)}
              </Text>
            </div>
          </div>
        </Container>
      </div>

      {/* Order Items - New table format showing customer and delivery info per item */}
      {order.items && order.items.length > 0 && (
        <div>
          <Heading level="h3" className="mb-3">
            Order Items
          </Heading>
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell>Item</Table.HeaderCell>
                <Table.HeaderCell>Customer</Table.HeaderCell>
                <Table.HeaderCell>Delivery Info</Table.HeaderCell>
                <Table.HeaderCell>Type</Table.HeaderCell>
                <Table.HeaderCell>Quantity</Table.HeaderCell>
                <Table.HeaderCell>Unit Price</Table.HeaderCell>
                <Table.HeaderCell>Total</Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {order.items.map((item: any) => (
                <Table.Row key={item.id}>
                  <Table.Cell>
                    <div>
                      <Text className="font-medium">{item.item_name}</Text>
                      {item.item_description && (
                        <Text className="text-sm text-ui-fg-muted">
                          {item.item_description}
                        </Text>
                      )}
                      {item.product_sku && (
                        <Text className="text-xs text-ui-fg-muted">
                          SKU: {item.product_sku}
                        </Text>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      {item.customer_name && (
                        <Text className="font-medium">{item.customer_name}</Text>
                      )}
                      {item.customer_email && (
                        <Text className="text-sm text-ui-fg-muted">
                          {item.customer_email}
                        </Text>
                      )}
                      {item.customer_phone && (
                        <Text className="text-xs text-ui-fg-muted">
                          {item.customer_phone}
                        </Text>
                      )}
                      {!item.customer_name && !item.customer_email && (
                        <Text className="text-sm text-ui-fg-muted">
                          No customer info
                        </Text>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      {item.service_date && (
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 text-ui-fg-muted" />
                          <Text className="text-sm">
                            {formatDate(item.service_date)}
                          </Text>
                        </div>
                      )}
                      {item.delivery_address && (
                        <div className="flex items-start gap-1 mt-1">
                          <MapPin className="h-3 w-3 text-ui-fg-muted mt-0.5" />
                          <Text className="text-xs text-ui-fg-muted">
                            {item.delivery_address}
                          </Text>
                        </div>
                      )}
                      {item.service_duration_minutes && (
                        <Text className="text-xs text-ui-fg-muted">
                          Duration: {item.service_duration_minutes} min
                        </Text>
                      )}
                      {!item.service_date && !item.delivery_address && (
                        <Text className="text-sm text-ui-fg-muted">
                          No delivery info
                        </Text>
                      )}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge color={item.item_type === "product" ? "blue" : "green"}>
                      {item.item_type}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>{item.quantity}</Table.Cell>
                  <Table.Cell>
                    {formatCurrency(item.unit_price, order.currency_code)}
                  </Table.Cell>
                  <Table.Cell>
                    {formatCurrency(item.total_price, order.currency_code)}
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>
      )}

      {/* Notes */}
      {(order.notes || order.internal_notes) && (
        <div>
          <Heading level="h3" className="mb-3">
            Notes
          </Heading>
          <Container className="p-4 space-y-4">
            {order.notes && (
              <div className="flex items-start gap-2">
                <FileText className="h-4 w-4 text-ui-fg-muted mt-1" />
                <div>
                  <Text className="text-sm text-ui-fg-muted">Order Notes</Text>
                  <Text className="whitespace-pre-wrap">{order.notes}</Text>
                </div>
              </div>
            )}
            {order.internal_notes && (
              <div className="flex items-start gap-2">
                <FileText className="h-4 w-4 text-ui-fg-muted mt-1" />
                <div>
                  <Text className="text-sm text-ui-fg-muted">Internal Notes</Text>
                  <Text className="whitespace-pre-wrap">{order.internal_notes}</Text>
                </div>
              </div>
            )}
          </Container>
        </div>
      )}

      {/* Order Timeline */}
      <div>
        <Heading level="h3" className="mb-3">
          Order Timeline
        </Heading>
        <Container className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-ui-fg-muted" />
              <div>
                <Text className="text-sm text-ui-fg-muted">Created</Text>
                <Text>{formatDate(order.created_at)}</Text>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-ui-fg-muted" />
              <div>
                <Text className="text-sm text-ui-fg-muted">Last Updated</Text>
                <Text>{formatDate(order.updated_at)}</Text>
              </div>
            </div>
            {order.requested_delivery_date && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-ui-fg-muted" />
                <div>
                  <Text className="text-sm text-ui-fg-muted">Requested Delivery</Text>
                  <Text>{formatDate(order.requested_delivery_date)}</Text>
                </div>
              </div>
            )}
            {order.actual_delivery_date && (
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-ui-fg-muted" />
                <div>
                  <Text className="text-sm text-ui-fg-muted">Actual Delivery</Text>
                  <Text>{formatDate(order.actual_delivery_date)}</Text>
                </div>
              </div>
            )}
          </div>
        </Container>
      </div>
    </Container>
  );
};

export default ViewSupplierOrderPageClient;
