import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  LazyLoadErrorBoundary,
  createSafeLazyComponent,
} from "../../../components/common/lazy-load-error-boundary";
import { FileText } from "lucide-react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { Suspense } from "react";
import AdminPageHelmet from "../../../components/AdminPageHelmet";
import { TableListSkeleton } from "../../../components/skeletons/table-list-skeleton";

// Dynamically import page client for better performance
const SupplierRequestsPageClient = createSafeLazyComponent(
  () => import("./page-client")
);

const SupplierRequestsPage = () => {
  return (
    <>
      <AdminPageHelmet />
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_orders:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view supplier order management
                (including on-request items).
              </Text>
            </div>
          </Container>
        }
      >
        <Suspense fallback={<TableListSkeleton />}>
          <SupplierRequestsPageClient />
        </Suspense>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "On Requests",
  icon: FileText,
});

export default SupplierRequestsPage;
