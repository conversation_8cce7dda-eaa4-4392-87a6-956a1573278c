import React, { useState } from "react";
import { Container, Heading, Badge, Text, Select, toast } from "@camped-ai/ui";
import { TwoColumnPage } from "../../../../../components/layout/pages/two-column-page/two-column-page";
import { SectionRow } from "../../../../../components/common/section/section-row";
import { ConciergeOrderItem } from "../../../../hooks/supplier-management/use-on-request-items";
import { useAdminCurrencies } from "../../../../hooks/use-admin-currencies";
import { formatCurrencyAmount, fromSmallestUnit } from "../../../../utils/currency-utils";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";

import { format } from "date-fns";

// Enhanced customer details interface for production-level type safety
interface EnhancedCustomerDetails {
  id?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  full_name?: string;
}

// Status badge colors for ConciergeOrderItem
const statusColors = {
  under_review: "orange",
  client_confirmed: "green",
  order_placed: "blue",
  cancelled: "red",
  completed: "purple",
} as const;

interface OnRequestItemDetailsLayoutProps {
  item: ConciergeOrderItem;
}

// Hook to fetch concierge order details
const useConciergeOrderDetails = (conciergeOrderId: string) => {
  return useQuery({
    queryKey: ["concierge-order-details", conciergeOrderId],
    queryFn: async () => {
      if (!conciergeOrderId) {
        throw new Error("Concierge order ID is required");
      }

      const response = await fetch(`/admin/concierge-management/orders/${conciergeOrderId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch concierge order: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.concierge_order;
    },
    enabled: !!conciergeOrderId,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
};

const OnRequestItemDetailsLayout: React.FC<OnRequestItemDetailsLayoutProps> = ({
  item,
}) => {
  // Get default currency from store settings
  const { defaultCurrency } = useAdminCurrencies();
  const queryClient = useQueryClient();
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // Fetch concierge order details for booking information
  const { data: conciergeOrderData, isLoading: isConciergeOrderLoading } = useConciergeOrderDetails(item.concierge_order_id);

  // Status update mutation
  const updateStatusMutation = useMutation({
    mutationFn: async (newStatus: string) => {
      const response = await fetch(`/admin/concierge-management/orders/${item.concierge_order_id}/items/${item.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update status");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["on-request-item", item.id] });
      toast.success("Status updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update status: ${error.message}`);
    },
  });

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  // Format currency using the default currency from store settings
  const formatCurrency = (amount: number) => {
    const currency = defaultCurrency || { currency_code: "USD", symbol: "$", decimal_digits: 2 };

    // Convert from smallest unit (cents) to display unit
    const displayAmount = fromSmallestUnit(amount, currency);

    return formatCurrencyAmount(displayAmount, currency, {
      showSymbol: true,
      showCode: false,
      symbolPosition: "before",
    });
  };

  const formatDateOnly = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Customer Details Section
  const CustomerDetailsSection = () => {
    // Show loading state while concierge order data is being fetched
    if (isConciergeOrderLoading) {
      return (
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <Heading level="h2">Customer Details</Heading>
          </div>
          <div className="px-6 py-8 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-3"></div>
            <Text className="text-ui-fg-muted">Loading customer information...</Text>
          </div>
        </Container>
      );
    }

    // Enhanced customer information resolution with multiple fallback sources
    // Priority:
    // 1. Enhanced item customer_details (from list API)
    // 2. Concierge order customer data
    // 3. Order metadata guest data
    // 4. Basic item customer field
    // 5. Fallback values

    let customerName = 'N/A';
    let customerEmail = 'N/A';
    let customerPhone = 'N/A';

    // First, try enhanced customer details from the list API (most comprehensive)
    if (item.customer_details) {
      customerName = item.customer_details.full_name ||
                    `${item.customer_details.first_name || ''} ${item.customer_details.last_name || ''}`.trim() ||
                    item.customer_details.email || 'N/A';
      customerEmail = item.customer_details.email || 'N/A';
      customerPhone = item.customer_details.phone || 'N/A';
    } else {
      // Fallback to concierge order data if available
      const customerFirstName = conciergeOrderData?.customer_first_name || '';
      const customerLastName = conciergeOrderData?.customer_last_name || '';
      customerName = customerFirstName && customerLastName
        ? `${customerFirstName} ${customerLastName}`.trim()
        : conciergeOrderData?.order?.metadata?.guest_name || item.customer || 'N/A';

      customerEmail = conciergeOrderData?.customer_email ||
                     conciergeOrderData?.order?.metadata?.guest_email ||
                     item.order?.email || 'N/A';

      customerPhone = conciergeOrderData?.customer_phone ||
                     conciergeOrderData?.order?.metadata?.guest_phone ||
                     item.metadata?.customer_phone || 'N/A';
    }

    // Extract guest breakdown from metadata or calculate from quantity
    const guestBreakdown = item.metadata?.guest_breakdown ||
                          (item.quantity > 1 ? `${item.quantity} Guests` : '1 Guest');

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Customer Details</Heading>
        </div>

        <SectionRow
          title="Customer Name"
          value={customerName}
        />
        <SectionRow
          title="Email"
          value={customerEmail}
        />
        <SectionRow
          title="Phone Number"
          value={customerPhone}
        />
        <SectionRow
          title="Guest Breakdown"
          value={guestBreakdown}
        />
      </Container>
    );
  };

  // Booking Details Section - copied from concierge management
  const BookingDetailsSection = () => {
    if (isConciergeOrderLoading) {
      return (
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <Heading level="h2">Booking Details</Heading>
          </div>
          <div className="px-6 py-8 text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-3"></div>
            <Text className="text-ui-fg-muted">Loading booking information...</Text>
          </div>
        </Container>
      );
    }

    if (!conciergeOrderData) {
      return (
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <Heading level="h2">Booking Details</Heading>
          </div>
          <div className="px-6 py-4">
            <Text className="text-ui-fg-muted">No booking details available</Text>
          </div>
        </Container>
      );
    }

    // Format date helper
    const formatDate = (dateString?: string) => {
      if (!dateString) return "Not specified";
      try {
        return format(new Date(dateString), "dd MMM yyyy");
      } catch (error) {
        return "Invalid date";
      }
    };

    // Format currency helper
    const formatCurrencyLocal = (amount: number, currencyCode: string = 'GBP') => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode,
      }).format(amount / 100); // Assuming amount is in cents
    };

    // Get hotel information with enhanced fallback sources
    const getHotelInfo = () => {
      return {
        name: item.hotel || // Enhanced item data from list API (most reliable)
              conciergeOrderData.hotel_name ||
              conciergeOrderData.order?.metadata?.hotel_name ||
              conciergeOrderData.order_line_items?.[0]?.metadata?.hotel_name ||
              conciergeOrderData.hotel_id ||
              "Hotel information not available",
        id: conciergeOrderData.hotel_id || conciergeOrderData.order?.metadata?.hotel_id
      };
    };

    const hotelInfo = getHotelInfo();
    const currency = conciergeOrderData.order_currency_code || conciergeOrderData.order?.currency_code || 'GBP';

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Booking Details</Heading>
        </div>

        {/* Hotel Information */}
        <SectionRow
          title="Hotel"
          value={hotelInfo.name}
        />

        {/* Check-in Date */}
        <SectionRow
          title="Check-in Date"
          value={formatDate(conciergeOrderData.check_in_date)}
        />

        {/* Check-out Date */}
        <SectionRow
          title="Check-out Date"
          value={formatDate(conciergeOrderData.check_out_date)}
        />

        {/* Total Booking Amount */}
        <SectionRow
          title="Total Booking Amount"
          value={formatCurrencyLocal(conciergeOrderData.order_total || 0, currency)}
        />
      </Container>
    );
  };

  // Item Information Section
  const ItemInformationSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Item Information</Heading>
      </div>

      <SectionRow
        title="Item ID"
        value={item.id}
      />
      <SectionRow
        title="Title"
        value={item.title || "N/A"}
      />
      <SectionRow
        title="Status"
        value={
          <Badge color={getStatusBadgeVariant(item.status)} size="small">
            {item.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
        }
      />
      <SectionRow
        title="Start Date"
        value={item.start_date ? formatDateOnly(item.start_date) : "N/A"}
      />
      <SectionRow
        title="End Date"
        value={item.end_date ? formatDateOnly(item.end_date) : "N/A"}
      />
    </Container>
  );

  // Financial Information Section
  const FinancialInformationSection = () => {
    // Use order_line_item data when available, fall back to item data
    const unitPrice = item.order_line_item?.unit_price ?? item.unit_price;
    const quantity = item.order_item?.quantity ?? item.quantity;
    const currency = item.order?.currency_code ?? defaultCurrency?.currency_code ?? "GBP";
    const totalPrice = (unitPrice || 0) * (quantity || 0);

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Selling Price Information</Heading>
        </div>

        <SectionRow
          title="Unit Price"
          value={formatCurrency(unitPrice || 0)}
        />
        <SectionRow
          title="Quantity"
          value={quantity?.toString() || "0"}
        />
        <SectionRow
          title="Total Selling Price"
          value={
            <Text className="font-semibold text-lg">
              {formatCurrency(totalPrice)}
            </Text>
          }
        />
        <SectionRow
          title="Currency"
          value={currency}
        />
      </Container>
    );
  };

  // Status Information Section with Dropdown
  const StatusInformationSection = () => {
    // Remove "order_placed" from available options as it should only be set automatically
    const statusOptions = [
      { value: "under_review", label: "Under Review" },
      { value: "client_confirmed", label: "Client Confirmed" },
      { value: "cancelled", label: "Cancelled" },
      { value: "completed", label: "Completed" },
    ];

    const handleStatusChange = (newStatus: string) => {
      setIsUpdatingStatus(true);
      updateStatusMutation.mutate(newStatus, {
        onSettled: () => setIsUpdatingStatus(false),
      });
    };

    // Check if the current status is "order_placed" to disable editing
    const isOrderPlaced = item.status === "order_placed";
    const isStatusEditable = !isOrderPlaced;

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Status Information</Heading>
        </div>

        <SectionRow
          title="Current Status"
          value={
            isStatusEditable ? (
              <Select
                value={item.status}
                onValueChange={handleStatusChange}
                disabled={isUpdatingStatus || updateStatusMutation.isPending}
              >
                <Select.Trigger className="w-full">
                  <Select.Value>
                    {item.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Select.Value>
                </Select.Trigger>
                <Select.Content>
                  {statusOptions.map((option) => (
                    <Select.Item key={option.value} value={option.value}>
                      {option.label}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select>
            ) : (
              <div className="flex items-center gap-2">
                <Badge color="blue" size="small">
                  {item.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
                <Text size="small" className="text-ui-fg-muted">
                  (Status cannot be changed once order is placed)
                </Text>
              </div>
            )
          }
        />
        <SectionRow
          title="Is Active"
          value={
            <Badge color={item.is_active ? "green" : "red"} size="small">
              {item.is_active ? "Active" : "Inactive"}
            </Badge>
          }
        />
      </Container>
    );
  };

  return (
    <>

      <TwoColumnPage
        widgets={{
          before: [],
          after: [],
          sideBefore: [],
          sideAfter: [],
        }}
        data={item}
        showJSON={false}
        showMetadata={false}
        hasOutlet={false}
      >
        <TwoColumnPage.Main>
          <CustomerDetailsSection />
          <BookingDetailsSection />
          <ItemInformationSection />
          <FinancialInformationSection />
        </TwoColumnPage.Main>

        <TwoColumnPage.Sidebar>
          <StatusInformationSection />
        </TwoColumnPage.Sidebar>
      </TwoColumnPage>
    </>
  );
};

export default OnRequestItemDetailsLayout;
