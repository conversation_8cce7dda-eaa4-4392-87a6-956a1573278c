import React from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  FocusModal,
  Button,
  Input,
  Label,
  Select,
  Text,
} from "@camped-ai/ui";
import { useCreateExchangeRate } from "../../../../../hooks/supplier-management/use-exchange-rates";
import DatePicker from "../../../../components/shared/date-picker";

// Validation schema
const createExchangeRateSchema = z.object({
  valid_from: z.string().min(1, "Valid from date is required"),
  valid_to: z.string().min(1, "Valid to date is required"),
  base_currency: z.string().min(3, "Base currency is required").max(3, "Currency code must be 3 characters"),
  selling_currency: z.string().min(3, "Selling currency is required").max(3, "Currency code must be 3 characters"),
  exchange_rate: z.number().positive("Exchange rate must be positive").min(0.0001, "Exchange rate must be at least 0.0001"),
}).refine((data) => data.base_currency !== data.selling_currency, {
  message: "Base currency and selling currency must be different",
  path: ["selling_currency"],
}).refine((data) => {
  // Validate that valid_from is before valid_to
  const validFrom = new Date(data.valid_from);
  const validTo = new Date(data.valid_to);
  return validFrom < validTo;
}, {
  message: "Valid from date must be before valid to date",
  path: ["valid_to"],
});

type CreateExchangeRateFormData = z.infer<typeof createExchangeRateSchema>;

// Common currency options
const CURRENCY_OPTIONS = [
  { value: "USD", label: "USD - US Dollar" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "CHF", label: "CHF - Swiss Franc" },
  { value: "GBP", label: "GBP - British Pound" },
  { value: "JPY", label: "JPY - Japanese Yen" },
  { value: "CAD", label: "CAD - Canadian Dollar" },
  { value: "AUD", label: "AUD - Australian Dollar" },
  { value: "CNY", label: "CNY - Chinese Yuan" },
  { value: "INR", label: "INR - Indian Rupee" },
  { value: "SEK", label: "SEK - Swedish Krona" },
  { value: "NOK", label: "NOK - Norwegian Krone" },
  { value: "DKK", label: "DKK - Danish Krone" },
];

interface CreateExchangeRateModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CreateExchangeRateModal: React.FC<CreateExchangeRateModalProps> = ({
  open,
  onOpenChange,
}) => {
  const createExchangeRate = useCreateExchangeRate();

  const form = useForm<CreateExchangeRateFormData>({
    resolver: zodResolver(createExchangeRateSchema),
    defaultValues: {
      valid_from: new Date().toISOString().split('T')[0], // Today's date
      valid_to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from today
      base_currency: "USD",
      selling_currency: "EUR",
      exchange_rate: 1,
    },
  });

  const handleSubmit = async (data: CreateExchangeRateFormData) => {
    try {
      await createExchangeRate.mutateAsync({
        valid_from: data.valid_from,
        valid_to: data.valid_to,
        base_currency: data.base_currency.toUpperCase(),
        selling_currency: data.selling_currency.toUpperCase(),
        exchange_rate: data.exchange_rate,
      });
      
      // Reset form and close modal
      form.reset();
      onOpenChange(false);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  const handleCancel = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <FocusModal open={open} onOpenChange={onOpenChange}>
      <FocusModal.Content>
        <FocusModal.Header>
          <FocusModal.Title className="w-full align-end">Create Exchange Rate</FocusModal.Title>
        </FocusModal.Header>

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6 p-6">
          <FocusModal.Body className="space-y-6">
            {/* Date Range Fields */}
            <div className="grid grid-cols-2 gap-4">
              {/* Valid From Date */}
              <div className="space-y-2">
                <Label htmlFor="valid_from">Valid From *</Label>
                <Controller
                  name="valid_from"
                  control={form.control}
                  render={({ field }) => (
                    <DatePicker
                      date={field.value ? new Date(field.value) : new Date()}
                      onDateChange={(date) => field.onChange(date?.toISOString().split('T')[0])}
                      placeholder="Select valid from date"
                    />
                  )}
                />
                {form.formState.errors.valid_from && (
                  <Text className="text-ui-fg-error text-sm">
                    {form.formState.errors.valid_from.message}
                  </Text>
                )}
              </div>

              {/* Valid To Date */}
              <div className="space-y-2">
                <Label htmlFor="valid_to">Valid To *</Label>
                <Controller
                  name="valid_to"
                  control={form.control}
                  render={({ field }) => (
                    <DatePicker
                      date={field.value ? new Date(field.value) : new Date()}
                      onDateChange={(date) => field.onChange(date?.toISOString().split('T')[0])}
                      placeholder="Select valid to date"
                    />
                  )}
                />
                {form.formState.errors.valid_to && (
                  <Text className="text-ui-fg-error text-sm">
                    {form.formState.errors.valid_to.message}
                  </Text>
                )}
              </div>
            </div>

            {/* Currency Pair */}
            <div className="grid grid-cols-2 gap-4">
              {/* Base Currency */}
              <div className="space-y-2">
                <Label htmlFor="base_currency">Base Currency *</Label>
                <Controller
                  name="base_currency"
                  control={form.control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select base currency" />
                      </Select.Trigger>
                      <Select.Content>
                        {CURRENCY_OPTIONS.map((option) => (
                          <Select.Item key={option.value} value={option.value}>
                            {option.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
                {form.formState.errors.base_currency && (
                  <Text className="text-ui-fg-error text-sm">
                    {form.formState.errors.base_currency.message}
                  </Text>
                )}
              </div>

              {/* Selling Currency */}
              <div className="space-y-2">
                <Label htmlFor="selling_currency">Selling Currency *</Label>
                <Controller
                  name="selling_currency"
                  control={form.control}
                  render={({ field }) => (
                    <Select value={field.value} onValueChange={field.onChange}>
                      <Select.Trigger>
                        <Select.Value placeholder="Select selling currency" />
                      </Select.Trigger>
                      <Select.Content>
                        {CURRENCY_OPTIONS.map((option) => (
                          <Select.Item key={option.value} value={option.value}>
                            {option.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                />
                {form.formState.errors.selling_currency && (
                  <Text className="text-ui-fg-error text-sm">
                    {form.formState.errors.selling_currency.message}
                  </Text>
                )}
              </div>
            </div>

            {/* Exchange Rate */}
            <div className="space-y-2">
              <Label htmlFor="exchange_rate">Exchange Rate *</Label>
              <Controller
                name="exchange_rate"
                control={form.control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    step="0.0001"
                    min="0.0001"
                    max="1000000"
                    placeholder="Enter exchange rate (e.g., 1.0850)"
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  />
                )}
              />
              {form.formState.errors.exchange_rate && (
                <Text className="text-ui-fg-error text-sm">
                  {form.formState.errors.exchange_rate.message}
                </Text>
              )}
              <Text className="text-ui-fg-subtle text-sm">
                How many units of the selling currency equal 1 unit of the base currency
              </Text>
            </div>

            {/* Preview */}
            {form.watch("base_currency") && form.watch("selling_currency") && form.watch("exchange_rate") > 0 && (
              <div className="p-4 bg-ui-bg-subtle rounded-lg">
                <Text className="text-sm font-medium mb-1">Preview:</Text>
                <Text className="text-sm text-ui-fg-subtle">
                  1 {form.watch("base_currency")} = {Number(form.watch("exchange_rate")).toFixed(4)} {form.watch("selling_currency")}
                </Text>
              </div>
            )}
          </FocusModal.Body>

          <FocusModal.Footer>
            <div className="flex items-center justify-end gap-x-2">
              <Button
                type="button"
                variant="secondary"
                onClick={handleCancel}
                disabled={createExchangeRate.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createExchangeRate.isPending}
                isLoading={createExchangeRate.isPending}
              >
                Create Exchange Rate
              </Button>
            </div>
          </FocusModal.Footer>
        </form>
      </FocusModal.Content>
    </FocusModal>
  );
};
