import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Trash,
  Copy,
} from "lucide-react";
import {
  Container,
  <PERSON>ing,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  Prompt,
} from "@camped-ai/ui";
import { useMemo, useState, useCallback } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useReactTable, getCoreRowModel, createColumnHelper, ColumnDef } from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";
import { useDeleteSupplierOffering, useDuplicateSupplierOffering } from "../../../hooks/supplier-products-services/use-supplier-offerings";
import { CategoryIcon } from "../../../utils/category-icon-utils";
import { BulkStatusChangeModal } from "../../../components/supplier-management/bulk-status-change-modal";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { formatCurrencyAmount } from "../../../utils/currency-utils";

// Types
interface SupplierOffering {
  id: string;
  supplier_id: string;
  product_service_id: string;
  commission?: string | number;
  gross_price?: string | number;
  selling_price?: string | number;
  selling_price_selling_currency?: string | number;
  selling_currency?: string;
  net_price?: string | number;
  currency?: string;
  active_from?: string;
  active_to?: string;
  status: "active" | "inactive";
  created_at: string;
  updated_at: string;

  // Calculated fields from API
  calculated_selling_price_selling_currency?: number;
  supplier?: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  product_service?: {
    id: string;
    name: string;
    type: string;
    category?: {
      id: string;
      name: string;
    };
  };
  category?: {
    id: string;
    name: string;
  };
}

interface SupplierOfferingsPageClientProps {
  offerings: SupplierOffering[];
  suppliers: any[];
  categories: any[];
  productsServices: any[];
  hotels: any[];
  destinations: any[];
  isLoading: boolean;
  totalCount: number;
  pageSize: number;
}

const columnHelper = createColumnHelper<SupplierOffering>();

const SupplierOfferingsPageClient = ({
  offerings,
  suppliers,
  categories,
  productsServices,
  hotels,
  destinations,
  isLoading,
  totalCount,
  pageSize,
}: SupplierOfferingsPageClientProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useRbac();


  const deleteSupplierOfferingMutation = useDeleteSupplierOffering();
  const duplicateSupplierOfferingMutation = useDuplicateSupplierOffering();

  // Raw display helper: echo values verbatim, no currency/locale formatting
  const raw = (v: any) => (v === null || v === undefined ? "N/A" : String(v));

  // Date formatting helper: format dates to show only date part (DD.MM.YYYY)
  const formatDateOnly = (dateString: string | null | undefined) => {
    if (!dateString) return null;
    try {
      const date = new Date(dateString);
      // Use British date format (DD.MM.YYYY) as per user preferences
      return date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric"
      });
    } catch {
      return dateString; // Fallback to original string if parsing fails
    }
  };

  // Row selection state for multi-select functionality
  const [rowSelection, setRowSelection] = useState({});

  // Modal state for bulk status change
  const [isBulkStatusModalOpen, setIsBulkStatusModalOpen] = useState(false);
  const [currentSelection, setCurrentSelection] = useState<Record<string, boolean>>({});

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [offeringToDelete, setOfferingToDelete] = useState<SupplierOffering | null>(null);

  // Handle export of selected rows
  const handleExportSelected = async (selection: Record<string, boolean>) => {
    const selectedIds = Object.keys(selection);
    if (selectedIds.length === 0) {
      toast.error("No rows selected for export");
      return;
    }

    try {
      // Build export URL with selected IDs
      const params = new URLSearchParams();
      params.append("format", "excel");
      selectedIds.forEach(id => params.append("ids", id));

      // Open export URL in new tab
      const exportUrl = `/admin/supplier-management/supplier-offerings/export?${params.toString()}`;
      window.open(exportUrl, '_blank');

      toast.success(`Exporting ${selectedIds.length} selected supplier offerings`);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export selected supplier offerings");
    }
  };

  // Handle bulk status change - open modal
  const handleBulkStatusChange = useCallback(async (selection: Record<string, boolean>) => {
    const selectedIds = Object.keys(selection);

    if (selectedIds.length === 0) {
      toast.error("No rows selected for status change");
      return;
    }

    // Store the current selection for the modal
    setCurrentSelection(selection);
    // Open the modal
    setIsBulkStatusModalOpen(true);
  }, []);

  // Handle the actual status change from modal
  const handleStatusChangeSubmit = async (ids: string[], newStatus: string) => {
    try {
      // Call bulk update API
      const response = await fetch("/admin/supplier-management/supplier-offerings/bulk-status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ids,
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update status");
      }

      await response.json();

      // Refresh the page data
      window.location.reload();
    } catch (error) {
      console.error("Bulk status change error:", error);
      throw error; // Re-throw to let modal handle the error display
    }
  };

  // Define bulk commands for selected rows - conditional based on selection count
  const commands = useMemo(() => {
    const selectedCount = Object.keys(rowSelection || {}).length;
    const commandList = [];

    // Always show Export Selected when items are selected
    if (selectedCount > 0) {
      commandList.push({
        label: "Export Selected",
        shortcut: "e",
        action: handleExportSelected,
      });
    }

    // Show Change Status for any selection (1+ items)
    if (selectedCount > 0) {
      commandList.push({
        label: "Change Status",
        shortcut: "s",
        action: handleBulkStatusChange,
      });
    }

    return commandList;
  }, [rowSelection, handleExportSelected, handleBulkStatusChange]);


  // Helper function to resolve product/service name
  const resolveProductServiceName = (productService: any, hotels: any[], destinations: any[]) => {
    if (!productService) return "Unknown Product/Service";

    let name = "";

    if (productService.type === "hotel" && productService.hotel_id) {
      const hotel = hotels.find(h => h.id === productService.hotel_id);
      name = hotel ? hotel.name : productService.name || "Unknown Hotel";
    } else if (productService.type === "destination" && productService.destination_id) {
      const destination = destinations.find(d => d.id === productService.destination_id);
      name = destination ? destination.name : productService.name || "Unknown Destination";
    } else {
      name = productService.name || "Unknown Product/Service";
    }

    // Clean up the name by removing excessive details and truncating if too long
    if (name && name.length > 80) {
      // Try to find a natural break point (like " - " or " (")
      const breakPoints = [" - ", " (", " |", " –"];
      let cleanName = name;

      for (const breakPoint of breakPoints) {
        const firstPart = name.split(breakPoint)[0];
        if (firstPart && firstPart.length >= 20 && firstPart.length <= 60) {
          cleanName = firstPart;
          break;
        }
      }

      // If still too long, truncate and add ellipsis
      if (cleanName.length > 80) {
        cleanName = cleanName.substring(0, 77) + "...";
      }

      return cleanName;
    }

    return name;
  };

  // Handle copy offering ID
  const handleCopyOfferingId = (id: string) => {
    navigator.clipboard.writeText(id);
    toast.success("Offering ID copied to clipboard");
  };

  // Handle delete offering click - open modal
  const handleDeleteOfferingClick = (offering: SupplierOffering) => {
    setOfferingToDelete(offering);
    setIsDeleteModalOpen(true);
  };

  // Handle delete offering - actual deletion
  const handleDeleteOffering = async () => {
    if (!offeringToDelete) return;

    // Dismiss any existing toasts and show loading toast
    toast.dismiss();
    const loadingToast = toast.loading("Deleting supplier offering...");

    try {
      await deleteSupplierOfferingMutation.mutateAsync(offeringToDelete.id);
      toast.dismiss(loadingToast);
      toast.success("Supplier offering deleted successfully");
      setIsDeleteModalOpen(false);
      setOfferingToDelete(null);
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error("Failed to delete supplier offering");
      setIsDeleteModalOpen(false);
      setOfferingToDelete(null);
    }
  };

  // Handle cancel delete
  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setOfferingToDelete(null);
  };

  // Handle duplicate offering
  const handleDuplicateOffering = async (id: string) => {
    try {
      await duplicateSupplierOfferingMutation.mutateAsync(id);
      toast.success("Supplier offering duplicated successfully");
    } catch (error) {
      toast.error("Failed to duplicate supplier offering");
    }
  };



  // Get selected category from URL to determine if we should show custom field columns
  const searchParams = new URLSearchParams(location.search);
  const selectedCategoryId = searchParams.get("category_id");
  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  // Helper function to render custom field value with resolved names
  const renderCustomFieldValue = (customFields: any, fieldKey: string) => {
    if (!customFields) return "N/A";

    // Check for resolved names first (with _names suffix)
    const resolvedNamesKey = `${fieldKey}_names`;
    if (customFields[resolvedNamesKey]) {
      const names = customFields[resolvedNamesKey];
      if (Array.isArray(names)) {
        return names.length > 0 ? names.join(", ") : "N/A";
      }
      return names || "N/A";
    }

    // Fallback to original field value
    const value = customFields[fieldKey];
    if (!value) return "N/A";

    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(", ") : "N/A";
    }

    return value.toString();
  };

  // Generate dynamic custom field columns based on selected category
  const generateCustomFieldColumns = () => {
    if (!selectedCategory?.dynamic_field_schema) return [];

    // Filter dynamic fields to only include fields marked for supplier offering usage
    const filteredFields = selectedCategory.dynamic_field_schema.filter((field: any) => {
      const isSupplierContext = !field.field_context || field.field_context === "supplier";
      const isUsedInSupplierOffering = field.used_in_supplier_offering === true;
      return isSupplierContext && isUsedInSupplierOffering;
    });

    return filteredFields.map((field: any) =>
      columnHelper.display({
        id: `custom_field_${field.key}`,
        header: field.label,
        cell: ({ row }) => {
          const offering = row.original;
          // Check both offering custom fields and product service custom fields
          const offeringValue = renderCustomFieldValue((offering as any).custom_fields, field.key);
          const productServiceValue = renderCustomFieldValue((offering.product_service as any)?.custom_fields, field.key);

          // Prioritize offering custom fields, fallback to product service custom fields
          const displayValue = offeringValue !== "N/A" ? offeringValue : productServiceValue;

          return (
            <div className="max-w-[200px]">
              {field.type === "hotels" || field.type === "destinations" || field.type === "addons" ? (
                <div className="flex flex-wrap gap-1">
                  {displayValue.split(", ").map((name: string, index: number) => (
                    <Badge key={index} size="small" color="grey">
                      {name}
                    </Badge>
                  ))}
                </div>
              ) : (
                <Text className="txt-compact-small truncate" title={displayValue}>
                  {displayValue}
                </Text>
              )}
            </div>
          );
        },
      })
    );
  };

  // Define columns
  const columns = useMemo<ColumnDef<SupplierOffering, any>[]>(() => {
    const baseColumns = [
    // Select column for multi-select functionality
    columnHelper.display({
      id: "select",
      header: ({ table }) => (
        <input
          type="checkbox"
          checked={table.getIsAllPageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
          className="rounded border-ui-border-base"
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
          className="rounded border-ui-border-base"
        />
      ),
    }),
    columnHelper.display({
      id: "supplier",
      header: "Supplier Name",
      cell: ({ row }) => {
        const offering = row.original;
        return (
          <div className="flex items-center gap-x-3 min-w-[280px] w-full pr-4" style={{ minWidth: '280px', width: '100%' }}>
            <div className="flex h-8 w-8 items-center justify-center rounded bg-ui-bg-subtle flex-shrink-0">
              <CategoryIcon category={offering.product_service?.category} className="h-4 w-4 text-ui-fg-subtle" />
            </div>
            <div className="flex-1 min-w-0 overflow-visible">
              <Text
                className="txt-compact-medium-plus break-words leading-relaxed whitespace-normal text-wrap overflow-visible"
                weight="plus"
                title={offering.supplier?.name || "Unknown Supplier"}
                style={{ wordBreak: 'break-word', whiteSpace: 'normal', overflow: 'visible' }}
              >
                {offering.supplier?.name || "Unknown Supplier"}
              </Text>
              <div className="flex items-center gap-x-1">
                <Text
                  size="small"
                  className="text-ui-fg-subtle break-words whitespace-normal overflow-visible"
                  style={{ wordBreak: 'break-word', whiteSpace: 'normal', overflow: 'visible' }}
                >
                  {offering.supplier?.type}
                </Text>
              </div>
            </div>
          </div>
        );
      },
      size: 300, // Further increased column width
      minSize: 280,
      maxSize: 450,
    }),
    columnHelper.display({
      id: "product_service",
      header: "Product/Service Name",
      cell: ({ row }) => {
        const offering = row.original;
        const productServiceName = resolveProductServiceName(offering.product_service, hotels, destinations);
        return (
          <div className="px-3 py-2 min-w-0">
            <Text
              className="txt-compact-medium-plus font-medium break-words leading-relaxed whitespace-normal"
              title={productServiceName}
              style={{
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                display: 'block',
                lineHeight: '1.4'
              }}
            >
              {productServiceName}
            </Text>
          </div>
        );
      },
      size: 350, // Balanced column width
      minSize: 300,
      maxSize: 500,
    }),
    columnHelper.display({
      id: "category",
      header: "Category",
      cell: ({ row }) => {
        const offering = row.original;
        const categoryName = offering.product_service?.category?.name || "Uncategorized";
        return (
          <div className="px-3 py-2 min-w-0">
            <Text
              className="text-sm break-words leading-relaxed whitespace-normal"
              title={categoryName}
              style={{
                wordBreak: 'break-word',
                whiteSpace: 'normal',
                display: 'block',
                lineHeight: '1.4'
              }}
            >
              {categoryName}
            </Text>
          </div>
        );
      },
      size: 200, // Balanced column width
      minSize: 180,
      maxSize: 300,
    }),
    columnHelper.display({
      id: "selling_price",
      header: "Selling Price",
      meta: {
        align: "right",
      },
      cell: ({ row }) => {
        const o = row.original;

        // Preference order for numeric value
        const candidate =
          (o as any).calculated_selling_price_selling_currency ??
          (o as any).selling_price_selling_currency ??
          (o as any).selling_price ??
          (o as any).gross_price ??
          (o as any).net_price;

        // Always display as GBP with en-GB formatting and £ symbol
        const formatted =
          candidate !== null && candidate !== undefined && candidate !== ""
            ? new Intl.NumberFormat("en-GB", {
                style: "currency",
                currency: "GBP",
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(Number(candidate))
            : "N/A";

        return (
          <div className="px-3 py-2 text-right">
            <Text className="txt-compact-medium">{formatted}</Text>
          </div>
        );
      },
      size: 200,
      minSize: 120,
      maxSize: 280,
    }),
    columnHelper.accessor("status", {
      header: "Status",
      cell: ({ getValue }) => {
        const status = getValue();
        return (
          <Badge color={status === "active" ? "green" : "red"} size="small">
            {status === "active" ? "Active" : "Inactive"}
          </Badge>
        );
      },
    }),
    columnHelper.display({
      id: "validity",
      header: "Validity",
      cell: ({ row }) => {
        const o = row.original;
        // Format dates to show only date part (DD.MM.YYYY)
        const fromFormatted = formatDateOnly(o.active_from);
        const toFormatted = formatDateOnly(o.active_to);

        return (
          <div className="flex items-center gap-1">
            <Text size="small">
              {fromFormatted ? fromFormatted : "Not set"}{toFormatted ? ` - ${toFormatted}` : " - Open"}
            </Text>
          </div>
        );
      },
    }),
    columnHelper.display({
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const offering = row.original;
        return (
          <DropdownMenu>
            <DropdownMenu.Trigger asChild>
              <Button
                variant="transparent"
                size="small"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/supplier-management/supplier-offerings/${offering.id}`);
                }}
              >
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenu.Item>
              {hasPermission("supplier_management:edit") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(`/supplier-management/supplier-offerings/${offering.id}/edit`);
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenu.Item>
              )}
              {hasPermission("supplier_management:create") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDuplicateOffering(offering.id);
                  }}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </DropdownMenu.Item>
              )}
              {hasPermission("supplier_management:delete") && (
                <DropdownMenu.Item
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteOfferingClick(offering);
                  }}
                  className="text-ui-fg-error"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenu.Item>
              )}
            </DropdownMenu.Content>
          </DropdownMenu>
        );
      },
    }),
    ];

    // Add custom field columns if a category is selected
    const customFieldColumns = generateCustomFieldColumns();

    return [...baseColumns, ...customFieldColumns];
  }, [offerings, suppliers, categories, productsServices, hotels, destinations, hasPermission, navigate, selectedCategory]);

  // Define filters
  const filters = useMemo<Filter[]>(() => [
    {
      key: "supplier_id",
      label: "Supplier",
      type: "select",
      multiple: false,
      searchable: true,
      options: suppliers.map(supplier => ({
        label: supplier.name,
        value: supplier.id,
      })),
    },
    {
      key: "category_id",
      label: "Category",
      type: "select",
      multiple: false,
      options: categories.map(category => ({
        label: category.name,
        value: category.id,
      })),
    },
    {
      key: "product_service_id",
      label: "Product/Service",
      type: "select",
      multiple: false,
      searchable: true,
      options: productsServices.map(ps => ({
        label: resolveProductServiceName(ps, hotels, destinations),
        value: ps.id,
      })),
    },
    {
      key: "status",
      label: "Status",
      type: "select",
      multiple: false,
      options: [
        { label: "Active", value: "active" },
        { label: "Inactive", value: "inactive" },
      ],
    },
    {
      key: "active_from",
      label: "Active From",
      type: "date",
    },
    {
      key: "active_to",
      label: "Active To",
      type: "date",
    },
  ], [suppliers, categories, productsServices, hotels, destinations]);

  // Define orderBy options
  const orderBy = useMemo(() => [
    { key: "product_service_name" as keyof SupplierOffering, label: "Product/Service Name" },
    { key: "commission_percentage" as keyof SupplierOffering, label: "Commission %" },
    { key: "gross_price" as keyof SupplierOffering, label: "Gross Price" },
    { key: "net_price" as keyof SupplierOffering, label: "Net Price" },
    { key: "status" as keyof SupplierOffering, label: "Status" },
    { key: "validity" as keyof SupplierOffering, label: "Validity" },
    { key: "created_at" as keyof SupplierOffering, label: "Created At" },
    { key: "updated_at" as keyof SupplierOffering, label: "Updated At" },
  ], []);

  // Get current page from URL
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Create table instance
  const table = useReactTable({
    data: offerings,
    columns,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    enableRowSelection: true,
    getRowId: (row) => row.id,
    columnResizeMode: 'onChange',
    enableColumnResizing: true,
    defaultColumn: {
      minSize: 200,
      maxSize: 1000,
    },
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
      rowSelection,
    },
    onRowSelectionChange: setRowSelection,
  });

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">Supplier Offerings</Heading>
        </div>
        <div className="flex items-center gap-x-2">
          <Button
            variant="secondary"
            asChild
          >
            <Link to="/supplier-management/supplier-offerings/export">
              Export
            </Link>
          </Button>
          {hasPermission("supplier_management:bulk_operations") && (
            <Button
              variant="secondary"
              asChild
            >
              <Link to="/supplier-management/supplier-offerings/import">
                Import
              </Link>
            </Button>
          )}
          {hasPermission("supplier_management:create") && (
            <Button asChild>
              <Link to="/supplier-management/supplier-offerings/create">
                Create Offering
              </Link>
            </Button>
          )}
        </div>
      </div>

      {/* DataTable */}
      <div className="overflow-x-auto min-w-0">
        <div className="min-w-[1200px]">
          <DataTable
            table={table}
            columns={columns}
            pageSize={pageSize}
            count={totalCount}
            isLoading={isLoading}
            filters={filters}
            orderBy={orderBy}
            search="autofocus"
            pagination
            navigateTo={(row) => `/supplier-management/supplier-offerings/${row.original.id}`}
            commands={commands}
            queryObject={Object.fromEntries(searchParams)}
            noRecords={{
              title: "No supplier offerings found",
              message: "Get started by creating your first supplier offering",
            }}
          />
        </div>
      </div>
      <Toaster />

      {/* Bulk Status Change Modal */}
      {isBulkStatusModalOpen && (
        <BulkStatusChangeModal
          open={isBulkStatusModalOpen}
          onOpenChange={setIsBulkStatusModalOpen}
          selectedIds={Object.keys(currentSelection)}
          selectedItems={Object.keys(currentSelection).map(id => {
            try {
              const item = offerings.find((so: any) => so.id === id);
              return {
                id,
                name: item ? resolveProductServiceName(item.product_service, hotels, destinations) : `Supplier Offering ${id}`,
                status: item?.status || 'unknown'
              };
            } catch (error) {
              console.error("Error preparing modal item:", error);
              return {
                id,
                name: `Supplier Offering ${id}`,
                status: 'unknown'
              };
            }
          })}
          onStatusChange={handleStatusChangeSubmit}
        />
      )}

      {/* Delete Confirmation Modal */}
      <Prompt open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Supplier Offering</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete this supplier offering? This action cannot be undone.
              {offeringToDelete && (
                <div className="mt-2 text-sm font-medium">
                  {resolveProductServiceName(offeringToDelete.product_service, hotels, destinations)} by {offeringToDelete.supplier?.name}
                </div>
              )}
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleDeleteOffering}>Delete</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default SupplierOfferingsPageClient;
