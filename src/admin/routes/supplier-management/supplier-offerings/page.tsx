import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Package } from "lucide-react";
import { Container, Heading, Text } from "@camped-ai/ui";
import { useSearchParams } from "react-router-dom";
import { useMemo, Suspense } from "react";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { useSupplierOfferings } from "../../../hooks/supplier-products-services/use-supplier-offerings";
import { useCategories } from "../../../hooks/supplier-products-services/use-categories";
import { useSuppliers } from "../../../hooks/vendor-management/use-suppliers";
import { useProductsServices } from "../../../hooks/supplier-products-services/use-products-services";
import { useHotels } from "../../../hooks/supplier-products-services/use-hotels";
import { useDestinations } from "../../../hooks/supplier-products-services/use-destinations";
import {
  LazyLoadErrorBoundary,
  createSafeLazyComponent,
} from "../../../components/common/lazy-load-error-boundary";
import { TableListSkeleton } from "../../../components/skeletons/table-list-skeleton";

// Dynamically import page client for better performance with Router context safety
const SupplierOfferingsPageClient = createSafeLazyComponent(
  () => import("./page-client")
);

const SupplierOfferingsPage = () => {
  const [searchParams] = useSearchParams();

  // Extract filters from URL parameters
  const filters = useMemo(() => {
    const currentPage = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("pageSize") || "25");
    const searchTerm = searchParams.get("search") || "";

    const baseFilters = {
      supplier_id: searchParams.get("supplier_id") || undefined,
      category_id: searchParams.get("category_id") || undefined,
      product_service_id: searchParams.get("product_service_id") || undefined,
      status:
        (searchParams.get("status") as "active" | "inactive") || undefined,
      active_from: searchParams.get("active_from") || undefined,
      active_to: searchParams.get("active_to") || undefined,
      search: searchTerm || undefined,
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
      sort_by:
        (searchParams.get("sort_by") as
          | "updated_at"
          | "product_service_name"
          | "net_price"
          | "validity") || "updated_at",
      sort_order: (searchParams.get("sort_order") as "asc" | "desc") || "desc",
    };

    return baseFilters;
  }, [searchParams]);

  // API calls
  const { data: offeringsData, isLoading: offeringsLoading } =
    useSupplierOfferings(filters);

  const { data: categoriesData, isLoading: categoriesLoading } =
    useCategories();
  const { data: suppliersData, isLoading: suppliersLoading } = useSuppliers();
  const { data: productsServicesData, isLoading: productsServicesLoading } =
    useProductsServices();
  const { data: hotelsData, isLoading: hotelsLoading } = useHotels();
  const { data: destinationsData, isLoading: destinationsLoading } =
    useDestinations();

  // Extract data
  const offerings = offeringsData?.supplier_offerings || [];
  const suppliers = suppliersData?.suppliers || [];
  const categories = categoriesData?.categories || [];
  const productsServices = productsServicesData?.product_services || [];
  const hotels = hotelsData?.hotels || [];
  const destinations = destinationsData?.destinations || [];
  const totalCount = offeringsData?.count || 0;
  const pageSize = parseInt(searchParams.get("pageSize") || "25");

  const isLoading =
    offeringsLoading ||
    categoriesLoading ||
    suppliersLoading ||
    productsServicesLoading ||
    hotelsLoading ||
    destinationsLoading;

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view supplier management.
              </Text>
            </div>
          </Container>
        }
      >
        <LazyLoadErrorBoundary fallbackMessage="Failed to load supplier offerings page.">
          <Suspense fallback={<TableListSkeleton />}>
            <SupplierOfferingsPageClient
              offerings={offerings}
              suppliers={suppliers}s
              categories={categories}
              productsServices={productsServices}
              hotels={hotels}
              destinations={destinations}
              isLoading={isLoading}
              totalCount={totalCount}
              pageSize={pageSize}
            />
          </Suspense>
        </LazyLoadErrorBoundary>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Offerings",
  icon: Package,
});

export default SupplierOfferingsPage;
