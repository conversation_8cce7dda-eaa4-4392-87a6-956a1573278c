import { defineWidgetConfig } from "@camped-ai/admin-sdk";
import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useRbac } from "../hooks/use-rbac";
import {
  HIDDEN_SIDEBAR_ITEMS,
  EXCEPTION_ITEMS,
} from "../config/sidebar-config";

/**
 * Permission-based sidebar hider that hides menu items based on RBAC permissions
 * Combines static hiding (from config) with dynamic permission-based hiding
 */
// Immediately inject CSS to prevent flash of unstyled content
const injectImmediateHidingCSS = () => {
  const styleId = "immediate-sidebar-hiding";

  if (!document.getElementById(styleId)) {
    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      /* Immediately hide all sidebar navigation until permissions are loaded */
      nav, aside, [role="navigation"], [data-testid="sidebar"] {
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
      }

      /* Show sidebar only when explicitly marked as ready */
      nav[data-permissions-ready="true"],
      aside[data-permissions-ready="true"],
      [role="navigation"][data-permissions-ready="true"],
      [data-testid="sidebar"][data-permissions-ready="true"] {
        opacity: 1 !important;
      }
    `;
    document.head.appendChild(style);
  }
};

// Run immediately when module loads
injectImmediateHidingCSS();

const PermissionBasedSidebarHider = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission, hasAnyPermission, isAdmin, currentUser, loading } = useRbac();

  // Inject CSS to hide sidebar initially
  useEffect(() => {
    const styleId = "permission-sidebar-loading-style";

    if (!document.getElementById(styleId)) {
      const style = document.createElement("style");
      style.id = styleId;
      style.textContent = `
        /* Hide sidebar navigation while permissions are loading */
        nav[data-loading="true"],
        aside[data-loading="true"],
        [role="navigation"][data-loading="true"],
        [data-testid="sidebar"][data-loading="true"] {
          opacity: 0 !important;
          pointer-events: none !important;
          transition: opacity 0.3s ease !important;
        }

        /* Show sidebar after permissions are loaded */
        nav[data-loading="false"],
        aside[data-loading="false"],
        [role="navigation"][data-loading="false"],
        [data-testid="sidebar"][data-loading="false"] {
          opacity: 1 !important;
          pointer-events: auto !important;
          transition: opacity 0.3s ease !important;
        }
      `;
      document.head.appendChild(style);
    }

    // Set loading state on sidebar elements
    const sidebarElements = document.querySelectorAll(
      'nav, aside, [role="navigation"], [data-testid="sidebar"]'
    );

    sidebarElements.forEach((sidebar) => {
      (sidebar as HTMLElement).setAttribute("data-loading", loading.toString());
    });

    return () => {
      // Cleanup: remove loading state when component unmounts
      sidebarElements.forEach((sidebar) => {
        (sidebar as HTMLElement).removeAttribute("data-loading");
      });
    };
  }, [loading]);

  useEffect(() => {
    // Hide sidebar immediately while RBAC is loading
    const hideSidebarWhileLoading = () => {
      const sidebarElements = document.querySelectorAll(
        'nav, aside, [role="navigation"], [data-testid="sidebar"]'
      );

      sidebarElements.forEach((sidebar) => {
        if (loading) {
          (sidebar as HTMLElement).style.opacity = "0";
          (sidebar as HTMLElement).style.pointerEvents = "none";
        } else {
          (sidebar as HTMLElement).style.opacity = "1";
          (sidebar as HTMLElement).style.pointerEvents = "auto";
        }
      });
    };

    // Always run this to handle loading state
    hideSidebarWhileLoading();

    // Don't run permission hiding if RBAC is still loading
    if (loading) return;

    // Use a more specific flag that includes the current path to allow re-running on navigation
    const currentPath = location.pathname;
    // Sanitize the path to create a valid dataset property name (replace invalid characters)
    const sanitizedPath = currentPath.replace(/[^a-zA-Z0-9]/g, '_');
    const runKey = `sidebarHidden${sanitizedPath}`;
    let hasRun = document.body.dataset[runKey] === 'true';

    // Handle redirections for unauthorized access
    const handleRedirection = () => {
      const currentPath = location.pathname;

      // Redirect unauthorized users away from admin-only pages
      if (currentPath.includes("/admin/settings") && !isAdmin()) {
        navigate("/");
        return;
      }
    };

    // Run redirection check first
    handleRedirection();

    // Immediate search hiding function
    const hideSearchImmediately = () => {
      const searchElements = document.querySelectorAll(
        'nav *, aside *, [role="navigation"] *, [data-testid="sidebar"] *'
      );

      searchElements.forEach((element) => {
        const text = element.textContent?.trim() || "";
        if (text.toLowerCase() === "search") {
          const listItem = element.closest("li");
          const targetElement = listItem || element.parentElement || element;
          (targetElement as HTMLElement).style.display = "none";
        }
      });
    };

    // Hide search immediately
    hideSearchImmediately();

    const hideSidebarItems = () => {
      if (hasRun) return;
      document.body.dataset[runKey] = 'true';

      // Define parent module permissions that control entire module groups
      const parentModulePermissions = {
        "supplier_management": "supplier_management:view",
        "hotel_management": "hotel_management:view",
        "concierge_management": "concierge_management:view",
        "user_management": "user_management:view",
        "role_management": "role_management:view",
        "analytics": "analytics:view",
        "reports": "reports:view",
        "addon_services": "addon_services:view",
        "addons": "addons:view",
        "rooms": "rooms:view",
        "bookings": ["bookings:view", "bookings:create", "bookings:edit", "bookings:delete"],
        "destinations": "destinations:view",
        "pricing": "pricing:view",
      };

      // Define which URLs/paths belong to which parent modules
      const modulePathMappings: Record<string, string[]> = {
        "supplier_management": [
          "/supplier-management/",
          "/supplier-management/suppliers",
          "/supplier-management/products-services",
          "/supplier-management/supplier-offerings",
          "/supplier-management/orders",
          "/supplier-management/pricing",
          "/supplier-management/contracts",
          "/supplier-management/requests",
          "/supplier-management/availability",
          "/supplier-management/config",
        ],
        "package_management": [
          "/package-management/",
          "/package-management/package-lookups",
          "/package-management/packages",
        ],
        "hotel_management": [
          "/admin/hotel-management/",
          "/hotel-management/",
          "/admin/hotel-management/destinations",
        ],
        "concierge_management": [
          "/concierge-management/",
          "/admin/concierge-management/",
        ],
        "user_management": [
          "/app/settings/user-management",
          "/settings/user-management",
        ],
        "role_management": [
          "/app/settings/roles",
          "/settings/roles",
        ],
      };

      // Define permission mappings for sidebar items based on actual RBAC permissions
      const permissionMappings = {
        // Hotel Management
        "hotel management": "hotel_management:view",
        hotels: "hotel_management:view",

        // Destinations
        destinations: "destinations:view",
        destination: "destinations:view",

        // Room Management
        "room management": "rooms:view",
        rooms: "rooms:view",
        "room configs": "rooms:view",
        "room configurations": "rooms:view",
        availability: "rooms:availability", // Specific availability permission

        // Bookings - Only show for users with specific booking permissions
        bookings: ["bookings:view", "bookings:create", "bookings:edit", "bookings:delete"],
        booking: ["bookings:view", "bookings:create", "bookings:edit", "bookings:delete"],

        // Add-on Services
        "add-on services": "addon_services:view",

        // Add-ons Inventory
        "add ons": "addons:view",
        "add-ons": "addons:view",

        // Inventory Management
        inventory: "addons:view", // Using addons permission for inventory access
        "inventory management": "addons:view",

        // User Management
        "user management": "user_management:view",
        users: "user_management:view",

        // Role Management
        "role management": "role_management:view",
        roles: "role_management:view",
        "users & roles": "role_management:view",

        // Analytics
        analytics: "analytics:view",
        "store analytics": "analytics:store",
        "user analytics": "analytics:user",

        // Reports
        reports: "reports:view",
        "hotel reports": "reports:hotel",

        // Supplier Management - All sub-modules require base supplier_management:view
        "supplier management": "supplier_management:view",
        suppliers: "supplier_management:view",
        "products-services": "supplier_management:view",
        "products & services": "supplier_management:view",
        "supplier products": "supplier_management:view",
        "supplier offerings": "supplier_management:view",
        contracts: "supplier_management:view",
        "supplier contracts": "supplier_management:view",
        orders: "supplier_orders:view", // Specific permission for supplier order management
        "supplier orders": "supplier_orders:view",
        "on requests": "supplier_orders:view",
        "exchange rates": ["supplier_management:view", "exchange_rate_management:view"], // Requires both permissions
        "hotel pricing": "supplier_cost:view", // Specific permission for supplier cost management

        // Package Management - All sub-modules require base package_management:view
        "package management": "package_management:view",
        "package lookups": "package_management:view",
        packages: "package_management:view",

        // Vendor Management (Legacy)
        "vendor management": "vendor_management:view",
        vendors: "vendor_management:view",

        // Fulfillment Operations
        "fulfillment operations": "fulfillment_operations:view",
        itineraries: "fulfillment_operations:view",
        tasks: "fulfillment_operations:view",

        // Storefront
        storefront: "storefront:view",

        // Carts - Only show for users with cart or booking permissions
        carts: ["carts:view", "bookings:view", "bookings:create", "bookings:edit", "bookings:delete"],
        cart: ["carts:view", "bookings:view", "bookings:create", "bookings:edit", "bookings:delete"],

        // Subscriptions
        subscriptions: "subscriptions:view",
        subscription: "subscriptions:view",

        // Pricing
        pricing: "pricing:view",
        price: "pricing:view",
        rates: "pricing:view",

        // User Manual
        "user manual": "user_manual:view",

        // Settings - specific items that require admin or specific permissions
        "api keys": "admin_only",
        "publishable api keys": "admin_only",
        "secret api keys": "admin_only",
        webhooks: "admin_only",
        workflows: "admin_only",
        locations: "admin_only",
        shipping: "admin_only",
        tax: "admin_only",
        regions: "admin_only",
        currencies: "admin_only",
        "return reasons": "admin_only",
        "sales channels": "admin_only",
        "product types": "admin_only",
        "product tags": "admin_only",

        // Specific settings permissions
        "notification templates": "settings:notification_templates",
        "add-on categories": "settings:addon_categories",
        "bulk import": "settings:bulk_import",
        settings: "settings:view", // General settings access

        // Items to always hide
        search: "never_show", // Hide search from everyone including admins

        // User profile items that should always be visible to all authenticated users
        profile: "always_show",
        "profile settings": "always_show",
        "change password": "always_show",
        password: "always_show",
        account: "always_show",
        "account settings": "always_show",

        // Concierge Management - All sub-modules require base concierge_management:view
        "concierge management": "concierge_management:view",
        concierge: "concierge_management:view",
        "concierge tasks": "concierge_management:view",
        "concierge itineraries": "concierge_management:view",
        "concierge bookings": "concierge_management:view",
        "status configuration": "concierge_management:view",
        "email templates": "concierge_management:view",
      };

      // Function to check if a menu item belongs to a parent module that user lacks access to
      const shouldHideBasedOnParentModule = (text: string, href: string): boolean => {
        const normalizedText = text.toLowerCase();

        // First, check for items with specific permissions that override module permissions
        const isSupplierOrdersItem = (
          (normalizedText.includes("orders") && (href.includes("/supplier-management/") || href.includes("supplier"))) ||
          (normalizedText === "orders" && href.includes("/supplier-management/")) ||
          normalizedText.includes("on requests")
        );

        const isSupplierPricingItem = (
          normalizedText.includes("hotel pricing") ||
          (normalizedText.includes("pricing") && href.includes("/supplier-management/pricing"))
        );

        const isBookingItem = (
          normalizedText.includes("booking") &&
          !normalizedText.includes("concierge") // Exclude concierge bookings which have different permissions
        );

        const isCartItem = (
          normalizedText.includes("cart")
        );

        // Handle specific permission checks first (these override module permissions)
        if (isSupplierOrdersItem) {
          if (!isAdmin() && !hasPermission("supplier_orders:view")) {
            console.log(`[RBAC] Hiding supplier orders item "${text}" (href: ${href}) - missing permission: supplier_orders:view`);
            return true; // Hide this item
          } else {
            console.log(`[RBAC] Allowing supplier orders item "${text}" (href: ${href}) - has permission: supplier_orders:view`);
            return false; // Don't hide, user has specific permission
          }
        }

        if (isSupplierPricingItem) {
          if (!isAdmin() && !hasPermission("supplier_cost:view")) {
            console.log(`[RBAC] Hiding supplier pricing item "${text}" (href: ${href}) - missing permission: supplier_cost:view`);
            return true; // Hide this item
          } else {
            console.log(`[RBAC] Allowing supplier pricing item "${text}" (href: ${href}) - has permission: supplier_cost:view`);
            return false; // Don't hide, user has specific permission
          }
        }

        if (isBookingItem) {
          const bookingPermissions = ["bookings:view", "bookings:create", "bookings:edit", "bookings:delete"];
          if (!isAdmin() && !hasAnyPermission(bookingPermissions)) {
            console.log(`[RBAC] Hiding booking item "${text}" (href: ${href}) - missing any booking permission`);
            return true; // Hide this item
          } else {
            console.log(`[RBAC] Allowing booking item "${text}" (href: ${href}) - has at least one booking permission`);
            return false; // Don't hide, user has at least one booking permission
          }
        }

        if (isCartItem) {
          const cartPermissions = ["carts:view", "bookings:view", "bookings:create", "bookings:edit", "bookings:delete"];
          if (!isAdmin() && !hasAnyPermission(cartPermissions)) {
            console.log(`[RBAC] Hiding cart item "${text}" (href: ${href}) - missing any cart/booking permission`);
            return true; // Hide this item
          } else {
            console.log(`[RBAC] Allowing cart item "${text}" (href: ${href}) - has at least one cart/booking permission`);
            return false; // Don't hide, user has at least one cart/booking permission
          }
        }

        // Now check each parent module for general items
        for (const [moduleKey, permission] of Object.entries(parentModulePermissions)) {
          const modulePaths = modulePathMappings[moduleKey] || [];

          // Check if this item belongs to this module based on URL path
          const belongsToModule = modulePaths.some((path: string) => href.includes(path));

          // Also check based on text content for general supplier management items
          // (excluding items with specific permissions that were already handled above)
          const isSupplierManagementItem = moduleKey === "supplier_management" && (
            normalizedText.includes("supplier") ||
            normalizedText.includes("products & services") ||
            normalizedText.includes("products-services") ||
            normalizedText.includes("supplier offerings") ||
            normalizedText.includes("supplier products") ||
            normalizedText.includes("exchange rates") ||
            normalizedText.includes("contracts") ||
            normalizedText.includes("vendor")
          );

          // Also check based on text content for concierge management items
          const isConciergeManagementItem = moduleKey === "concierge_management" && (
            normalizedText.includes("concierge") ||
            (normalizedText.includes("tasks") && href.includes("/concierge-management/")) ||
            (normalizedText.includes("itineraries") && href.includes("/concierge-management/")) ||
            (normalizedText.includes("bookings") && href.includes("/concierge-management/"))
          );

          // Also check based on text content for hotel management items
          // Note: Bookings and Carts are excluded here as they require specific permissions
          const isHotelManagementItem = moduleKey === "hotel_management" && (
            normalizedText.includes("hotel") ||
            normalizedText.includes("destinations") ||
            (normalizedText.includes("rooms") && href.includes("/hotel-management/")) ||
            (normalizedText.includes("availability") && href.includes("/hotel-management/"))
          ) && !normalizedText.includes("booking") && !normalizedText.includes("cart"); // Exclude bookings and carts from hotel management fallback



          // Handle general module permission checks
          if (belongsToModule || isSupplierManagementItem || isConciergeManagementItem || isHotelManagementItem) {
            // Check if user has the parent module permission
            let hasRequiredPermission = false;

            if (Array.isArray(permission)) {
              // Special case for exchange rates - requires ALL permissions (AND logic)
              if (normalizedText.includes("exchange rates")) {
                hasRequiredPermission = permission.every(perm => hasPermission(perm));
              } else {
                // Handle array of permissions with OR logic (e.g., for bookings that can be accessed with multiple permissions)
                hasRequiredPermission = hasAnyPermission(permission);
              }
            } else {
              // Handle single permission
              hasRequiredPermission = hasPermission(permission);
            }

            if (!isAdmin() && !hasRequiredPermission) {
              const permissionText = Array.isArray(permission)
                ? (normalizedText.includes("exchange rates") ? permission.join(' AND ') : permission.join(' or '))
                : permission;
              console.log(`[RBAC] Hiding item "${text}" (href: ${href}) - missing permission: ${permissionText}`);
              return true; // Hide this item
            } else {
              const permissionText = Array.isArray(permission)
                ? (normalizedText.includes("exchange rates") ? permission.join(' AND ') : permission.join(' or '))
                : permission;
              console.log(`[RBAC] Allowing item "${text}" (href: ${href}) - has permission: ${permissionText}`);
            }
          }
        }

        return false; // Don't hide based on parent module
      };

      // Get static hidden items (normalized)
      const staticHiddenItems = HIDDEN_SIDEBAR_ITEMS.map((item) =>
        item.toLowerCase()
      );

      // Get exception items (normalized)
      const exceptionItems = EXCEPTION_ITEMS.map((item) => item.toLowerCase());

      let hiddenCount = 0;

      // Target navigation links
      const navLinks = document.querySelectorAll(
        'nav a, aside a, [role="navigation"] a, [data-testid="sidebar"] a'
      );

      navLinks.forEach((link) => {
        const text = link.textContent?.trim() || "";
        const normalizedText = text.toLowerCase();
        const href = (link as HTMLAnchorElement).href || "";

        // Skip if empty text
        if (!text) return;

        // Check for user profile and password related items that should always be visible
        const isUserProfileItem =
          normalizedText.includes("profile") ||
          normalizedText.includes("change password") ||
          normalizedText.includes("password") ||
          href.includes("/settings/profile") ||
          href.includes("/app/settings/profile") ||
          href.includes("/settings/change-password") ||
          href.includes("/app/settings/change-password") ||
          (normalizedText.includes("profile") &&
            normalizedText.includes("settings"));

        // Check static hiding first (from config)
        // But allow supplier management sub-items, role management items, and user profile items even if they match static hidden items
        const isSupplierManagementSubItem =
          href.includes("/supplier-management/") ||
          normalizedText.includes("supplier") ||
          (normalizedText.includes("products") &&
            normalizedText.includes("services")) ||
          (normalizedText.includes("orders") &&
            href.includes("/supplier-management/")) ||
          (normalizedText.includes("contracts") &&
            href.includes("/supplier-management/"));

        const isRoleManagementItem =
          href.includes("/app/settings/roles") ||
          href.includes("/settings/roles") ||
          normalizedText.includes("users & roles") ||
          normalizedText.includes("role management") ||
          (normalizedText.includes("users") &&
            normalizedText.includes("roles"));

        // Check if this item is in the exception list (should not be hidden)
        const isExceptionItem = exceptionItems.some((exception) => {
          return (
            normalizedText === exception || normalizedText.includes(exception)
          );
        });

        const shouldHideStatic =
          !isSupplierManagementSubItem &&
          !isRoleManagementItem &&
          !isUserProfileItem &&
          !isExceptionItem && // Don't hide if it's an exception item
          staticHiddenItems.some((item) => {
            return normalizedText === item || normalizedText.includes(item);
          });

        // Check permission-based hiding
        let shouldHidePermission = false;

        // Never hide user profile items regardless of permissions
        if (isUserProfileItem) {
          shouldHidePermission = false;
        } else {
          // Only check permissions for non-admin users
          if (!isAdmin() && currentUser?.rbac) {
            // Check if this menu item requires a specific permission
            const requiredPermission = Object.entries(permissionMappings).find(
              ([key]) => {
                return normalizedText.includes(key.toLowerCase());
              }
            )?.[1];

            if (requiredPermission) {
              if (requiredPermission === "never_show") {
                // Hide from everyone including admins
                shouldHidePermission = true;
              } else if (requiredPermission === "always_show") {
                // Always show these items to all authenticated users
                shouldHidePermission = false;
              } else if (requiredPermission === "admin_only") {
                // Hide admin-only items for non-admin users
                shouldHidePermission = true;
              } else {
                // Special handling for room availability - check both specific and general room permissions
                if (requiredPermission === "rooms:availability") {
                  const hasAvailabilityPermission = hasPermission("rooms:availability");
                  const hasRoomViewPermission = hasPermission("rooms:view");
                  const hasAnyRoomPermission = hasAvailabilityPermission || hasRoomViewPermission;

                  if (hasAnyRoomPermission) {
                    shouldHidePermission = false;
                    console.log(`[RBAC] Room availability check for "${text}": availability="${hasAvailabilityPermission}", rooms_view="${hasRoomViewPermission}", hide="${shouldHidePermission}"`);
                  } else {
                    // User doesn't have room permissions, check parent module permissions as fallback
                    const shouldHideParentModule = shouldHideBasedOnParentModule(text, href);
                    shouldHidePermission = shouldHideParentModule;
                    console.log(`[RBAC] Room availability check for "${text}": no_room_permissions, parent_module_check="${shouldHideParentModule}", hide="${shouldHidePermission}"`);
                  }
                } else {
                  // Check specific permission first for non-room items
                  let hasRequiredPermission = false;

                  if (Array.isArray(requiredPermission)) {
                    // Handle array of permissions (e.g., for carts that can be accessed with multiple permissions)
                    hasRequiredPermission = hasAnyPermission(requiredPermission);
                  } else {
                    // Handle single permission
                    hasRequiredPermission = hasPermission(requiredPermission);
                  }

                  if (hasRequiredPermission) {
                    // User has the specific permission, show the item
                    shouldHidePermission = false;
                    console.log(`[RBAC] Permission check for "${text}": required="${requiredPermission}", has="${hasRequiredPermission}", hide="${shouldHidePermission}"`);
                  } else {
                    // User doesn't have specific permission, check parent module permissions as fallback
                    const shouldHideParentModule = shouldHideBasedOnParentModule(text, href);
                    shouldHidePermission = shouldHideParentModule;
                    console.log(`[RBAC] Permission check for "${text}": required="${requiredPermission}", has="${hasRequiredPermission}", parent_module_check="${shouldHideParentModule}", hide="${shouldHidePermission}"`);
                  }
                }
              }
            } else {
              // No specific permission mapping, check parent module permissions only
              const shouldHideParentModule = shouldHideBasedOnParentModule(text, href);
              shouldHidePermission = shouldHideParentModule;
            }
          } else if (isAdmin()) {
            // Admin users can see everything except never_show items
            const requiredPermission = Object.entries(permissionMappings).find(
              ([key]) => {
                return normalizedText.includes(key.toLowerCase());
              }
            )?.[1];

            if (requiredPermission === "never_show") {
              shouldHidePermission = true;
            } else if (requiredPermission === "always_show") {
              // Always show these items to all authenticated users including admins
              shouldHidePermission = false;
            } else {
              shouldHidePermission = false;
            }
          }
        }

        // Hide if either static config or permission check says to hide
        if (shouldHideStatic || shouldHidePermission) {
          const listItem = link.closest("li");
          const targetElement = listItem || link;
          (targetElement as HTMLElement).style.display = "none";
          hiddenCount++;
        } else if (isSupplierManagementSubItem) {
          // Supplier management sub-item allowed
        } else if (isRoleManagementItem) {
          // Ensure the element is visible
          const listItem = link.closest("li");
          const targetElement = listItem || link;
          (targetElement as HTMLElement).style.display = "block";
          (targetElement as HTMLElement).style.visibility = "visible";
        } else if (isUserProfileItem) {
          // Ensure user profile items are always visible
          const listItem = link.closest("li");
          const targetElement = listItem || link;
          (targetElement as HTMLElement).style.display = "block";
          (targetElement as HTMLElement).style.visibility = "visible";
        }
      });

      // Special handling for search - look for elements containing "Search" text
      const allElements = document.querySelectorAll(
        'nav *, aside *, [role="navigation"] *, [data-testid="sidebar"] *'
      );

      allElements.forEach((element) => {
        const text = element.textContent?.trim() || "";
        const normalizedText = text.toLowerCase();

        // Only target elements that contain exactly "search" and are likely navigation items
        if (
          normalizedText === "search" &&
          (element.tagName === "SPAN" ||
            element.tagName === "DIV" ||
            element.tagName === "A" ||
            element.tagName === "BUTTON")
        ) {
          const listItem = element.closest("li");
          const targetElement = listItem || element.parentElement || element;

          (targetElement as HTMLElement).style.display = "none";
          hiddenCount++;
        }
      });

      // Handle duplicate "Supplier Management" items - keep only the first one
      const supplierMgmtLinks = document.querySelectorAll(
        'nav a, aside a, [role="navigation"] a'
      );
      let supplierMgmtCount = 0;

      supplierMgmtLinks.forEach((link) => {
        const text = link.textContent?.trim() || "";
        if (text === "Supplier Management") {
          supplierMgmtCount++;
          if (supplierMgmtCount > 1) {
            const listItem = link.closest("li");
            const targetElement = listItem || link;
            (targetElement as HTMLElement).style.display = "none";
            hiddenCount++;
          }
        }
      });

      // Mark sidebar as ready to show after permission filtering
      const sidebarElements = document.querySelectorAll(
        'nav, aside, [role="navigation"], [data-testid="sidebar"]'
      );

      sidebarElements.forEach((sidebar) => {
        (sidebar as HTMLElement).setAttribute("data-permissions-ready", "true");
      });
    };

    // Run the hiding logic
    hideSidebarItems();

    // Run once more after a short delay to catch late-loading content
    const timeoutId = setTimeout(hideSidebarItems, 1000);

    // Fallback: ensure sidebar shows after maximum wait time
    const fallbackTimeoutId = setTimeout(() => {
      const sidebarElements = document.querySelectorAll(
        'nav, aside, [role="navigation"], [data-testid="sidebar"]'
      );

      sidebarElements.forEach((sidebar) => {
        if (!(sidebar as HTMLElement).hasAttribute("data-permissions-ready")) {
          (sidebar as HTMLElement).setAttribute(
            "data-permissions-ready",
            "true"
          );
        }
      });
    }, 3000); // 3 second fallback

    // Set up MutationObserver to handle dynamically loaded content
    const observer = new MutationObserver((mutations) => {
      let shouldRerun = false;
      mutations.forEach((mutation) => {
        if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
          // Check if any added nodes contain navigation elements
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              if (
                element.querySelector(
                  'nav, aside, [role="navigation"], [data-testid="sidebar"]'
                ) ||
                element.matches(
                  'nav, aside, [role="navigation"], [data-testid="sidebar"]'
                )
              ) {
                shouldRerun = true;
              }
            }
          });
        }
      });

      if (shouldRerun) {
        setTimeout(hideSidebarItems, 100); // Small delay to let DOM settle
      }
    });

    // Observe changes to the document body
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
      clearTimeout(fallbackTimeoutId);
      observer.disconnect();
      // Clear the run flag for this path to allow re-running on next visit
      delete document.body.dataset[runKey];
    };
  }, [
    navigate,
    location.pathname,
    hasPermission,
    isAdmin,
    currentUser,
    loading,
  ]);

  return null;
};

export const config = defineWidgetConfig({
  zone: [
    "product.list.before",
    "order.list.before",
    "customer.list.before",
    "store.details.before",
    "inventory_item.list.before",
    "promotion.list.before",
    "price_list.list.before",
  ],
});

export default PermissionBasedSidebarHider;
