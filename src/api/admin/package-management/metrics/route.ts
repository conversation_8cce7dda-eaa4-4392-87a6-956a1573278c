import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

// GET /admin/package-management/metrics
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Fetch package lookups count
    const {
      metadata: { count: totalPackageLookupsCount },
    } = await query.graph({
      entity: "package_lookups",
      fields: ["id"],
      filters: {},
      pagination: {
        skip: 0,
        take: 1, // We only need the count
      },
    });

    // Fetch active package lookups count
    const {
      metadata: { count: activePackageLookupsCount },
    } = await query.graph({
      entity: "package_lookups",
      fields: ["id"],
      filters: {
        is_active: true,
      },
      pagination: {
        skip: 0,
        take: 1, // We only need the count
      },
    });

    // Fetch packages count
    const {
      metadata: { count: totalPackagesCount },
    } = await query.graph({
      entity: "packages",
      fields: ["id"],
      filters: {},
      pagination: {
        skip: 0,
        take: 1, // We only need the count
      },
    });

    // Fetch all packages to calculate active/expired counts
    const { data: allPackages } = await query.graph({
      entity: "packages",
      fields: ["id", "valid_from_date", "valid_to_date"],
      filters: {},
    });

    // Calculate active and expired packages
    const currentDate = new Date();
    let activePackagesCount = 0;
    let expiredPackagesCount = 0;
    let upcomingPackagesCount = 0;

    allPackages.forEach((pkg: any) => {
      const validFrom = new Date(pkg.valid_from_date);
      const validTo = new Date(pkg.valid_to_date);

      if (currentDate >= validFrom && currentDate <= validTo) {
        activePackagesCount++;
      } else if (currentDate > validTo) {
        expiredPackagesCount++;
      } else if (currentDate < validFrom) {
        upcomingPackagesCount++;
      }
    });

    const metrics = {
      total_package_lookups_count: totalPackageLookupsCount,
      active_package_lookups_count: activePackageLookupsCount,
      inactive_package_lookups_count: totalPackageLookupsCount - activePackageLookupsCount,
      total_packages_count: totalPackagesCount,
      active_packages_count: activePackagesCount,
      expired_packages_count: expiredPackagesCount,
      upcoming_packages_count: upcomingPackagesCount,
    };

    res.json({
      metrics,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching package management metrics:", error);
    res.status(500).json({
      message: "Failed to fetch package management metrics",
      error: error.message,
    });
  }
};
