import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { SUPPLIER_MANAGEMENT_MODULE } from "../../../../modules/vendor_management";
import {
  PostAdminCreateExchangeRate,
  GetAdminExchangeRatesQuery,
} from "./validators";
import {
  CreateExchangeRateWorkflow,
} from "src/workflows/vendor_management/exchange-rate";

/**
 * GET /admin/supplier-management/exchange-rates
 * List exchange rates with filtering and pagination
 */
export async function GET(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    // Validate query parameters
    const validatedQuery = GetAdminExchangeRatesQuery.parse(req.query || {});

    const supplierModuleService = req.scope.resolve(SUPPLIER_MANAGEMENT_MODULE);

    const {
      limit,
      offset,
      date,
      date_from,
      date_to,
      base_currency,
      selling_currency,
      exchange_rate_min,
      exchange_rate_max,
      sort_by,
      sort_order,
    } = validatedQuery;

    // Build filters for the service
    const filters: Record<string, any> = {};

    // Date filters
    if (date) {
      filters.date = date;
    } else if (date_from || date_to) {
      if (date_from && date_to) {
        filters.date_from = date_from;
        filters.date_to = date_to;
      } else if (date_from) {
        filters.date_from = date_from;
      } else if (date_to) {
        filters.date_to = date_to;
      }
    }

    // Currency filters
    if (base_currency) {
      filters.base_currency = base_currency;
    }

    if (selling_currency) {
      filters.selling_currency = selling_currency;
    }

    // Exchange rate range filters
    if (exchange_rate_min) {
      filters.exchange_rate_min = exchange_rate_min;
    }

    if (exchange_rate_max) {
      filters.exchange_rate_max = exchange_rate_max;
    }

    // Add pagination
    filters.limit = limit;
    filters.offset = offset;

    // Build order configuration
    const options: any = {};
    const orderConfig: any = {};
    orderConfig[sort_by] = sort_order.toUpperCase();
    options.order = orderConfig;

    try {
      // Use the service to list exchange rates
      const result = await supplierModuleService.listExchangeRates(filters, options);

      // Return exchange rates with proper metadata
      res.json({
        exchange_rates: result.exchange_rates || [],
        count: result.count || 0,
        limit: Number(limit),
        offset: Number(offset),
      });
    } catch (serviceError) {
      console.error("Service error in listExchangeRates:", serviceError);
      return res.status(500).json({
        type: "service_error",
        message: "Failed to retrieve exchange rates",
        details: serviceError.message,
      });
    }
  } catch (error) {
    console.error("Error in GET /admin/supplier-management/exchange-rates:", error);

    if (error.errors) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid query parameters",
        errors: error.errors,
      });
    }

    return res.status(500).json({
      type: "internal_error",
      message: "Internal server error",
      details: error.message,
    });
  }
}

/**
 * POST /admin/supplier-management/exchange-rates
 * Create a new exchange rate
 */
export async function POST(
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) {
  try {
    // Validate request body
    const validatedBody = PostAdminCreateExchangeRate.parse(req.body);

    const { date, valid_from, valid_to, base_currency, selling_currency, exchange_rate, metadata } = validatedBody;

    const exchangeRateData = {
      date, // Keep for backward compatibility
      valid_from,
      valid_to,
      base_currency,
      selling_currency,
      exchange_rate,
      metadata: {
        ...metadata,
        created_at: new Date().toISOString(),
      },
    };

    const { result } = await CreateExchangeRateWorkflow(req.scope).run({
      input: exchangeRateData,
    });

    res.status(201).json({
      exchange_rate: result,
      message: "Exchange rate created successfully",
    });
  } catch (error) {
    console.error("Error creating exchange rate:", error);

    if (error.errors) {
      return res.status(400).json({
        type: "validation_error",
        message: "Invalid request body",
        errors: error.errors,
      });
    }

    // Handle specific error types
    if (error.message.includes("already exists")) {
      return res.status(409).json({
        type: "conflict_error",
        message: error.message,
      });
    }

    if (error.message.includes("validation") || error.message.includes("required")) {
      return res.status(400).json({
        type: "validation_error",
        message: error.message,
      });
    }

    return res.status(500).json({
      type: "internal_error",
      message: "Failed to create exchange rate",
      details: error.message,
    });
  }
}


