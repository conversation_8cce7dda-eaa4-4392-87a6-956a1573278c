import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";

// Base schema for common fields
const BasePricingDataSchema = z.object({
  room_config_name: z.string().min(1, "Room Config Name is required"),
  occupancy_name: z.string().min(1, "Occupancy Name is required"),
  meal_plan_name: z.string().min(1, "Meal Plan Name is required"),
});

// New template format schema
const NewFormatPricingDataSchema = BasePricingDataSchema.extend({
  date_from: z.string().min(1, "Date From is required"),
  date_to: z.string().min(1, "Date To is required"),
  hotel_name: z.string().min(1, "Hotel Name is required"),

  // New format pricing fields
  cost: z.union([z.string(), z.number()]).optional(),
  fixed_margin: z.union([z.string(), z.number()]).optional(),
  margin_percentage: z.union([z.string(), z.number()]).optional(),
});

// Legacy template format schema
const LegacyFormatPricingDataSchema = BasePricingDataSchema.extend({
  currency_code: z.string().min(1, "Currency Code is required"),

  // Weekday price fields
  monday_price: z.union([z.string(), z.number()]).optional(),
  tuesday_price: z.union([z.string(), z.number()]).optional(),
  wednesday_price: z.union([z.string(), z.number()]).optional(),
  thursday_price: z.union([z.string(), z.number()]).optional(),
  friday_price: z.union([z.string(), z.number()]).optional(),
  saturday_price: z.union([z.string(), z.number()]).optional(),
  sunday_price: z.union([z.string(), z.number()]).optional(),

  // Default cost/margin fields
  default_gross_cost: z.union([z.string(), z.number()]).optional(),
  default_fixed_margin: z.union([z.string(), z.number()]).optional(),
  default_margin_percentage: z.union([z.string(), z.number()]).optional(),

  // Weekday cost fields
  monday_gross_cost: z.union([z.string(), z.number()]).optional(),
  tuesday_gross_cost: z.union([z.string(), z.number()]).optional(),
  wednesday_gross_cost: z.union([z.string(), z.number()]).optional(),
  thursday_gross_cost: z.union([z.string(), z.number()]).optional(),
  friday_gross_cost: z.union([z.string(), z.number()]).optional(),
  saturday_gross_cost: z.union([z.string(), z.number()]).optional(),
  sunday_gross_cost: z.union([z.string(), z.number()]).optional(),

  // Weekday fixed margin fields
  monday_fixed_margin: z.union([z.string(), z.number()]).optional(),
  tuesday_fixed_margin: z.union([z.string(), z.number()]).optional(),
  wednesday_fixed_margin: z.union([z.string(), z.number()]).optional(),
  thursday_fixed_margin: z.union([z.string(), z.number()]).optional(),
  friday_fixed_margin: z.union([z.string(), z.number()]).optional(),
  saturday_fixed_margin: z.union([z.string(), z.number()]).optional(),
  sunday_fixed_margin: z.union([z.string(), z.number()]).optional(),

  // Weekday margin percentage fields
  monday_margin_percentage: z.union([z.string(), z.number()]).optional(),
  tuesday_margin_percentage: z.union([z.string(), z.number()]).optional(),
  wednesday_margin_percentage: z.union([z.string(), z.number()]).optional(),
  thursday_margin_percentage: z.union([z.string(), z.number()]).optional(),
  friday_margin_percentage: z.union([z.string(), z.number()]).optional(),
  saturday_margin_percentage: z.union([z.string(), z.number()]).optional(),
  sunday_margin_percentage: z.union([z.string(), z.number()]).optional(),

  // Optional seasonal fields
  seasonal_period_name: z.string().optional(),
  seasonal_start_date: z.string().optional(),
  seasonal_end_date: z.string().optional(),
});

// Union schema that accepts either format
const PricingDataSchema = z.union([
  NewFormatPricingDataSchema,
  LegacyFormatPricingDataSchema,
]);

// Main validation schema for import request
const PostAdminImportHotelPricingSchema = z.object({
  pricing_data: z
    .array(PricingDataSchema)
    .min(1, "At least one pricing record is required"),
  currency: z.string().optional(),
  batch_size: z.number().optional().default(50), // Process in batches to avoid payload size limits
});

type PostAdminImportHotelPricingType = z.infer<
  typeof PostAdminImportHotelPricingSchema
>;

/**
 * POST /admin/hotel-management/hotels/[id]/pricing/import
 * Import hotel pricing data from parsed CSV/Excel data
 */
export const POST = async (
  req: MedusaRequest<{}, PostAdminImportHotelPricingType>,
  res: MedusaResponse
) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Validate request body
    const validatedBody = PostAdminImportHotelPricingSchema.parse(req.body);
    const { pricing_data, currency, batch_size } = validatedBody;

    // Verify hotel exists
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: { id: [hotelId] },
      fields: ["id", "name"],
    });

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Hotel not found",
        type: "not_found",
      });
    }

    // Get room configurations for validation
    const { data: roomConfigs } = await query.graph({
      entity: "product",
      filters: {},
      fields: ["id", "title", "handle", "metadata"],
    });

    const hotelRoomConfigs =
      roomConfigs?.filter(
        (product) =>
          product.metadata &&
          (product.metadata.hotel_id === hotelId ||
            product.metadata.type === "room_config")
      ) || [];

    // Get occupancy configurations
    const { data: occupancyConfigs } = await query.graph({
      entity: "occupancy_config",
      filters: { hotel_id: hotelId },
      fields: ["id", "name"],
    });

    // Get meal plans
    const mealPlans = await hotelPricingService.listMealPlans({
      hotel_id: hotelId,
    });

    // Create lookup maps (trim whitespace to handle data inconsistencies)
    const roomConfigMap = new Map(
      hotelRoomConfigs.map((rc) => [rc.title.trim().toLowerCase(), rc])
    );
    const occupancyMap = new Map(
      occupancyConfigs.map((oc) => [oc.name.trim().toLowerCase(), oc])
    );
    const mealPlanMap = new Map(
      mealPlans.map((mp) => [mp.name.trim().toLowerCase(), mp])
    );

    // No alias/variant detection: meal plan must match exactly (by name) or be provided as a valid ID
    // Build a map by id as well for direct id lookups
    const mealPlanIdMap = new Map(mealPlans.map((mp) => [mp.id, mp]));

    // Split data into chunks to avoid payload size issues
    const chunks = [];
    for (let i = 0; i < pricing_data.length; i += batch_size) {
      chunks.push(pricing_data.slice(i, i + batch_size));
    }

    // Process and validate each pricing record
    const results = {
      imported: 0,
      errors: [] as Array<{ row: number; message: string; data: any }>,
    };

    // Bulk operations arrays for performance optimization
    const bulkSeasonalRules: any[] = [];
    const baseRuleCache = new Map<string, any>();

    // Process each chunk
    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
      const chunk = chunks[chunkIndex];

      for (let i = 0; i < chunk.length; i++) {
        const record = chunk[i];
        const globalIndex = chunkIndex * batch_size + i; // Calculate global index for error reporting

        try {
          // Detect template format
          const isNewFormat =
            record.hasOwnProperty("date_from") ||
            record.hasOwnProperty("date_to") ||
            record.hasOwnProperty("hotel_name");


          // Find matching entities (trim whitespace to handle data inconsistencies)
          const roomConfigKey = record.room_config_name.trim().toLowerCase();
          const occupancyKey = record.occupancy_name.trim().toLowerCase();
          const roomConfig = roomConfigMap.get(roomConfigKey);
          const occupancyConfig = occupancyMap.get(occupancyKey);


          // Strict meal plan mapping: derive meal_plan_id from the given value only
          // Accept either a valid meal plan ID or an exact name match (case-insensitive)
          const rawMealPlan = (record.meal_plan_name ?? "").toString().trim();
          const mealPlanKey = rawMealPlan.toLowerCase();

          let mealPlan = null as null | (typeof mealPlans)[number];

          // If the provided value matches an existing meal plan ID, use it
          if (mealPlanIdMap.has(rawMealPlan)) {
            mealPlan = mealPlanIdMap.get(rawMealPlan)!;
          } else if (mealPlanMap.has(mealPlanKey)) {
            // Else try by name (case-insensitive)
            mealPlan = mealPlanMap.get(mealPlanKey)!;
          } else {
            throw new Error(
              `Meal plan '${record.meal_plan_name}' not found. Provide a valid meal plan name or ID.`
            );
          }

          // Validate entity existence
          if (!roomConfig) {
            throw new Error(
              `Room configuration '${record.room_config_name}' not found`
            );
          }
          if (!occupancyConfig) {
            throw new Error(
              `Occupancy configuration '${record.occupancy_name}' not found`
            );
          }
          // Validate meal plan resolution
          if (!mealPlan) {
            throw new Error(
              `Meal plan '${record.meal_plan_name}' not found or could not resolve a concrete 'No Meal Plan' record`
            );
          }

          // Process pricing data based on format
          if (isNewFormat) {
            // New format: Handle date range and simplified pricing fields
            const dateFrom = record.date_from;
            const dateTo = record.date_to;
            const cost = Math.round(record.cost * 100);

            const fixedMargin = record.fixed_margin;
            const marginPercentage = record.margin_percentage;

            // Validate date format
            if (!dateFrom || !dateTo) {
              throw new Error(
                "Date From and Date To are required for new format"
              );
            }

            // Generate individual daily date ranges
            const startDate = new Date(dateFrom);
            const endDate = new Date(dateTo);
            const dailyRanges: Array<{ from: Date; to: Date }> = [];

            // Create consecutive daily ranges
            let currentDate = new Date(startDate);
            while (currentDate <= endDate) {
              const nextDate = new Date(currentDate);
              nextDate.setDate(nextDate.getDate() + 1);

              dailyRanges.push({
                from: new Date(currentDate),
                to: nextDate
              });

              currentDate = nextDate;
            }

            // Normalize currency (fallback to CHF if not provided)
            const currencyCode = currency || "CHF";

            // We must ensure a BasePriceRule exists for this combination and currency,
            // then create a SeasonalPriceRule anchored to it. Use the existing bulk endpoints' logic from service.

            // 1) Ensure base rule exists (upsert) for this (room_config, occupancy, meal_plan, currency)
            // Use caching to avoid repeated lookups for the same combination
            const baseRuleKey = `${roomConfig.id}-${occupancyConfig.id}-${mealPlan?.id || 'null'}-${currencyCode}`;
            let baseRule = baseRuleCache.get(baseRuleKey);

            if (!baseRule) {
              // Try to find existing base price rule first
              try {
                const existing = await hotelPricingService.listBasePriceRules({
                  room_config_id: roomConfig.id,
                  occupancy_type_id: occupancyConfig.id,
                  meal_plan_id: mealPlan?.id || null,
                  currency_code: currencyCode,
                });
                baseRule = existing[0] || null;
              } catch (e) {
                console.warn(
                  "[Pricing Import API] listBasePriceRules failed, will try creating a base rule:",
                  e
                );
              }

              if (!baseRule) {
                // Create a minimal base rule so seasonal can attach to it
                // amount is required by model; set 0 if unknown
                const created = await hotelPricingService.createBasePriceRules([
                  {
                    hotel_id: hotelId,
                    room_config_id: roomConfig.id,
                    occupancy_type_id: occupancyConfig.id,
                    meal_plan_id: mealPlan?.id || null,
                    currency_code: currencyCode,
                    amount: 0,
                    // no default_* or weekday_* needed at creation for seasonal import
                  },
                ]);
                baseRule = created[0];
              }

              // Cache the base rule for reuse
              baseRuleCache.set(baseRuleKey, baseRule);
            }

            if (!baseRule?.id) {
              throw new Error(
                "Failed to ensure base price rule for seasonal import."
              );
            }

            for (let dayIndex = 0; dayIndex < dailyRanges.length; dayIndex++) {
              const dailyRange = dailyRanges[dayIndex];
              const dayFrom = dailyRange.from;
              const dayTo = dailyRange.to;

              // 2) Build weekday prices and weekday cost/margin values for the seasonal rule.
              // To help UI detect seasonal overrides without changing UI logic, we will compute weekday_prices
              // from cost/margin when provided (fallback), instead of keeping them all zeros.
              const weekdayPrices = {
                mon: 0,
                tue: 0,
                wed: 0,
                thu: 0,
                fri: 0,
                sat: 0,
                sun: 0,
              };

              // Parse cost/margin -> store values as provided (no unit conversion)
              const defaultValues: any = {};
              if (cost !== undefined && cost !== null && !isNaN(cost)) {
                const numericCost = parseFloat(cost.toString());
                if (!isNaN(numericCost) && numericCost >= 0) {
                  defaultValues.gross_cost = Math.round(numericCost);
                }
              }
              if (
                fixedMargin !== undefined &&
                fixedMargin !== null &&
                fixedMargin !== ""
              ) {
                const numericMargin = parseFloat(fixedMargin.toString());
                if (!isNaN(numericMargin)) {
                  defaultValues.fixed_margin = Math.round(numericMargin);
                }
              }
              if (
                marginPercentage !== undefined &&
                marginPercentage !== null &&
                marginPercentage !== ""
              ) {
                const numericPercentage = parseFloat(marginPercentage.toString());
                if (
                  !isNaN(numericPercentage) &&
                  numericPercentage >= 0 &&
                  numericPercentage <= 100
                ) {
                  defaultValues.margin_percentage = numericPercentage;
                }
              }

              // Derive weekday_values from default_values to ensure UI picks up seasonal cost/margin per-day overrides.
              // If fixed_margin is provided (>0), it takes precedence; otherwise margin_percentage is used.
              // We only set weekday_values fields when we have meaningful values, to avoid zeroing out base values unintentionally.
              const hasDefaultCost =
                typeof defaultValues.gross_cost === "number" &&
                defaultValues.gross_cost > 0;
              const hasDefaultFixedMargin =
                typeof defaultValues.fixed_margin === "number" &&
                defaultValues.fixed_margin > 0;
              const hasDefaultMarginPct =
                typeof defaultValues.margin_percentage === "number" &&
                defaultValues.margin_percentage > 0;

              const weekdayValues: any = {};
              const initDay = (
                key: "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun"
              ) => {
                const day: any = {};
                if (hasDefaultCost) day.gross_cost = defaultValues.gross_cost; // cents
                if (hasDefaultFixedMargin) {
                  day.fixed_margin = defaultValues.fixed_margin; // cents
                  // When fixed margin present, ignore percentage for day
                } else if (hasDefaultMarginPct) {
                  day.margin_percentage = defaultValues.margin_percentage; // percentage
                }
                if (Object.keys(day).length > 0) {
                  weekdayValues[key] = day;
                }
              };
              initDay("mon");
              initDay("tue");
              initDay("wed");
              initDay("thu");
              initDay("fri");
              initDay("sat");
              initDay("sun");

              // NEW: Compute weekday_prices from cost/margin when provided, so seasonal is detected in UI.
              // If explicit weekday_prices are all zero, derive a price using:
              // price = cost + fixed_margin OR price = cost / (1 - margin%)
              // Values are used as provided (no conversion).
              const computeFrom = (
                dayKey: "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun"
              ) => {
                const dayVals = weekdayValues[dayKey] || {};
                const baseCost =
                  dayVals.gross_cost ?? defaultValues.gross_cost ?? 0;
                const fixed =
                  dayVals.fixed_margin ?? defaultValues.fixed_margin ?? 0;
                const pct =
                  dayVals.margin_percentage ??
                  defaultValues.margin_percentage ??
                  0;

                if (baseCost > 0) {
                  if (fixed > 0) {
                    return baseCost + fixed;
                  }
                  if (pct > 0 && pct < 100) {
                    return Math.round(baseCost / (1 - pct / 100));
                  }
                }
                return 0;
              };

              // Only override weekdayPrices when we have meaningful defaults (cost or margin)
              const hasMeaningfulDefaults =
                hasDefaultCost || hasDefaultFixedMargin || hasDefaultMarginPct;
              if (hasMeaningfulDefaults) {
                (
                  ["mon", "tue", "wed", "thu", "fri", "sat", "sun"] as const
                ).forEach((d) => {
                  // If already non-zero (future extension), keep it; else compute
                  if (!weekdayPrices[d] || weekdayPrices[d] === 0) {
                    const computed = computeFrom(d);
                    if (computed > 0) {
                      weekdayPrices[d] = computed;
                    }
                  }
                });
              }

              // 3) Create seasonal override anchored to base rule for this specific day
              // Use description to hold a consistent season name for the daily range
              const seasonName = `Imported Daily ${dayFrom.toISOString().split('T')[0]} to ${dayTo.toISOString().split('T')[0]}`;

              // Choose a representative non-zero amount for the seasonal rule to avoid 0 in consumers relying on amount.
              // Prefer Monday, else first non-zero from the week, else 0. Values as provided.
              const representativeAmount =
                weekdayPrices.mon > 0
                  ? weekdayPrices.mon
                  : (["tue", "wed", "thu", "fri", "sat", "sun"] as const).reduce(
                      (acc, d) => (acc > 0 ? acc : weekdayPrices[d] || 0),
                      0
                    );

              // Collect seasonal price rule data for bulk creation
              const seasonalRuleData = {
                base_price_rule_id: baseRule.id,
                start_date: dayFrom,
                end_date: dayTo,
                amount: representativeAmount,
                currency_code: currencyCode,
                description: seasonName,
                // Stronger priority to resolve overlaps deterministically: YYYYMMDDHHmm as a SAFE integer within DB column range
                // Some environments use SMALLINT/INTEGER for priority; reduce scale to avoid "integer out of range".
                // Keep ordering semantics by compressing HHmm into a smaller bias (HH * 2 + minuteHalf).
                // Example: 09:33 -> 9*2 + 1 = 19  (00..23 => 0..46 and + minuteHalf 0/1 -> 0..47)
                priority: (() => {
                  const now = new Date();
                  const yyyy = now.getFullYear().toString();
                  const mm = String(now.getMonth() + 1).padStart(2, "0");
                  const dd = String(now.getDate()).padStart(2, "0");
                  const hour = now.getHours();
                  const minute = now.getMinutes();
                  const bias = hour * 2 + (minute >= 30 ? 1 : 0); // 0..47
                  // Build as YYYYMMDD plus a two-digit bias ensuring it stays within 32-bit signed int
                  // YYYYMMDD max ~ 99991231, adding 2 digits keeps it under ~ 9999123147 (< 2,147,483,647)
                  return parseInt(
                    `${yyyy}${mm}${dd}${String(bias).padStart(2, "0")}`
                  );
                })(),
                metadata: {
                  imported_at: new Date().toISOString(),
                  import_source: "bulk_template_daily",
                  hotel_name: record.hotel_name,
                  original_date_range: `${dateFrom} to ${dateTo}`,
                  daily_index: dayIndex + 1,
                  total_days: dailyRanges.length,
                  // Store weekday prices and default_values similar to seasonal bulk endpoint for consistency
                  // weekday_prices are now computed from cost/margin when provided, so UI can detect seasonal override
                  weekday_prices: {
                    mon: weekdayPrices.mon,
                    tue: weekdayPrices.tue,
                    wed: weekdayPrices.wed,
                    thu: weekdayPrices.thu,
                    fri: weekdayPrices.fri,
                    sat: weekdayPrices.sat,
                    sun: weekdayPrices.sun,
                  },
                  default_values: defaultValues,
                  // Populate weekday_values so the calendar can apply seasonal cost/margins per day
                  // Only included when we have at least one non-zero/meaningful default value
                  ...(Object.keys(weekdayValues).length > 0
                    ? { weekday_values: weekdayValues }
                    : {}),
                },
              };

              // Add to bulk creation array instead of creating immediately
              bulkSeasonalRules.push(seasonalRuleData);
            } // End of daily range loop

          } else {
            // Legacy format: Handle weekday prices and cost/margin data
            const weekdayData: any = {};
            const weekdays = [
              "monday",
              "tuesday",
              "wednesday",
              "thursday",
              "friday",
              "saturday",
              "sunday",
            ];

            // Parse default cost/margin values (no conversion)
            const defaultValues: any = {};
            if (
              record.default_gross_cost !== undefined &&
              record.default_gross_cost !== null &&
              record.default_gross_cost !== ""
            ) {
              const cost = parseFloat(record.default_gross_cost.toString());
              if (!isNaN(cost) && cost >= 0) {
                defaultValues.default_gross_cost = Math.round(cost);
              }
            }

            if (
              record.default_fixed_margin !== undefined &&
              record.default_fixed_margin !== null &&
              record.default_fixed_margin !== ""
            ) {
              const margin = parseFloat(record.default_fixed_margin.toString());
              if (!isNaN(margin) && margin >= 0) {
                defaultValues.default_fixed_margin = Math.round(margin);
              }
            }

            if (
              record.default_margin_percentage !== undefined &&
              record.default_margin_percentage !== null &&
              record.default_margin_percentage !== ""
            ) {
              const percentage = parseFloat(
                record.default_margin_percentage.toString()
              );
              if (!isNaN(percentage) && percentage >= 0 && percentage <= 100) {
                defaultValues.default_margin_percentage = percentage; // Store as percentage
              }
            }

            weekdays.forEach((day) => {
              // Parse weekday prices (no conversion)
              const priceField = `${day}_price`;
              const price = record[priceField];
              if (price !== undefined && price !== null && price !== "") {
                const numericPrice = parseFloat(price.toString());
                if (!isNaN(numericPrice) && numericPrice >= 0) {
                  weekdayData[priceField] = Math.round(numericPrice);
                }
              }

              // Parse weekday cost data (no conversion)
              const costField = `${day}_gross_cost`;
              const cost = record[costField];
              if (cost !== undefined && cost !== null && cost !== "") {
                const numericCost = parseFloat(cost.toString());
                if (!isNaN(numericCost) && numericCost >= 0) {
                  weekdayData[costField] = Math.round(numericCost);
                }
              }

              // Parse weekday fixed margin data (no conversion)
              const fixedMarginField = `${day}_fixed_margin`;
              const fixedMargin = record[fixedMarginField];
              if (
                fixedMargin !== undefined &&
                fixedMargin !== null &&
                fixedMargin !== ""
              ) {
                const numericMargin = parseFloat(fixedMargin.toString());
                if (!isNaN(numericMargin) && numericMargin >= 0) {
                  weekdayData[fixedMarginField] = Math.round(numericMargin);
                }
              }

              // Parse weekday margin percentage data
              const marginPercentageField = `${day}_margin_percentage`;
              const marginPercentage = record[marginPercentageField];
              if (
                marginPercentage !== undefined &&
                marginPercentage !== null &&
                marginPercentage !== ""
              ) {
                const numericPercentage = parseFloat(
                  marginPercentage.toString()
                );
                if (
                  !isNaN(numericPercentage) &&
                  numericPercentage >= 0 &&
                  numericPercentage <= 100
                ) {
                  weekdayData[marginPercentageField] = numericPercentage; // Store as percentage
                }
              }
            });

            // Check if base price rule already exists
            const existingRules = await hotelPricingService.listBasePriceRules({
              room_config_id: roomConfig.id,
              occupancy_type_id: occupancyConfig.id,
              meal_plan_id: mealPlan?.id || null,
              currency_code: record.currency_code,
            });


            if (existingRules.length > 0) {
              // Update existing rule
              const existingRule = existingRules[0];
              const updateData = {
                id: existingRule.id,
                ...defaultValues,
                ...weekdayData,
                currency_code: record.currency_code,
              };
              const updatedRules =
                await hotelPricingService.updateBasePriceRules([updateData]);
            } else {
              // Create new rule
              const createData = {
                hotel_id: hotelId,
                room_config_id: roomConfig.id,
                occupancy_type_id: occupancyConfig.id,
                meal_plan_id: mealPlan?.id || null,
                currency_code: record.currency_code,
                amount:
                  weekdayData.monday_price || weekdayData.sunday_price || 0, // Fallback amount
                ...defaultValues,
                ...weekdayData,
              };
              const createdRules =
                await hotelPricingService.createBasePriceRules([createData]);
            }

            results.imported++;
          } // End of legacy format processing
        } catch (error) {
          console.error(
            `[Pricing Import API] Error processing record ${globalIndex + 1}:`,
            error
          );
          results.errors.push({
            row: globalIndex + 1,
            message: error instanceof Error ? error.message : "Unknown error",
            data: record,
          });
        }
      }
    }

    // Perform bulk creation of all collected seasonal price rules
    if (bulkSeasonalRules.length > 0) {

      try {
        const bulkStartTime = Date.now();
        const createdSeasonalRules = await hotelPricingService.createSeasonalPriceRules(bulkSeasonalRules);
        const bulkEndTime = Date.now();

        // Update the imported count with the actual number of created seasonal rules
        results.imported = createdSeasonalRules.length;

      } catch (bulkError) {
        // Add bulk creation error to results
        results.errors.push({
          row: 0,
          message: `Bulk seasonal rule creation failed: ${bulkError instanceof Error ? bulkError.message : 'Unknown error'}`,
          data: { bulkSeasonalRulesCount: bulkSeasonalRules.length }
        });
      }
    }


    const response = {
      success: results.errors.length === 0,
      message:
        results.errors.length === 0
          ? `Successfully imported ${results.imported} pricing records`
          : `Imported ${results.imported} records with ${results.errors.length} errors`,
      imported: results.imported,
      errors: results.errors,
      total_processed: pricing_data.length,
    };

    if (results.errors.length > 0) {
      return res.status(207).json(response); // 207 Multi-Status for partial success
    }

    return res.status(201).json(response);
  } catch (error) {
    console.error("Error importing hotel pricing data:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        type: "validation_error",
        errors: error.errors,
      });
    }

    return res.status(500).json({
      success: false,
      message: "Failed to import pricing data",
      type: "internal_error",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
