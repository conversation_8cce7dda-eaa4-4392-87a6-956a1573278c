import { z } from "zod";

export const PostAdminCreatePackageLookup = z.object({
  package_name: z.string().min(1, "Package name is required"),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
});

export const PostAdminUpdatePackageLookup = z.object({
  id: z.string(),
  package_name: z.string().min(1, "Package name is required").optional(),
  description: z.string().optional(),
  is_active: z.boolean().optional(),
});

export const PostAdminDeletePackageLookup = z.object({
  ids: z.array(z.string()),
});
