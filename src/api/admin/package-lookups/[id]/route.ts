import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

// GET /admin/package-lookups/:id
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const { id } = req.params;

    const { data: packageLookups } = await query.graph({
      entity: "package_lookups",
      filters: {
        id: [id],
      },
      fields: ["*"],
    });

    if (!packageLookups || packageLookups.length === 0) {
      return res.status(404).json({
        message: "Package lookup not found",
      });
    }

    res.json({ package_lookup: packageLookups[0] });
  } catch (error) {
    console.error("Get package lookup API error:", error);
    return res.status(500).json({
      message: "Failed to retrieve package lookup",
    });
  }
};
