import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import {
  PostAdminCreatePackageLookup,
  PostAdminUpdatePackageLookup,
  PostAdminDeletePackageLookup,
} from "./validators";
import { CreatePackageLookupWorkflow } from "src/workflows/package-lookup/create-package-lookup";
import { UpdatePackageLookupWorkflow } from "src/workflows/package-lookup/update-package-lookup";
import { DeletePackageLookupWorkflow } from "src/workflows/package-lookup/delete-package-lookup";

type PostAdminCreatePackageLookupType = z.infer<typeof PostAdminCreatePackageLookup>;
type PostAdminUpdatePackageLookupType = z.infer<typeof PostAdminUpdatePackageLookup>;
type PostAdminDeletePackageLookupType = z.infer<typeof PostAdminDeletePackageLookup>;

// GET /admin/package-lookups
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

  const { limit = 20, offset = 0, is_active, q, order } = req.query || {};

  // Build filters
  const filters: any = {};
  if (is_active !== undefined) {
    filters.is_active = is_active === "true";
  }

  // Add search filter
  if (q) {
    filters.$or = [
      { package_name: { $ilike: `%${q}%` } },
      { description: { $ilike: `%${q}%` } }
    ];
  }

  // Build order configuration
  let orderConfig: any = {};
  if (order) {
    const isDescending = order.startsWith("-");
    const orderField = isDescending ? order.slice(1) : order;
    orderConfig[orderField] = isDescending ? "DESC" : "ASC";
  }

  const {
    data: packageLookups,
    metadata: { count, take, skip },
  } = await query.graph({
    entity: "package_lookups",
    fields: ["*"],
    filters,
    order: Object.keys(orderConfig).length > 0 ? orderConfig : undefined,
    pagination: {
      skip: Number(offset),
      take: Number(limit),
    },
  });

  res.json({
    package_lookups: packageLookups,
    count,
    limit: take,
    offset: skip,
  });
};

// POST /admin/package-lookups
export const POST = async (
  req: MedusaRequest<PostAdminCreatePackageLookupType>,
  res: MedusaResponse
) => {
  try {
    const validatedData = PostAdminCreatePackageLookup.parse(req.body);

    const { result } = await CreatePackageLookupWorkflow(req.scope).run({
      input: validatedData,
    });

    res.json({ package_lookup: result });
  } catch (error) {
    console.error("Create package lookup API error:", error);
    
    if (error.message?.includes("unique constraint")) {
      return res.status(409).json({
        message: "A package lookup with this name already exists",
      });
    }

    return res.status(400).json({
      message: error.message || "Failed to create package lookup",
    });
  }
};

// PUT /admin/package-lookups
export const PUT = async (
  req: MedusaRequest<PostAdminUpdatePackageLookupType>,
  res: MedusaResponse
) => {
  try {
    const validatedData = PostAdminUpdatePackageLookup.parse(req.body);

    const { result } = await UpdatePackageLookupWorkflow(req.scope).run({
      input: {
        id: validatedData.id,
        data: {
          package_name: validatedData.package_name,
          description: validatedData.description,
          is_active: validatedData.is_active,
        },
      },
    });

    res.json({ package_lookup: result });
  } catch (error) {
    console.error("Update package lookup API error:", error);

    if (error.message?.includes("not found")) {
      return res.status(404).json({
        message: "Package lookup not found",
      });
    }

    if (error.message?.includes("unique constraint")) {
      return res.status(409).json({
        message: "A package lookup with this name already exists",
      });
    }

    return res.status(400).json({
      message: error.message || "Failed to update package lookup",
    });
  }
};

// DELETE /admin/package-lookups
export const DELETE = async (
  req: MedusaRequest<PostAdminDeletePackageLookupType>,
  res: MedusaResponse
) => {
  try {
    const validatedData = PostAdminDeletePackageLookup.parse(req.body);

    await DeletePackageLookupWorkflow(req.scope).run({
      input: validatedData,
    });

    res.json({ success: true });
  } catch (error) {
    console.error("Delete package lookup API error:", error);
    return res.status(400).json({
      message: error.message || "Failed to delete package lookup",
    });
  }
};
