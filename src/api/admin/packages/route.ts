import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { createPackageWorkflow } from "../../../workflows/package-management/create-package";
import { PACKAGE_MANAGEMENT_MODULE } from "../../../modules/package-management";

// Validation schemas
const PostAdminCreatePackage = z.object({
  package_lookup_id: z.string(),
  description: z.string(),
  valid_from_date: z.string(),
  valid_to_date: z.string(),
  destination_ids: z.array(z.string()),
});

const PostAdminUpdatePackage = z.object({
  id: z.string(),
  package_lookup_id: z.string().optional(),
  description: z.string().optional(),
  valid_from: z.string().optional(),
  valid_to: z.string().optional(),
  destination_ids: z.array(z.string()).optional(),
});

const PostAdminDeletePackage = z.object({
  id: z.string(),
});

type PostAdminCreatePackageType = z.infer<typeof PostAdminCreatePackage>;
type PostAdminUpdatePackageType = z.infer<typeof PostAdminUpdatePackage>;
type PostAdminDeletePackageType = z.infer<typeof PostAdminDeletePackage>;

// GET /admin/packages
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const { limit = 20, offset = 0, q, destinations, order } = req.query || {};

  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Build filters
    const filters: any = {};

    // Add search filter - we'll handle this with a more complex query
    if (q) {
      // For now, let's fetch all packages and filter client-side
      // This is a temporary solution until we can properly implement the join search
    }

    // Add destination filter
    if (destinations) {
      // We need to filter packages that have the specified destination
      // This will require joining with package_destinations table
      filters.package_destinations = {
        destination_id: destinations
      };
    }

    // Build order configuration
    let orderConfig: any = {};
    if (order && typeof order === "string") {
      const isDescending = order.startsWith("-");
      const orderField = isDescending ? order.slice(1) : order;
      orderConfig[orderField] = isDescending ? "DESC" : "ASC";
    }

    // Fetch packages with their destinations
    const {
      data: packages,
      metadata: { count },
    } = await query.graph({
      entity: "packages",
      fields: ["*"],
      filters,
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    // Fetch package destinations separately
    const packageIds = packages.map((pkg: any) => pkg.id);
    let packageDestinations: any[] = [];
    if (packageIds.length > 0) {
      const { data: destinations } = await query.graph({
        entity: "package_destinations",
        fields: ["package_id", "destination_id"],
        filters: {
          package_id: packageIds,
        },
      });
      packageDestinations = destinations;
    }

    // Fetch package lookups separately
    const packageLookupIds = packages.map((pkg: any) => pkg.package_lookup_id);
    let packageLookups: any[] = [];
    if (packageLookupIds.length > 0) {
      const { data: lookups } = await query.graph({
        entity: "package_lookups",
        fields: ["id", "package_name"],
        filters: {
          id: packageLookupIds,
        },
      });
      packageLookups = lookups;
    }

    // Create lookup maps for efficient data joining
    const destinationMap = new Map();
    packageDestinations.forEach((dest: any) => {
      if (!destinationMap.has(dest.package_id)) {
        destinationMap.set(dest.package_id, []);
      }
      destinationMap.get(dest.package_id).push(dest.destination_id);
    });

    const lookupMap = new Map();
    packageLookups.forEach((lookup: any) => {
      lookupMap.set(lookup.id, lookup.package_name);
    });

    // Transform packages to include destination names and package lookup names
    let transformedPackages = packages.map((pkg: any) => {
      const destinationIds = destinationMap.get(pkg.id) || [];
      const packageLookupName = lookupMap.get(pkg.package_lookup_id);

      return {
        id: pkg.id,
        package_lookup_id: pkg.package_lookup_id,
        package_lookup_name: packageLookupName || null,
        description: pkg.description,
        valid_from: pkg.valid_from_date,
        valid_to: pkg.valid_to_date,
        destinations: destinationIds,
        created_at: pkg.created_at,
        updated_at: pkg.updated_at,
      };
    });

    // Apply search filter if provided (client-side filtering for now)
    if (q && typeof q === "string") {
      const searchTerm = q.toLowerCase();
      transformedPackages = transformedPackages.filter((pkg: any) =>
        pkg.description?.toLowerCase().includes(searchTerm) ||
        pkg.package_lookup_name?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply sorting if provided
    if (order && typeof order === "string") {
      const isDescending = order.startsWith("-");
      const sortKey = isDescending ? order.slice(1) : order;

      transformedPackages.sort((a: any, b: any) => {
        let aValue = a[sortKey];
        let bValue = b[sortKey];

        // Handle package lookup name sorting
        if (sortKey === "package_lookup_id") {
          aValue = a.package_lookup_name || "";
          bValue = b.package_lookup_name || "";
        }

        // Handle date sorting
        if (sortKey === "valid_from" || sortKey === "valid_to") {
          aValue = new Date(aValue).getTime();
          bValue = new Date(bValue).getTime();
        } else if (typeof aValue === "string" && typeof bValue === "string") {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return isDescending ? 1 : -1;
        if (aValue > bValue) return isDescending ? -1 : 1;
        return 0;
      });
    }

    res.json({
      packages: transformedPackages,
      count,
      limit: Number(limit),
      offset: Number(offset),
    });
  } catch (error) {
    console.error("Error fetching packages:", error);
    res.status(500).json({
      message: "Failed to fetch packages",
      error: error.message,
    });
  }
};

// POST /admin/packages
export const POST = async (
  req: MedusaRequest<PostAdminCreatePackageType>,
  res: MedusaResponse
) => {
  try {
    console.log("📥 Received package creation request:", req.body);

    const validatedData = PostAdminCreatePackage.parse(req.body);
    console.log("✅ Validated data:", validatedData);

    // Check if package management module is available
    try {
      const packageManagementService: any = req.scope.resolve(PACKAGE_MANAGEMENT_MODULE);
      console.log("✅ Package management service resolved");
    } catch (moduleError) {
      console.error("❌ Package management module not available:", moduleError);
      return res.status(500).json({
        message: "Package management module not available",
        error: moduleError.message,
      });
    }

    // Execute the package creation workflow
    console.log("🚀 Executing package creation workflow...");
    const { result } = await createPackageWorkflow(req.scope).run({
      input: {
        package_lookup_id: validatedData.package_lookup_id,
        description: validatedData.description,
        valid_from_date: validatedData.valid_from_date,
        valid_to_date: validatedData.valid_to_date,
        destination_ids: validatedData.destination_ids,
      },
    });

    console.log("✅ Package creation workflow completed:", result);

    res.status(201).json({
      package: (result as any).package,
      destinations: (result as any).destinations,
      message: "Package created successfully",
    });
  } catch (error) {
    console.error("❌ Create package API error:", error);
    return res.status(400).json({
      message: error.message || "Failed to create package",
      error: error.stack,
    });
  }
};

// PUT /admin/packages
export const PUT = async (
  req: MedusaRequest<PostAdminUpdatePackageType>,
  res: MedusaResponse
) => {
  try {
    const validatedData = PostAdminUpdatePackage.parse(req.body);
    
    // TODO: Implement package update workflow
    // For now, return a placeholder response
    res.status(501).json({
      message: "Package update not yet implemented",
    });
  } catch (error) {
    console.error("Update package API error:", error);
    return res.status(400).json({
      message: error.message || "Failed to update package",
    });
  }
};

// DELETE /admin/packages
export const DELETE = async (
  req: MedusaRequest<PostAdminDeletePackageType>,
  res: MedusaResponse
) => {
  try {
    const validatedData = PostAdminDeletePackage.parse(req.body);
    
    // TODO: Implement package deletion workflow
    // For now, return a placeholder response
    res.status(501).json({
      message: "Package deletion not yet implemented",
    });
  } catch (error) {
    console.error("Delete package API error:", error);
    return res.status(400).json({
      message: error.message || "Failed to delete package",
    });
  }
};
