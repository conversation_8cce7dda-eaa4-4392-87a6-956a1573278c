import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { DeletePackageWorkflow } from "src/workflows/package-management/delete-package";
import { UpdatePackageWorkflow } from "src/workflows/package-management/update-package";

// GET /admin/packages/:id
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const { id } = req.params;

    const { data: packages } = await query.graph({
      entity: "packages",
      filters: {
        id: [id],
      },
      fields: ["*"],
    });

    if (!packages || packages.length === 0) {
      return res.status(404).json({
        message: "Package not found",
      });
    }

    const pkg = packages[0];

    // Fetch package destinations separately
    const { data: packageDestinations } = await query.graph({
      entity: "package_destinations",
      filters: {
        package_id: [id],
      },
      fields: ["destination_id"],
    });

    // Transform the data to match the expected format
    const transformedPackage = {
      id: pkg.id,
      package_lookup_id: pkg.package_lookup_id,
      description: pkg.description,
      valid_from: pkg.valid_from_date,
      valid_to: pkg.valid_to_date,
      destinations: packageDestinations?.map((pd: any) => pd.destination_id) || [],
      created_at: pkg.created_at,
      updated_at: pkg.updated_at,
    };

    res.json({ package: transformedPackage });
  } catch (error) {
    console.error("Get package API error:", error);
    return res.status(500).json({
      message: "Failed to retrieve package",
    });
  }
};

// DELETE /admin/packages/:id
export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;

    console.log(`📥 Received package deletion request for ID: ${id}`);

    if (!id) {
      return res.status(400).json({
        message: "Package ID is required",
      });
    }

    // Execute the delete package workflow
    const { result } = await DeletePackageWorkflow(req.scope).run({
      input: { id },
    });

    console.log(`✅ Package deletion completed:`, result);

    res.json({
      id,
      deleted: true,
      message: "Package deleted successfully",
    });
  } catch (error) {
    console.error("Delete package API error:", error);

    if (error.message?.includes("not found")) {
      return res.status(404).json({
        message: "Package not found",
      });
    }

    return res.status(500).json({
      message: error.message || "Failed to delete package",
    });
  }
};

// PUT /admin/packages/:id
export const PUT = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { id } = req.params;
    const { package_lookup_id, description, valid_from, valid_to, destination_ids } = req.body;

    console.log(`📥 Received package update request for ID: ${id}`, req.body);

    if (!id) {
      return res.status(400).json({
        message: "Package ID is required",
      });
    }

    // Execute the update package workflow
    const { result } = await UpdatePackageWorkflow(req.scope).run({
      input: {
        id,
        package_lookup_id,
        description,
        valid_from,
        valid_to,
        destination_ids
      },
    });

    console.log(`✅ Package update completed:`, result);

    res.json({
      id,
      updated: true,
      message: "Package updated successfully",
      package: result.package,
    });
  } catch (error) {
    console.error("Update package API error:", error);

    if (error.message?.includes("not found")) {
      return res.status(404).json({
        message: "Package not found",
      });
    }

    return res.status(500).json({
      message: error.message || "Failed to update package",
    });
  }
};
